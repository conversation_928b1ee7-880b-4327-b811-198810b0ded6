name: Deploy Book
on:
  push:
    branches: [main]
    paths: [book/**]

jobs:
  deploy-book:
    runs-on: ubuntu-latest

    permissions:
      contents: write
      pages: write
      id-token: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Rust
        uses: actions-rust-lang/setup-rust-toolchain@v1

      - name: Install mdbook
        env:
          GH_TOKEN: ${{ github.token }}
        run: |
          jq_expr='.assets[] | select(.name | contains("x86_64-unknown-linux-gnu")) | .browser_download_url'
          url=$(gh api repos/rust-lang/mdbook/releases/latest --jq "$jq_expr")
          mkdir mdbook
          curl -sSL "$url" | tar -xz -C mdbook
          printf '%s/mdbook\n' "$PWD" >> "$GITHUB_PATH"
      - name: Install mdbook-admonish
        run: cargo install mdbook-admonish

      - name: Install mdbook-mermaid
        run: cargo install mdbook-mermaid

      - name: Build Book
        run: mdbook build book

      - name: Setup Pages
        uses: actions/configure-pages@v5

      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: book/book

      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
