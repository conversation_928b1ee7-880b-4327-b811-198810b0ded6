{
	// Use IntelliSense to learn about possible attributes.
	// Hover to view descriptions of existing attributes.
	// For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
	"version": "0.2.0",
	"configurations": [
		{
			"type": "extensionHost",
			"request": "launch",
			"name": "Run Python WASI LSP",
			"runtimeExecutable": "${execPath}/examples/vscode-wasi",
			"args": [
				"--extensionDevelopmentPath=${workspaceFolder}/examples/vscode-wasi",
				"${workspaceFolder}/examples/vscode-wasi/test"
			],
			"outFiles": [
				"${workspaceFolder}/examples/vscode-wasi/client/out/**/*.js"
			],
			"autoAttachChildProcesses": true,
			"preLaunchTask": "npm: build wasi"
		},
		{
			"type": "extensionHost",
			"request": "launch",
			"name": "Run Python Native LSP",
			"runtimeExecutable": "${execPath}/examples/vscode-native",
			"args": [
				"--extensionDevelopmentPath=${workspaceFolder}/examples/vscode-native",
				"${workspaceFolder}/examples/vscode-native/test"
			],
			"outFiles": [
				"${workspaceFolder}/examples/vscode-native/client/out/**/*.js"
			],
			"autoAttachChildProcesses": true,
			"preLaunchTask": "npm: build native"
		}
	]
}