{"typescript.tsserver.experimental.enableProjectDiagnostics": true, "editor.formatOnSave": true, "[typescript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "git.branchProtection": ["main"], "rust-analyzer.linkedProjects": ["Cargo.toml", "examples/vscode-wasi/server/Cargo.toml", "examples/vscode-native/server/Cargo.toml", "examples/native/Cargo.toml", "examples/ast-python/Cargo.toml", "examples/ast-html/Cargo.toml", "crates/codegen/Cargo.toml"]}