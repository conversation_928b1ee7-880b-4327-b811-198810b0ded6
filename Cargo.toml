[package]
name = "auto-lsp"
authors = ["<PERSON><PERSON><PERSON>"]
license = "GPL-3.0"
repository = "https://github.com/adclz/auto-lsp"
description = """
A rust crate for creating AST and LSP servers powered by tree-sitter.
"""
categories = ["parsing"]
keywords = ["parser", "tree-sitter", "lsp"]
version = "0.5.1"
edition = "2021"
readme = "README.MD"

[workspace]
members = ["crates/core", "crates/codegen"]
exclude = [
    "examples/vscode-wasi/server",
    "examples/vscode-native/server",
    "examples/native",
    "examples/ast-python",
    "examples/ast-html",
]

[features]
default = []
lsp_server = [
    "dep:serde",
    "dep:serde_json",
    "dep:walkdir",
    "dep:lsp-server",
    "dep:crossbeam-channel",
]
wasm = ["parking_lot/nightly", "auto-lsp-core/wasm"]

[workspace.dependencies]
texter = { version = "0.2.0", default-features = false }
lsp-types = "0.94.1"
tree-sitter = "0.25.4"
downcast-rs = "2.0.1"
streaming-iterator = "0.1.9"
parking_lot = "0.12.3"
log = "0.4.22"
rayon = "1.10"
anyhow = "1.0.97"
regex = "1.11.1"
rstest = "0.25.0"
salsa = "0.19.0"
dashmap = "6.1.0"
thiserror = "2.0.12"
ariadne = "0.5.1"
fastrace = "0.7"

[dependencies]
auto-lsp-core = { path = "./crates/core", version = "0.5.0" }
rayon-par-bridge = "0.1.0"
tree-sitter-language = "0.1.5"
regex = "1.11.1"
fastrace = { workspace = true }
tree-sitter = { workspace = true }
lsp-types = { workspace = true }
streaming-iterator = { workspace = true }
anyhow = { workspace = true }
parking_lot = { workspace = true }
log = { workspace = true }
salsa = { workspace = true }
rayon = { workspace = true }
texter = { workspace = true, default-features = false }
lsp-server = { version = "0.7.8", optional = true }
serde = { version = "1.0.219", optional = true }
serde_json = { version = "1.0.140", optional = true }
walkdir = { version = "2.5.0", optional = true }
crossbeam-channel = { version = "0.5.14", optional = true }

[package.metadata.docs.rs]
features = ["lsp_server"]

[dev-dependencies]
tempfile = "3.19.0"
