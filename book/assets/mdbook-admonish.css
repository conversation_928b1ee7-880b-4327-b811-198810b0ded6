@charset "UTF-8";
:is(.admonition) {
    display: flow-root;
    margin: 1.5625em 0;
    padding: 0 1.2rem;
    color: var(--fg);
    page-break-inside: avoid;
    background-color: var(--bg);
    border: 0 solid black;
    border-inline-start-width: 0.4rem;
    border-radius: 0.2rem;
    box-shadow:
        0 0.2rem 1rem rgba(0, 0, 0, 0.05),
        0 0 0.1rem rgba(0, 0, 0, 0.1);
}
@media print {
    :is(.admonition) {
        box-shadow: none;
    }
}
:is(.admonition) > * {
    box-sizing: border-box;
}
:is(.admonition) :is(.admonition) {
    margin-top: 1em;
    margin-bottom: 1em;
}
:is(.admonition) > .tabbed-set:only-child {
    margin-top: 0;
}
html :is(.admonition) > :last-child {
    margin-bottom: 1.2rem;
}

a.admonition-anchor-link {
    display: none;
    position: absolute;
    left: -1.2rem;
    padding-right: 1rem;
}
a.admonition-anchor-link:link,
a.admonition-anchor-link:visited {
    color: var(--fg);
}
a.admonition-anchor-link:link:hover,
a.admonition-anchor-link:visited:hover {
    text-decoration: none;
}
a.admonition-anchor-link::before {
    content: "§";
}

:is(.admonition-title, summary.admonition-title) {
    position: relative;
    min-height: 4rem;
    margin-block: 0;
    margin-inline: -1.6rem -1.2rem;
    padding-block: 0.8rem;
    padding-inline: 4.4rem 1.2rem;
    font-weight: 700;
    background-color: rgba(68, 138, 255, 0.1);
    print-color-adjust: exact;
    -webkit-print-color-adjust: exact;
    display: flex;
}
:is(.admonition-title, summary.admonition-title) p {
    margin: 0;
}
html :is(.admonition-title, summary.admonition-title):last-child {
    margin-bottom: 0;
}
:is(.admonition-title, summary.admonition-title)::before {
    position: absolute;
    top: 0.625em;
    inset-inline-start: 1.6rem;
    width: 2rem;
    height: 2rem;
    background-color: #448aff;
    print-color-adjust: exact;
    -webkit-print-color-adjust: exact;
    mask-image: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"></svg>');
    -webkit-mask-image: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"></svg>');
    mask-repeat: no-repeat;
    -webkit-mask-repeat: no-repeat;
    mask-size: contain;
    -webkit-mask-size: contain;
    content: "";
}
:is(.admonition-title, summary.admonition-title):hover
    a.admonition-anchor-link {
    display: initial;
}

details.admonition > summary.admonition-title::after {
    position: absolute;
    top: 0.625em;
    inset-inline-end: 1.6rem;
    height: 2rem;
    width: 2rem;
    background-color: currentcolor;
    mask-image: var(--md-details-icon);
    -webkit-mask-image: var(--md-details-icon);
    mask-repeat: no-repeat;
    -webkit-mask-repeat: no-repeat;
    mask-size: contain;
    -webkit-mask-size: contain;
    content: "";
    transform: rotate(0deg);
    transition: transform 0.25s;
}
details[open].admonition > summary.admonition-title::after {
    transform: rotate(90deg);
}
summary.admonition-title::-webkit-details-marker {
    display: none;
}

:root {
    --md-details-icon: url("data:image/svg+xml;charset=utf-8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='M8.59 16.58 13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.42Z'/></svg>");
}

:root {
    --md-admonition-icon--admonish-note: url("data:image/svg+xml;charset=utf-8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='M20.71 7.04c.39-.39.39-1.04 0-1.41l-2.34-2.34c-.37-.39-1.02-.39-1.41 0l-1.84 1.83 3.75 3.75M3 17.25V21h3.75L17.81 9.93l-3.75-3.75L3 17.25z'/></svg>");
    --md-admonition-icon--admonish-abstract: url("data:image/svg+xml;charset=utf-8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='M17 9H7V7h10m0 6H7v-2h10m-3 6H7v-2h7M12 3a1 1 0 0 1 1 1 1 1 0 0 1-1 1 1 1 0 0 1-1-1 1 1 0 0 1 1-1m7 0h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2z'/></svg>");
    --md-admonition-icon--admonish-info: url("data:image/svg+xml;charset=utf-8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='M13 9h-2V7h2m0 10h-2v-6h2m-1-9A10 10 0 0 0 2 12a10 10 0 0 0 10 10 10 10 0 0 0 10-10A10 10 0 0 0 12 2z'/></svg>");
    --md-admonition-icon--admonish-tip: url("data:image/svg+xml;charset=utf-8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='M17.66 11.2c-.23-.3-.51-.56-.77-.82-.67-.6-1.43-1.03-2.07-1.66C13.33 7.26 13 4.85 13.95 3c-.95.23-1.78.75-2.49 1.32-2.59 2.08-3.61 5.75-2.39 ********.********** 0 .22-.15.42-.35.5-.23.1-.47.04-.66-.12a.58.58 0 0 1-.14-.17c-1.13-1.43-1.31-3.48-.55-5.12C5.78 10 4.87 12.3 5 14.47c.06.5.12 1 .29 ********.41 1.2.71 1.73 1.08 1.73 2.95 2.97 4.96 3.22 2.14.27 4.43-.12 6.07-1.6 1.83-1.66 2.47-4.32 1.53-6.6l-.13-.26c-.21-.46-.77-1.26-.77-1.26m-3.16 6.3c-.28.24-.74.5-1.1.6-1.12.4-2.24-.16-2.9-.82 1.19-.28 1.9-1.16 2.11-2.05.17-.8-.15-1.46-.28-2.23-.12-.74-.1-1.37.17-**********.39.76.63 1.06.77 1 1.98 1.44 2.24 *********.***********.03.82-.33 1.72-.93 2.27z'/></svg>");
    --md-admonition-icon--admonish-success: url("data:image/svg+xml;charset=utf-8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='m9 20.42-6.21-6.21 2.83-2.83L9 14.77l9.88-9.89 2.83 2.83L9 20.42z'/></svg>");
    --md-admonition-icon--admonish-question: url("data:image/svg+xml;charset=utf-8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='m15.07 11.25-.9.92C13.45 12.89 13 13.5 13 15h-2v-.5c0-1.11.45-2.11 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41a2 2 0 0 0-2-2 2 2 0 0 0-2 2H8a4 4 0 0 1 4-4 4 4 0 0 1 4 4 3.2 3.2 0 0 1-.93 2.25M13 19h-2v-2h2M12 2A10 10 0 0 0 2 12a10 10 0 0 0 10 10 10 10 0 0 0 10-10c0-5.53-4.5-10-10-10z'/></svg>");
    --md-admonition-icon--admonish-warning: url("data:image/svg+xml;charset=utf-8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='M13 14h-2V9h2m0 9h-2v-2h2M1 21h22L12 2 1 21z'/></svg>");
    --md-admonition-icon--admonish-failure: url("data:image/svg+xml;charset=utf-8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='M20 6.91 17.09 4 12 9.09 6.91 4 4 6.91 9.09 12 4 17.09 6.91 20 12 14.91 17.09 20 20 17.09 14.91 12 20 6.91z'/></svg>");
    --md-admonition-icon--admonish-danger: url("data:image/svg+xml;charset=utf-8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='M11 15H6l7-14v8h5l-7 14v-8z'/></svg>");
    --md-admonition-icon--admonish-bug: url("data:image/svg+xml;charset=utf-8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='M14 12h-4v-2h4m0 6h-4v-2h4m6-6h-2.81a5.985 5.985 0 0 0-1.82-1.96L17 4.41 15.59 3l-2.17 2.17a6.002 6.002 0 0 0-2.83 0L8.41 3 7 4.41l1.62 1.63C7.88 6.55 7.26 7.22 6.81 8H4v2h2.09c-.05.33-.09.66-.09 1v1H4v2h2v1c0 .34.04.67.09 1H4v2h2.81c1.04 1.79 2.97 3 5.19 3s4.15-1.21 5.19-3H20v-2h-2.09c.05-.33.09-.66.09-1v-1h2v-2h-2v-1c0-.34-.04-.67-.09-1H20V8z'/></svg>");
    --md-admonition-icon--admonish-example: url("data:image/svg+xml;charset=utf-8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='M7 13v-2h14v2H7m0 6v-2h14v2H7M7 7V5h14v2H7M3 8V5H2V4h2v4H3m-1 9v-1h3v4H2v-1h2v-.5H3v-1h1V17H2m2.25-7a.75.75 0 0 1 .75.75c0 .2-.08.39-.21.52L3.12 13H5v1H2v-.92L4 11H2v-1h2.25z'/></svg>");
    --md-admonition-icon--admonish-quote: url("data:image/svg+xml;charset=utf-8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='M14 17h3l2-4V7h-6v6h3M6 17h3l2-4V7H5v6h3l-2 4z'/></svg>");
}

:is(.admonition):is(.admonish-note) {
    border-color: #448aff;
}

:is(.admonish-note) > :is(.admonition-title, summary.admonition-title) {
    background-color: rgba(68, 138, 255, 0.1);
}
:is(.admonish-note) > :is(.admonition-title, summary.admonition-title)::before {
    background-color: #448aff;
    mask-image: var(--md-admonition-icon--admonish-note);
    -webkit-mask-image: var(--md-admonition-icon--admonish-note);
    mask-repeat: no-repeat;
    -webkit-mask-repeat: no-repeat;
    mask-size: contain;
    -webkit-mask-repeat: no-repeat;
}

:is(.admonition):is(.admonish-abstract, .admonish-summary, .admonish-tldr) {
    border-color: #00b0ff;
}

:is(.admonish-abstract, .admonish-summary, .admonish-tldr)
    > :is(.admonition-title, summary.admonition-title) {
    background-color: rgba(0, 176, 255, 0.1);
}
:is(.admonish-abstract, .admonish-summary, .admonish-tldr)
    > :is(.admonition-title, summary.admonition-title)::before {
    background-color: #00b0ff;
    mask-image: var(--md-admonition-icon--admonish-abstract);
    -webkit-mask-image: var(--md-admonition-icon--admonish-abstract);
    mask-repeat: no-repeat;
    -webkit-mask-repeat: no-repeat;
    mask-size: contain;
    -webkit-mask-repeat: no-repeat;
}

:is(.admonition):is(.admonish-info, .admonish-todo) {
    border-color: #00b8d4;
}

:is(.admonish-info, .admonish-todo)
    > :is(.admonition-title, summary.admonition-title) {
    background-color: rgba(0, 184, 212, 0.1);
}
:is(.admonish-info, .admonish-todo)
    > :is(.admonition-title, summary.admonition-title)::before {
    background-color: #00b8d4;
    mask-image: var(--md-admonition-icon--admonish-info);
    -webkit-mask-image: var(--md-admonition-icon--admonish-info);
    mask-repeat: no-repeat;
    -webkit-mask-repeat: no-repeat;
    mask-size: contain;
    -webkit-mask-repeat: no-repeat;
}

:is(.admonition):is(.admonish-tip, .admonish-hint, .admonish-important) {
    border-color: #00bfa5;
}

:is(.admonish-tip, .admonish-hint, .admonish-important)
    > :is(.admonition-title, summary.admonition-title) {
    background-color: rgba(0, 191, 165, 0.1);
}
:is(.admonish-tip, .admonish-hint, .admonish-important)
    > :is(.admonition-title, summary.admonition-title)::before {
    background-color: #00bfa5;
    mask-image: var(--md-admonition-icon--admonish-tip);
    -webkit-mask-image: var(--md-admonition-icon--admonish-tip);
    mask-repeat: no-repeat;
    -webkit-mask-repeat: no-repeat;
    mask-size: contain;
    -webkit-mask-repeat: no-repeat;
}

:is(.admonition):is(.admonish-success, .admonish-check, .admonish-done) {
    border-color: #00c853;
}

:is(.admonish-success, .admonish-check, .admonish-done)
    > :is(.admonition-title, summary.admonition-title) {
    background-color: rgba(0, 200, 83, 0.1);
}
:is(.admonish-success, .admonish-check, .admonish-done)
    > :is(.admonition-title, summary.admonition-title)::before {
    background-color: #00c853;
    mask-image: var(--md-admonition-icon--admonish-success);
    -webkit-mask-image: var(--md-admonition-icon--admonish-success);
    mask-repeat: no-repeat;
    -webkit-mask-repeat: no-repeat;
    mask-size: contain;
    -webkit-mask-repeat: no-repeat;
}

:is(.admonition):is(.admonish-question, .admonish-help, .admonish-faq) {
    border-color: #64dd17;
}

:is(.admonish-question, .admonish-help, .admonish-faq)
    > :is(.admonition-title, summary.admonition-title) {
    background-color: rgba(100, 221, 23, 0.1);
}
:is(.admonish-question, .admonish-help, .admonish-faq)
    > :is(.admonition-title, summary.admonition-title)::before {
    background-color: #64dd17;
    mask-image: var(--md-admonition-icon--admonish-question);
    -webkit-mask-image: var(--md-admonition-icon--admonish-question);
    mask-repeat: no-repeat;
    -webkit-mask-repeat: no-repeat;
    mask-size: contain;
    -webkit-mask-repeat: no-repeat;
}

:is(.admonition):is(.admonish-warning, .admonish-caution, .admonish-attention) {
    border-color: #ff9100;
}

:is(.admonish-warning, .admonish-caution, .admonish-attention)
    > :is(.admonition-title, summary.admonition-title) {
    background-color: rgba(255, 145, 0, 0.1);
}
:is(.admonish-warning, .admonish-caution, .admonish-attention)
    > :is(.admonition-title, summary.admonition-title)::before {
    background-color: #ff9100;
    mask-image: var(--md-admonition-icon--admonish-warning);
    -webkit-mask-image: var(--md-admonition-icon--admonish-warning);
    mask-repeat: no-repeat;
    -webkit-mask-repeat: no-repeat;
    mask-size: contain;
    -webkit-mask-repeat: no-repeat;
}

:is(.admonition):is(.admonish-failure, .admonish-fail, .admonish-missing) {
    border-color: #ff5252;
}

:is(.admonish-failure, .admonish-fail, .admonish-missing)
    > :is(.admonition-title, summary.admonition-title) {
    background-color: rgba(255, 82, 82, 0.1);
}
:is(.admonish-failure, .admonish-fail, .admonish-missing)
    > :is(.admonition-title, summary.admonition-title)::before {
    background-color: #ff5252;
    mask-image: var(--md-admonition-icon--admonish-failure);
    -webkit-mask-image: var(--md-admonition-icon--admonish-failure);
    mask-repeat: no-repeat;
    -webkit-mask-repeat: no-repeat;
    mask-size: contain;
    -webkit-mask-repeat: no-repeat;
}

:is(.admonition):is(.admonish-danger, .admonish-error) {
    border-color: #ff1744;
}

:is(.admonish-danger, .admonish-error)
    > :is(.admonition-title, summary.admonition-title) {
    background-color: rgba(255, 23, 68, 0.1);
}
:is(.admonish-danger, .admonish-error)
    > :is(.admonition-title, summary.admonition-title)::before {
    background-color: #ff1744;
    mask-image: var(--md-admonition-icon--admonish-danger);
    -webkit-mask-image: var(--md-admonition-icon--admonish-danger);
    mask-repeat: no-repeat;
    -webkit-mask-repeat: no-repeat;
    mask-size: contain;
    -webkit-mask-repeat: no-repeat;
}

:is(.admonition):is(.admonish-bug) {
    border-color: #f50057;
}

:is(.admonish-bug) > :is(.admonition-title, summary.admonition-title) {
    background-color: rgba(245, 0, 87, 0.1);
}
:is(.admonish-bug) > :is(.admonition-title, summary.admonition-title)::before {
    background-color: #f50057;
    mask-image: var(--md-admonition-icon--admonish-bug);
    -webkit-mask-image: var(--md-admonition-icon--admonish-bug);
    mask-repeat: no-repeat;
    -webkit-mask-repeat: no-repeat;
    mask-size: contain;
    -webkit-mask-repeat: no-repeat;
}

:is(.admonition):is(.admonish-example) {
    border-color: #7c4dff;
}

:is(.admonish-example) > :is(.admonition-title, summary.admonition-title) {
    background-color: rgba(124, 77, 255, 0.1);
}
:is(.admonish-example)
    > :is(.admonition-title, summary.admonition-title)::before {
    background-color: #7c4dff;
    mask-image: var(--md-admonition-icon--admonish-example);
    -webkit-mask-image: var(--md-admonition-icon--admonish-example);
    mask-repeat: no-repeat;
    -webkit-mask-repeat: no-repeat;
    mask-size: contain;
    -webkit-mask-repeat: no-repeat;
}

:is(.admonition):is(.admonish-quote, .admonish-cite) {
    border-color: #9e9e9e;
}

:is(.admonish-quote, .admonish-cite)
    > :is(.admonition-title, summary.admonition-title) {
    background-color: rgba(158, 158, 158, 0.1);
}
:is(.admonish-quote, .admonish-cite)
    > :is(.admonition-title, summary.admonition-title)::before {
    background-color: #9e9e9e;
    mask-image: var(--md-admonition-icon--admonish-quote);
    -webkit-mask-image: var(--md-admonition-icon--admonish-quote);
    mask-repeat: no-repeat;
    -webkit-mask-repeat: no-repeat;
    mask-size: contain;
    -webkit-mask-repeat: no-repeat;
}

.navy :is(.admonition) {
    background-color: var(--sidebar-bg);
}

.ayu :is(.admonition),
.coal :is(.admonition) {
    background-color: var(--theme-hover);
}

.rust :is(.admonition) {
    background-color: var(--sidebar-bg);
    color: var(--sidebar-fg);
}
.rust .admonition-anchor-link:link,
.rust .admonition-anchor-link:visited {
    color: var(--sidebar-fg);
}
