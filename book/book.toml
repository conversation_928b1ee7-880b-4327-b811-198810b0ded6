[book]
authors = ["<PERSON><PERSON><PERSON>"]
language = "en"
multilingual = false
src = "src"
title = "Auto-LSP"

[output.html]
git-repository-url = "https://github.com/adclz/auto-lsp"
git-repository-icon = "fa-github"
additional-css = ["assets/mdbook-admonish.css"]
additional-js = ["assets/mermaid.min.js", "assets/mermaid-init.js"]

[output.html.search]
limit-results = 20
use-boolean-and = true
boost-title = 2
boost-hierarchy = 2
boost-paragraph = 1
expand = true

[preprocessor.admonish]
command = "mdbook-admonish"
assets_version = "3.0.2"    # do not edit: managed by `mdbook-admonish install`

[preprocessor.mermaid]
command = "mdbook-mermaid"
