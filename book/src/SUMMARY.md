# Summary

[Introduction](./index.md)
[Architecture](./ARCHITECTURE.md)

 - [Generating an AST](generating-an-ast/index.md)
   - [AstNode trait](generating-an-ast/ast-node-trait.md)
   - [ParsedAst struct](generating-an-ast/parsed-ast-struct.md)
   - [Errors](generating-an-ast/errors.md)
 - [DataBase](database/index.md)
   - [Document](database/document.md)
   - [Configuring Parsers](database/configuring-parsers.md)
 - [LSP server](lsp-server/index.md)
   - [Configuring a server](lsp-server/configuring-a-server.md)
   - [Configuring Semantic <PERSON>](lsp-server/configuring-semantic-tokens.md)
   - [Handlers](lsp-server/handlers.md) 
   - [Configuring a client](lsp-server/configuring-a-client.md)
 - [Patterns]()
   - [Dispatch](patterns/dispatch.md)
   - [Tree-sitter queries](patterns/tree-sitter-queries.md)   
 - [Tests]()
   - [Logging and Tracing](tests/logging_and_tracing.md)
   - [Snapshots](tests/snapshots.md)
