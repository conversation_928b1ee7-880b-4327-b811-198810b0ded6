# Configuring Parsers

To inform the server about which file extensions are associated with a parser, you need to use the [`configure_parsers!`](https://docs.rs/auto-lsp/latest/auto_lsp/macro.configure_parsers.html) macro.

`configure_parsers!` takes as first argument the name of the list, then each entry is a parser configuration.

A parser requires the following informations:
 - A tree-sitter language fn.
 - The AST root node (often Module, Document, SourceFile nodes ...).

## Example with python

```rust, ignore
configure_parsers!(
    PYTHON_PARSERS,
    "python" => {
        language: tree_sitter_python::LANGUAGE,
        ast_root: ast::generated::Module // generated by auto_lsp_codegen
    }
);
```
