# Custom Database

`auto_lsp` provides default implementations for working with source files and a database:

- `BaseDb`: a basic database implementation
- `File`: an input struct representing a file
- `BaseDatabase`: a trait for file retrieval
- `FileManager` — a trait for file updates.

These are designed to cover common use cases, but you can extend or replace them to fit your needs.

## File struct

The File struct is a `salsa::input` representing a source file, its `Url`, and a reference to its parser configuration.

```rust, ignore
#[salsa::input]
pub struct File {
    #[id]
    pub url: Url,
    pub parsers: &'static Parsers,
    #[return_ref]
    pub document: Arc<Document>,
}
```

## BaseDb struct

`BaseDb` is the default implementation of a database in `auto_lsp`. It stores File inputs and optionally captures logs in debug mode.

```rust
#[salsa::db]
#[derive(Default, Clone)]
pub struct BaseDb {
    storage: Storage<Self>,
    pub(crate) files: DashMap<Url, File>,
    #[cfg(debug_assertions)]
    logs: Arc<parking_lot::Mutex<Vec<String>>>,
}
```

## BaseDatabase trait

The `BaseDatabase` trait defines how to access stored files. It's meant to be implemented by any Salsa-compatible database.

```rust, ignore
#[salsa::db]
pub trait BaseDatabase: Database {
    fn get_files(&self) -> &DashMap<Url, File>;

    fn get_file(&self, url: &Url) -> Option<File> {
        self.get_files().get(url).map(|file| *file)
    }

    #[cfg(debug_assertions)]
    fn take_logs(&self) -> Vec<String>;
}
```

## FileManager trait

The `FileManager` trait provides high-level methods to manage files (add, update, remove). It is implemented for all types that implement `BaseDatabase`.

```rust, ignore
pub trait FileManager: BaseDatabase + salsa::Database {
    fn add_file_from_texter(
        &mut self,
        parsers: &'static Parsers,
        url: &Url,
        texter: Text,
    ) -> Result<(), DataBaseError>;

    fn update(
        &mut self,
        url: &Url,
        changes: &[lsp_types::TextDocumentContentChangeEvent],
    ) -> Result<(), DataBaseError>;

    fn remove_file(&mut self, url: &Url) -> Result<(), DataBaseError>;
}
```