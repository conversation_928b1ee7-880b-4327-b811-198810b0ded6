# Errors

Errors happen when a node is missing for a field that is neither Vec or Option.

If an error happen inside a Vector, it is accumulated rather than returned, so the walk can continue through the tree.

To accumulate errors, we use a salsa accumulator therefore the function that triggers the parsing must be a salsa query.

auto_lsp provides the `get_ast` query that returns a `ParsedAst` struct - which is wrapper for `Vec<Arc<dyn AstNode>>`.

When used with `get_ast::accumulated::<ParseErrorAccumulator>(db, file)`, it will return all the errors that happened during the parsing of the file.

```admonish
One caveat with that approach is that  some errrors might be silent if the accumulator is not used.
```
