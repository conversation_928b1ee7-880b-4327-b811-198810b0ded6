# Parsed Ast struct

The `get_ast` query returns a `ParsedAst` struct which is a wrapper around a `Vec<Arc<dyn AstNode>>`.

The first node of the list is always the root node.

This gives you different ways to perform operations on the AST.

Either by:
 - Downcasting the root node or any other node to a concrete type and accessing its fields.
 - Iterating over the node list, calling methods on it or checking their concrete type.

## Methods

 - `get_root`: Returns the root node.
 - `descendant_at`: Returns the first node that contains the given offset.

### Example: Finding first node containing a position

```rust, ignore
use auto_lsp::core::ast::AstNode;
use std::sync::Arc;

pub fn descendant_at<'a>(list: &'a Vec<Arc<dyn AstNode>>, offset: usize) -> Option<&'a Arc<dyn AstNode>> {
    let mut result = None;
    for node in list.iter() {
        let range = node.get_range();
        if range.start_byte <= offset && offset <= range.end_byte {
            result = Some(node);
        }
    }
    result
}
```

### Example: Filtering nodes by type

```rust, ignore
use std::sync::Arc;
use aut_lsp::downcast_rs::DowncastSync;
use auto_lsp::core::ast::AstNode;
use ast::generated::FunctionDefinition;

pub fn get_functions(list: &Vec<Arc<dyn AstNode>>) -> Vec<&Arc<FunctionDefinition>> {
    list.iter()
        .filter_map(|node| node.is::<FunctionDefinition>())
        .collect()
}
```

To make it more convenient to call methods on a list of nodes, you can use the `dispatch` or `dispatch_once` macro.
See [dispatch](../patterns/dispatch.md) for more information.
