# Configuring a server

## Pre-requisites

The server is generic over a `salsa::Database`, so you need to implement a database before starting the server.

You can use the default `BaseDb` database provided by `auto_lsp` or create your own.

The `default` module in server contains a file storage and file event handlers and workspace loading logic that are compatible with the `BaseDatabase` trait.

If you create your own database, you will have to create your own file storage and file event handlers.

## Configuring

To configure a server, you need to use the `create` method from the [`Session`](https://docs.rs/auto-lsp/latest/auto_lsp/server/struct.Session.html) struct wich takes 4 arguments.

- `parsers`: A list of parsers (previously defined with the [`configure_parsers!`](/workspace-and-document/configuring-parsers.html) macro)
- `capabilities`: Server capabilities, see [ServerCapabilities](https://docs.rs/lsp-types/latest/lsp_types/struct.ServerCapabilities.html).
- `server_info`: Optional information about the server, such as its name and version, see [ServerInfo](https://docs.rs/lsp-types/latest/lsp_types/struct.ServerInfo.html).
- `connection`: The connection to the client, see [Connection](https://docs.rs/lsp-server/latest/lsp_server/struct.Connection.html).
- `db`: The database to use, it must implement `salsa::Database`.

`create` will return a tuple containing the `Session` and the [InitializeParams](https://docs.rs/lsp-types/latest/lsp_types/struct.InitializeParams.html) sent by the client.

The server communicates with an LSP client using one of lsp_server's tranport methods: `stdio`, `tcp` or `memory`.

```rust, ignore
use std::error::Error;
use auto_lsp::server::{InitOptions, LspOptions, Session, ServerCapabilities};
use ast_python::db::PYTHON_PARSERS;

fn main() -> Result<(), Box<dyn Error + Sync + Send>> {
    let (connection, io_threads) = Connection::stdio();

    // Create a database, either BaseDb or your own
    let db = BaseDb::default();

    // Enable logging and tracing, this is optional
    stderrlog::new()
        .modules([module_path!(), "auto_lsp"])
        .verbosity(4)
        .init()
        .unwrap();

    fastrace::set_reporter(ConsoleReporter, Config::default());

    /// Create the session
    let (mut session, params) = Session::create(
       InitOptions {
            parsers: &PYTHON_PARSERS,
            capabilities: ServerCapabilities {
                ..Default::default()
            },
            server_info: None
        },
        connection,
        db,
    );

    // This is where you register your requests and notifications
    // See the handlers section for more information
    let mut request_registry = RequestRegistry::<BaseDb>::default();
    let mut notification_registry = NotificationRegistry::<BaseDb>::default();


    // This will also add all documents, parse and send diagnostics.
    // The init_workspace is only available for databases that implement BaseDatabase
    let init_results = session.init_workspace(params)?;
    if !init_results.is_empty() {
        init_results.into_iter().for_each(|result| {
            if let Err(err) = result {
                eprintln!("{}", err);
            }
        });
    };

    // Run the server and wait for the two threads to end (typically by trigger LSP Exit event).
        session.main_loop(
        &mut request_registry,
        &mut notification_registry,
    )?;
    session.io_threads.join()?;

    // Shut down gracefully.
    eprintln!("Shutting down server");
    Ok(())
}
```
