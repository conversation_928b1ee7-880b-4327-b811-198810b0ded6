# Dispatch

Since the AST can be traverrsed by either iterating through a list or by manually walking inside concrete types, the best way to interact with it is up to you.

For convenience, auto_lsp provides the `dispatch` and `dispatch_once` macro to call a method on all nodes that match a given type.

## dispatch_once

`dispatch_once` will call the method on the first node that matches the type and return early.

```rust, ignore
use ast::generated::{FunctionDefinition, ClassDefinition};
use auto_lsp::dispatch_once;

dispatch_once!(node.lower(), [
    FunctionDefinition => return_something(db, param),
    ClassDefinition => return_something(db, param)
]);
```

## dispatch

`dispatch` will call the method on all nodes that match the type.

```rust, ignore
use ast::generated::{FunctionDefinition, ClassDefinition};
use auto_lsp::dispatch;

dispatch!(node.lower(), [
    FunctionDefinition => build_something(db, param),
    ClassDefinition => build_something(db, param)
]);
```

## Lower Method

The `lower` method is used to get the lowest concrete type in the AST.

It's role is to avoid matching on enum variants by going directly to the concrete type.

It behaves like `enum_dispatch` but returns an `&dyn AstNode` instead of a concrete type.

```admonish
`lower` will always returns the lowest variant possible, this means that if an enum variant contains another, `lower` will recursively call `lower` on the inner variant.
```

## Example with dispatch_once

```rust, ignore
use crate::generated::{Identifier, PassStatement};
use auto_lsp::core::ast::AstNode;
use auto_lsp::core::dispatch_once;
use auto_lsp::core::salsa::db::{BaseDatabase, File};
use auto_lsp::core::salsa::tracked::get_ast;
use auto_lsp::lsp_types::{Hover, HoverParams};
use auto_lsp::{anyhow, lsp_types};

// Request for hover
pub fn hover(db: &impl BaseDatabase, params: HoverParams) -> anyhow::Result<Option<Hover>> {
    // Get the file in DB
    let uri = &params.text_document_position_params.text_document.uri;

    let file = db
        .get_file(uri)
        .ok_or_else(|| anyhow::format_err!("File not found in workspace"))?;

    let document = file.document(db);

    // Find the node at the position using `offset_at` method
    // Note that we could also iterate over the AST to find the node
    let offset = document
        .offset_at(params.text_document_position_params.position)
        .ok_or_else(|| {
            anyhow::format_err!(
                "Invalid position, {:?}",
                params.text_document_position_params.position
            )
        })?;

    // Get the node at the given offset
    if let Some(node) = get_ast(db, file).descendant_at(offset) {
        // Call the `get_hover` method on the node if it matches the type.
        dispatch_once!(node.lower(), [
            PassStatement => get_hover(db, file),
            Identifier => get_hover(db, file)
        ]);
    }
    Ok(None)
}

// Implementation of the `get_hover` method for `PassStatement` and `Identifier`

impl PassStatement {
    fn get_hover(
        &self,
        _db: &impl BaseDatabase,
        _file: File,
    ) -> anyhow::Result<Option<lsp_types::Hover>> {
        Ok(Some(lsp_types::Hover {
            contents: lsp_types::HoverContents::Markup(lsp_types::MarkupContent {
                kind: lsp_types::MarkupKind::Markdown,
                value: r#"This is a pass statement

[See python doc](https://docs.python.org/3/reference/simple_stmts.html#the-pass-statement)"#
                    .into(),
            }),
            range: None,
        }))
    }
}

impl Identifier {
    fn get_hover(
        &self,
        db: &impl BaseDatabase,
        file: File,
    ) -> anyhow::Result<Option<lsp_types::Hover>> {
        let doc = file.document(db);
        Ok(Some(lsp_types::Hover {
            contents: lsp_types::HoverContents::Markup(lsp_types::MarkupContent {
                kind: lsp_types::MarkupKind::PlainText,
                value: format!("hover {}", self.get_text(doc.texter.text.as_bytes())?),
            }),
            range: None,
        }))
    }
}
```
