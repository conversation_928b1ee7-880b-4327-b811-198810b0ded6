[package]
name = "auto-lsp-codegen"
authors = ["<PERSON><PERSON><PERSON>"]
license = "GPL-3.0"
repository = "https://github.com/adclz/auto-lsp"
description = """
Codegen crate for `auto_lsp`.
"""
version = "0.1.0"
edition = "2021"
readme = "README.md"

[dependencies]
serde = { version = "1.0.219", features = ["derive"] }
serde_json = { version = "1.0.140", features = ["preserve_order"] }
phf = { version = "0.11", features = ["macros"] }
quote = "1.0"
proc-macro2 = "1.0"
tree-sitter = { workspace = true }

[dev-dependencies]
tree-sitter-python = "0.23.6"
