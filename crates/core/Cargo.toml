[package]
name = "auto-lsp-core"
authors = ["<PERSON><PERSON><PERSON>"]
license = "GPL-3.0"
repository = "https://github.com/adclz/auto-lsp"
description = """
Core crate for `auto_lsp`.
"""
version = "0.5.0"
edition = "2021"

[dependencies]
lsp-types = { workspace = true }
tree-sitter = { workspace = true }
downcast-rs = { workspace = true }
streaming-iterator = { workspace = true }
parking_lot = { workspace = true }
texter = { workspace = true }
anyhow = { workspace = true }
regex = { workspace = true }
salsa = { workspace = true }
dashmap = { workspace = true }
log = { workspace = true }
rayon = { workspace = true, optional = true }
thiserror = { workspace = true }
ariadne = { workspace = true, features = ["auto-color"] }
fastrace = { workspace = true }

[dev-dependencies]
tree-sitter-html = "0.23.2"
rstest = { workspace = true }

[features]
wasm = ["parking_lot/nightly"]
