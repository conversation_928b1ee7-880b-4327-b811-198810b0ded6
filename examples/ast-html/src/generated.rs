# ! [allow (clippy :: all)] # ! [allow (unused)] # ! [allow (dead_code)] # ! [allow (non_camel_case_types)] # ! [allow (non_snake_case)] # [derive (Debug , <PERSON><PERSON> , PartialEq)] pub struct Attribute { pub children : Vec < std :: sync :: Arc < AttributeName_AttributeValue_QuotedAttributeValue >> , _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > } impl auto_lsp :: core :: ast :: AstNode for Attribute { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind_id () , 37u16) } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { self } fn get_id (& self) -> usize { self . _id } fn get_parent_id (& self) -> Option < usize > { self . _parent } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { & self . _range } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for Attribute { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { let mut children = vec ! [] ; ; builder . builder (db , & node , Some (id) , | b | { b . on_vec_children_id (& mut children) }) ; Ok (Self { children , _range : node . range () , _id : id , _parent : parent_id }) } } # [derive (Debug , Clone , PartialEq)] pub struct Doctype { _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > , } impl auto_lsp :: core :: ast :: AstNode for Doctype { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind_id () , 26u16) } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { self } fn get_id (& self) -> usize { self . _id } fn get_parent_id (& self) -> Option < usize > { self . _parent } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { & self . _range } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for Doctype { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { ; Ok (Self { _range : node . range () , _id : id , _parent : parent_id }) } } # [derive (Debug , Clone , PartialEq)] pub struct Document { pub children : Vec < std :: sync :: Arc < Doctype_Element_Entity_ErroneousEndTag_ScriptElement_StyleElement_Text >> , _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > } impl auto_lsp :: core :: ast :: AstNode for Document { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind_id () , 25u16) } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { self } fn get_id (& self) -> usize { self . _id } fn get_parent_id (& self) -> Option < usize > { self . _parent } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { & self . _range } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for Document { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { let mut children = vec ! [] ; ; builder . builder (db , & node , Some (id) , | b | { b . on_vec_children_id (& mut children) }) ; Ok (Self { children , _range : node . range () , _id : id , _parent : parent_id }) } } # [derive (Debug , Clone , PartialEq)] pub struct Element { pub children : Vec < std :: sync :: Arc < Doctype_Element_EndTag_Entity_ErroneousEndTag_ScriptElement_SelfClosingTag_StartTag_StyleElement_Text >> , _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > } impl auto_lsp :: core :: ast :: AstNode for Element { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind_id () , 28u16) } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { self } fn get_id (& self) -> usize { self . _id } fn get_parent_id (& self) -> Option < usize > { self . _parent } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { & self . _range } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for Element { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { let mut children = vec ! [] ; ; builder . builder (db , & node , Some (id) , | b | { b . on_vec_children_id (& mut children) }) ; Ok (Self { children , _range : node . range () , _id : id , _parent : parent_id }) } } # [derive (Debug , Clone , PartialEq)] pub struct EndTag { pub children : std :: sync :: Arc < TagName > , _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > } impl auto_lsp :: core :: ast :: AstNode for EndTag { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind_id () , 35u16) } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { self } fn get_id (& self) -> usize { self . _id } fn get_parent_id (& self) -> Option < usize > { self . _parent } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { & self . _range } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for EndTag { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { let mut children = Ok (None) ; ; builder . builder (db , & node , Some (id) , | b | { b . on_children_id (& mut children) }) ; Ok (Self { children : children ? . ok_or_else (|| { auto_lsp :: core :: errors :: AstError :: UnexpectedSymbol { range : node . range () , symbol : node . kind () , parent_name : stringify ! (TagName) , } }) ? , _range : node . range () , _id : id , _parent : parent_id }) } } # [derive (Debug , Clone , PartialEq)] pub struct ErroneousEndTag { pub children : std :: sync :: Arc < ErroneousEndTagName > , _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > } impl auto_lsp :: core :: ast :: AstNode for ErroneousEndTag { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind_id () , 36u16) } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { self } fn get_id (& self) -> usize { self . _id } fn get_parent_id (& self) -> Option < usize > { self . _parent } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { & self . _range } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for ErroneousEndTag { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { let mut children = Ok (None) ; ; builder . builder (db , & node , Some (id) , | b | { b . on_children_id (& mut children) }) ; Ok (Self { children : children ? . ok_or_else (|| { auto_lsp :: core :: errors :: AstError :: UnexpectedSymbol { range : node . range () , symbol : node . kind () , parent_name : stringify ! (ErroneousEndTagName) , } }) ? , _range : node . range () , _id : id , _parent : parent_id }) } } # [derive (Debug , Clone , PartialEq)] pub struct QuotedAttributeValue { pub children : Option < std :: sync :: Arc < AttributeValue >> , _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > } impl auto_lsp :: core :: ast :: AstNode for QuotedAttributeValue { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind_id () , 38u16) } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { self } fn get_id (& self) -> usize { self . _id } fn get_parent_id (& self) -> Option < usize > { self . _parent } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { & self . _range } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for QuotedAttributeValue { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { let mut children = Ok (None) ; ; builder . builder (db , & node , Some (id) , | b | { b . on_children_id (& mut children) }) ; Ok (Self { children : children ? , _range : node . range () , _id : id , _parent : parent_id }) } } # [derive (Debug , Clone , PartialEq)] pub struct ScriptElement { pub children : Vec < std :: sync :: Arc < EndTag_RawText_StartTag >> , _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > } impl auto_lsp :: core :: ast :: AstNode for ScriptElement { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind_id () , 29u16) } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { self } fn get_id (& self) -> usize { self . _id } fn get_parent_id (& self) -> Option < usize > { self . _parent } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { & self . _range } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for ScriptElement { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { let mut children = vec ! [] ; ; builder . builder (db , & node , Some (id) , | b | { b . on_vec_children_id (& mut children) }) ; Ok (Self { children , _range : node . range () , _id : id , _parent : parent_id }) } } # [derive (Debug , Clone , PartialEq)] pub struct SelfClosingTag { pub children : Vec < std :: sync :: Arc < Attribute_TagName >> , _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > } impl auto_lsp :: core :: ast :: AstNode for SelfClosingTag { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind_id () , 34u16) } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { self } fn get_id (& self) -> usize { self . _id } fn get_parent_id (& self) -> Option < usize > { self . _parent } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { & self . _range } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for SelfClosingTag { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { let mut children = vec ! [] ; ; builder . builder (db , & node , Some (id) , | b | { b . on_vec_children_id (& mut children) }) ; Ok (Self { children , _range : node . range () , _id : id , _parent : parent_id }) } } # [derive (Debug , Clone , PartialEq)] pub struct StartTag { pub children : Vec < std :: sync :: Arc < Attribute_TagName >> , _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > } impl auto_lsp :: core :: ast :: AstNode for StartTag { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind_id () , 31u16) } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { self } fn get_id (& self) -> usize { self . _id } fn get_parent_id (& self) -> Option < usize > { self . _parent } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { & self . _range } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for StartTag { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { let mut children = vec ! [] ; ; builder . builder (db , & node , Some (id) , | b | { b . on_vec_children_id (& mut children) }) ; Ok (Self { children , _range : node . range () , _id : id , _parent : parent_id }) } } # [derive (Debug , Clone , PartialEq)] pub struct StyleElement { pub children : Vec < std :: sync :: Arc < EndTag_RawText_StartTag >> , _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > } impl auto_lsp :: core :: ast :: AstNode for StyleElement { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind_id () , 30u16) } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { self } fn get_id (& self) -> usize { self . _id } fn get_parent_id (& self) -> Option < usize > { self . _parent } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { & self . _range } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for StyleElement { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { let mut children = vec ! [] ; ; builder . builder (db , & node , Some (id) , | b | { b . on_vec_children_id (& mut children) }) ; Ok (Self { children , _range : node . range () , _id : id , _parent : parent_id }) } } # [derive (Debug , Clone , PartialEq)] pub struct Token_DoubleQuote { _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > , } impl auto_lsp :: core :: ast :: AstNode for Token_DoubleQuote { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind () , "\"") } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { self } fn get_id (& self) -> usize { self . _id } fn get_parent_id (& self) -> Option < usize > { self . _parent } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { & self . _range } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for Token_DoubleQuote { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { ; Ok (Self { _range : node . range () , _id : id , _parent : parent_id }) } } # [derive (Debug , Clone , PartialEq)] pub struct Token_Quote { _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > , } impl auto_lsp :: core :: ast :: AstNode for Token_Quote { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind () , "'") } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { self } fn get_id (& self) -> usize { self . _id } fn get_parent_id (& self) -> Option < usize > { self . _parent } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { & self . _range } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for Token_Quote { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { ; Ok (Self { _range : node . range () , _id : id , _parent : parent_id }) } } # [derive (Debug , Clone , PartialEq)] pub struct Token_SlashGreater { _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > , } impl auto_lsp :: core :: ast :: AstNode for Token_SlashGreater { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind () , "/>") } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { self } fn get_id (& self) -> usize { self . _id } fn get_parent_id (& self) -> Option < usize > { self . _parent } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { & self . _range } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for Token_SlashGreater { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { ; Ok (Self { _range : node . range () , _id : id , _parent : parent_id }) } } # [derive (Debug , Clone , PartialEq)] pub struct Token_Less { _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > , } impl auto_lsp :: core :: ast :: AstNode for Token_Less { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind () , "<") } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { self } fn get_id (& self) -> usize { self . _id } fn get_parent_id (& self) -> Option < usize > { self . _parent } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { & self . _range } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for Token_Less { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { ; Ok (Self { _range : node . range () , _id : id , _parent : parent_id }) } } # [derive (Debug , Clone , PartialEq)] pub struct Token_LessBang { _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > , } impl auto_lsp :: core :: ast :: AstNode for Token_LessBang { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind () , "<!") } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { self } fn get_id (& self) -> usize { self . _id } fn get_parent_id (& self) -> Option < usize > { self . _parent } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { & self . _range } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for Token_LessBang { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { ; Ok (Self { _range : node . range () , _id : id , _parent : parent_id }) } } # [derive (Debug , Clone , PartialEq)] pub struct Token_LessSlash { _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > , } impl auto_lsp :: core :: ast :: AstNode for Token_LessSlash { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind () , "</") } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { self } fn get_id (& self) -> usize { self . _id } fn get_parent_id (& self) -> Option < usize > { self . _parent } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { & self . _range } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for Token_LessSlash { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { ; Ok (Self { _range : node . range () , _id : id , _parent : parent_id }) } } # [derive (Debug , Clone , PartialEq)] pub struct Token_Equal { _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > , } impl auto_lsp :: core :: ast :: AstNode for Token_Equal { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind () , "=") } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { self } fn get_id (& self) -> usize { self . _id } fn get_parent_id (& self) -> Option < usize > { self . _parent } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { & self . _range } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for Token_Equal { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { ; Ok (Self { _range : node . range () , _id : id , _parent : parent_id }) } } # [derive (Debug , Clone , PartialEq)] pub struct Token_Greater { _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > , } impl auto_lsp :: core :: ast :: AstNode for Token_Greater { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind () , ">") } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { self } fn get_id (& self) -> usize { self . _id } fn get_parent_id (& self) -> Option < usize > { self . _parent } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { & self . _range } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for Token_Greater { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { ; Ok (Self { _range : node . range () , _id : id , _parent : parent_id }) } } # [derive (Debug , Clone , PartialEq)] pub struct AttributeName { _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > , } impl auto_lsp :: core :: ast :: AstNode for AttributeName { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind_id () , 9u16) } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { self } fn get_id (& self) -> usize { self . _id } fn get_parent_id (& self) -> Option < usize > { self . _parent } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { & self . _range } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for AttributeName { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { ; Ok (Self { _range : node . range () , _id : id , _parent : parent_id }) } } # [derive (Debug , Clone , PartialEq)] pub struct AttributeValue { _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > , } impl auto_lsp :: core :: ast :: AstNode for AttributeValue { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind_id () , 10u16) } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { self } fn get_id (& self) -> usize { self . _id } fn get_parent_id (& self) -> Option < usize > { self . _parent } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { & self . _range } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for AttributeValue { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { ; Ok (Self { _range : node . range () , _id : id , _parent : parent_id }) } } # [derive (Debug , Clone , PartialEq)] pub struct Comment { _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > , } impl auto_lsp :: core :: ast :: AstNode for Comment { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind_id () , 24u16) } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { self } fn get_id (& self) -> usize { self . _id } fn get_parent_id (& self) -> Option < usize > { self . _parent } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { & self . _range } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for Comment { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { ; Ok (Self { _range : node . range () , _id : id , _parent : parent_id }) } } # [derive (Debug , Clone , PartialEq)] pub struct Token_Doctype { _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > , } impl auto_lsp :: core :: ast :: AstNode for Token_Doctype { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind_id () , 26u16) } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { self } fn get_id (& self) -> usize { self . _id } fn get_parent_id (& self) -> Option < usize > { self . _parent } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { & self . _range } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for Token_Doctype { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { ; Ok (Self { _range : node . range () , _id : id , _parent : parent_id }) } } # [derive (Debug , Clone , PartialEq)] pub struct Entity { _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > , } impl auto_lsp :: core :: ast :: AstNode for Entity { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind_id () , 11u16) } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { self } fn get_id (& self) -> usize { self . _id } fn get_parent_id (& self) -> Option < usize > { self . _parent } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { & self . _range } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for Entity { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { ; Ok (Self { _range : node . range () , _id : id , _parent : parent_id }) } } # [derive (Debug , Clone , PartialEq)] pub struct ErroneousEndTagName { _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > , } impl auto_lsp :: core :: ast :: AstNode for ErroneousEndTagName { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind_id () , 21u16) } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { self } fn get_id (& self) -> usize { self . _id } fn get_parent_id (& self) -> Option < usize > { self . _parent } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { & self . _range } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for ErroneousEndTagName { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { ; Ok (Self { _range : node . range () , _id : id , _parent : parent_id }) } } # [derive (Debug , Clone , PartialEq)] pub struct RawText { _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > , } impl auto_lsp :: core :: ast :: AstNode for RawText { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind_id () , 23u16) } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { self } fn get_id (& self) -> usize { self . _id } fn get_parent_id (& self) -> Option < usize > { self . _parent } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { & self . _range } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for RawText { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { ; Ok (Self { _range : node . range () , _id : id , _parent : parent_id }) } } # [derive (Debug , Clone , PartialEq)] pub struct TagName { _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > , } impl auto_lsp :: core :: ast :: AstNode for TagName { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind_id () , 17u16) } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { self } fn get_id (& self) -> usize { self . _id } fn get_parent_id (& self) -> Option < usize > { self . _parent } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { & self . _range } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for TagName { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { ; Ok (Self { _range : node . range () , _id : id , _parent : parent_id }) } } # [derive (Debug , Clone , PartialEq)] pub struct Text { _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > , } impl auto_lsp :: core :: ast :: AstNode for Text { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind_id () , 16u16) } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { self } fn get_id (& self) -> usize { self . _id } fn get_parent_id (& self) -> Option < usize > { self . _parent } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { & self . _range } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for Text { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { ; Ok (Self { _range : node . range () , _id : id , _parent : parent_id }) } } # [derive (Debug , Clone , PartialEq)] pub enum Doctype_Element_Entity_ErroneousEndTag_ScriptElement_StyleElement_Text { Doctype (Doctype) , Element (Element) , Entity (Entity) , ErroneousEndTag (ErroneousEndTag) , ScriptElement (ScriptElement) , StyleElement (StyleElement) , Text (Text) } impl auto_lsp :: core :: ast :: AstNode for Doctype_Element_Entity_ErroneousEndTag_ScriptElement_StyleElement_Text { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind_id () , 26u16 | 28u16 | 11u16 | 36u16 | 29u16 | 30u16 | 16u16) } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { match self { Self :: Doctype (node) => node . lower () , Self :: Element (node) => node . lower () , Self :: Entity (node) => node . lower () , Self :: ErroneousEndTag (node) => node . lower () , Self :: ScriptElement (node) => node . lower () , Self :: StyleElement (node) => node . lower () , Self :: Text (node) => node . lower () } } fn get_id (& self) -> usize { match self { Self :: Doctype (node) => node . get_id () , Self :: Element (node) => node . get_id () , Self :: Entity (node) => node . get_id () , Self :: ErroneousEndTag (node) => node . get_id () , Self :: ScriptElement (node) => node . get_id () , Self :: StyleElement (node) => node . get_id () , Self :: Text (node) => node . get_id () } } fn get_parent_id (& self) -> Option < usize > { match self { Self :: Doctype (node) => node . get_parent_id () , Self :: Element (node) => node . get_parent_id () , Self :: Entity (node) => node . get_parent_id () , Self :: ErroneousEndTag (node) => node . get_parent_id () , Self :: ScriptElement (node) => node . get_parent_id () , Self :: StyleElement (node) => node . get_parent_id () , Self :: Text (node) => node . get_parent_id () } } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { match self { Self :: Doctype (node) => node . get_range () , Self :: Element (node) => node . get_range () , Self :: Entity (node) => node . get_range () , Self :: ErroneousEndTag (node) => node . get_range () , Self :: ScriptElement (node) => node . get_range () , Self :: StyleElement (node) => node . get_range () , Self :: Text (node) => node . get_range () } } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for Doctype_Element_Entity_ErroneousEndTag_ScriptElement_StyleElement_Text { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { match node . kind_id () { 26u16 => Ok (Self :: Doctype (Doctype :: try_from ((node , db , builder , id , parent_id)) ?)) , 28u16 => Ok (Self :: Element (Element :: try_from ((node , db , builder , id , parent_id)) ?)) , 11u16 => Ok (Self :: Entity (Entity :: try_from ((node , db , builder , id , parent_id)) ?)) , 36u16 => Ok (Self :: ErroneousEndTag (ErroneousEndTag :: try_from ((node , db , builder , id , parent_id)) ?)) , 29u16 => Ok (Self :: ScriptElement (ScriptElement :: try_from ((node , db , builder , id , parent_id)) ?)) , 30u16 => Ok (Self :: StyleElement (StyleElement :: try_from ((node , db , builder , id , parent_id)) ?)) , 16u16 => Ok (Self :: Text (Text :: try_from ((node , db , builder , id , parent_id)) ?)) , _ => Err (auto_lsp :: core :: errors :: AstError :: UnexpectedSymbol { range : node . range () , symbol : node . kind () , parent_name : stringify ! (Doctype_Element_Entity_ErroneousEndTag_ScriptElement_StyleElement_Text) , }) } } } # [derive (Debug , Clone , PartialEq)] pub enum EndTag_RawText_StartTag { EndTag (EndTag) , RawText (RawText) , StartTag (StartTag) } impl auto_lsp :: core :: ast :: AstNode for EndTag_RawText_StartTag { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind_id () , 35u16 | 23u16 | 31u16) } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { match self { Self :: EndTag (node) => node . lower () , Self :: RawText (node) => node . lower () , Self :: StartTag (node) => node . lower () } } fn get_id (& self) -> usize { match self { Self :: EndTag (node) => node . get_id () , Self :: RawText (node) => node . get_id () , Self :: StartTag (node) => node . get_id () } } fn get_parent_id (& self) -> Option < usize > { match self { Self :: EndTag (node) => node . get_parent_id () , Self :: RawText (node) => node . get_parent_id () , Self :: StartTag (node) => node . get_parent_id () } } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { match self { Self :: EndTag (node) => node . get_range () , Self :: RawText (node) => node . get_range () , Self :: StartTag (node) => node . get_range () } } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for EndTag_RawText_StartTag { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { match node . kind_id () { 35u16 => Ok (Self :: EndTag (EndTag :: try_from ((node , db , builder , id , parent_id)) ?)) , 23u16 => Ok (Self :: RawText (RawText :: try_from ((node , db , builder , id , parent_id)) ?)) , 31u16 => Ok (Self :: StartTag (StartTag :: try_from ((node , db , builder , id , parent_id)) ?)) , _ => Err (auto_lsp :: core :: errors :: AstError :: UnexpectedSymbol { range : node . range () , symbol : node . kind () , parent_name : stringify ! (EndTag_RawText_StartTag) , }) } } } # [derive (Debug , Clone , PartialEq)] pub enum Attribute_TagName { Attribute (Attribute) , TagName (TagName) } impl auto_lsp :: core :: ast :: AstNode for Attribute_TagName { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind_id () , 37u16 | 17u16) } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { match self { Self :: Attribute (node) => node . lower () , Self :: TagName (node) => node . lower () } } fn get_id (& self) -> usize { match self { Self :: Attribute (node) => node . get_id () , Self :: TagName (node) => node . get_id () } } fn get_parent_id (& self) -> Option < usize > { match self { Self :: Attribute (node) => node . get_parent_id () , Self :: TagName (node) => node . get_parent_id () } } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { match self { Self :: Attribute (node) => node . get_range () , Self :: TagName (node) => node . get_range () } } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for Attribute_TagName { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { match node . kind_id () { 37u16 => Ok (Self :: Attribute (Attribute :: try_from ((node , db , builder , id , parent_id)) ?)) , 17u16 => Ok (Self :: TagName (TagName :: try_from ((node , db , builder , id , parent_id)) ?)) , _ => Err (auto_lsp :: core :: errors :: AstError :: UnexpectedSymbol { range : node . range () , symbol : node . kind () , parent_name : stringify ! (Attribute_TagName) , }) } } } # [derive (Debug , Clone , PartialEq)] pub enum AttributeName_AttributeValue_QuotedAttributeValue { AttributeName (AttributeName) , AttributeValue (AttributeValue) , QuotedAttributeValue (QuotedAttributeValue) } impl auto_lsp :: core :: ast :: AstNode for AttributeName_AttributeValue_QuotedAttributeValue { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind_id () , 9u16 | 10u16 | 38u16) } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { match self { Self :: AttributeName (node) => node . lower () , Self :: AttributeValue (node) => node . lower () , Self :: QuotedAttributeValue (node) => node . lower () } } fn get_id (& self) -> usize { match self { Self :: AttributeName (node) => node . get_id () , Self :: AttributeValue (node) => node . get_id () , Self :: QuotedAttributeValue (node) => node . get_id () } } fn get_parent_id (& self) -> Option < usize > { match self { Self :: AttributeName (node) => node . get_parent_id () , Self :: AttributeValue (node) => node . get_parent_id () , Self :: QuotedAttributeValue (node) => node . get_parent_id () } } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { match self { Self :: AttributeName (node) => node . get_range () , Self :: AttributeValue (node) => node . get_range () , Self :: QuotedAttributeValue (node) => node . get_range () } } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for AttributeName_AttributeValue_QuotedAttributeValue { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { match node . kind_id () { 9u16 => Ok (Self :: AttributeName (AttributeName :: try_from ((node , db , builder , id , parent_id)) ?)) , 10u16 => Ok (Self :: AttributeValue (AttributeValue :: try_from ((node , db , builder , id , parent_id)) ?)) , 38u16 => Ok (Self :: QuotedAttributeValue (QuotedAttributeValue :: try_from ((node , db , builder , id , parent_id)) ?)) , _ => Err (auto_lsp :: core :: errors :: AstError :: UnexpectedSymbol { range : node . range () , symbol : node . kind () , parent_name : stringify ! (AttributeName_AttributeValue_QuotedAttributeValue) , }) } } } # [derive (Debug , Clone , PartialEq)] pub enum Doctype_Element_EndTag_Entity_ErroneousEndTag_ScriptElement_SelfClosingTag_StartTag_StyleElement_Text { Doctype (Doctype) , Element (Element) , EndTag (EndTag) , Entity (Entity) , ErroneousEndTag (ErroneousEndTag) , ScriptElement (ScriptElement) , SelfClosingTag (SelfClosingTag) , StartTag (StartTag) , StyleElement (StyleElement) , Text (Text) } impl auto_lsp :: core :: ast :: AstNode for Doctype_Element_EndTag_Entity_ErroneousEndTag_ScriptElement_SelfClosingTag_StartTag_StyleElement_Text { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind_id () , 26u16 | 28u16 | 35u16 | 11u16 | 36u16 | 29u16 | 34u16 | 31u16 | 30u16 | 16u16) } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { match self { Self :: Doctype (node) => node . lower () , Self :: Element (node) => node . lower () , Self :: EndTag (node) => node . lower () , Self :: Entity (node) => node . lower () , Self :: ErroneousEndTag (node) => node . lower () , Self :: ScriptElement (node) => node . lower () , Self :: SelfClosingTag (node) => node . lower () , Self :: StartTag (node) => node . lower () , Self :: StyleElement (node) => node . lower () , Self :: Text (node) => node . lower () } } fn get_id (& self) -> usize { match self { Self :: Doctype (node) => node . get_id () , Self :: Element (node) => node . get_id () , Self :: EndTag (node) => node . get_id () , Self :: Entity (node) => node . get_id () , Self :: ErroneousEndTag (node) => node . get_id () , Self :: ScriptElement (node) => node . get_id () , Self :: SelfClosingTag (node) => node . get_id () , Self :: StartTag (node) => node . get_id () , Self :: StyleElement (node) => node . get_id () , Self :: Text (node) => node . get_id () } } fn get_parent_id (& self) -> Option < usize > { match self { Self :: Doctype (node) => node . get_parent_id () , Self :: Element (node) => node . get_parent_id () , Self :: EndTag (node) => node . get_parent_id () , Self :: Entity (node) => node . get_parent_id () , Self :: ErroneousEndTag (node) => node . get_parent_id () , Self :: ScriptElement (node) => node . get_parent_id () , Self :: SelfClosingTag (node) => node . get_parent_id () , Self :: StartTag (node) => node . get_parent_id () , Self :: StyleElement (node) => node . get_parent_id () , Self :: Text (node) => node . get_parent_id () } } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { match self { Self :: Doctype (node) => node . get_range () , Self :: Element (node) => node . get_range () , Self :: EndTag (node) => node . get_range () , Self :: Entity (node) => node . get_range () , Self :: ErroneousEndTag (node) => node . get_range () , Self :: ScriptElement (node) => node . get_range () , Self :: SelfClosingTag (node) => node . get_range () , Self :: StartTag (node) => node . get_range () , Self :: StyleElement (node) => node . get_range () , Self :: Text (node) => node . get_range () } } } impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for Doctype_Element_EndTag_Entity_ErroneousEndTag_ScriptElement_SelfClosingTag_StartTag_StyleElement_Text { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { match node . kind_id () { 26u16 => Ok (Self :: Doctype (Doctype :: try_from ((node , db , builder , id , parent_id)) ?)) , 28u16 => Ok (Self :: Element (Element :: try_from ((node , db , builder , id , parent_id)) ?)) , 35u16 => Ok (Self :: EndTag (EndTag :: try_from ((node , db , builder , id , parent_id)) ?)) , 11u16 => Ok (Self :: Entity (Entity :: try_from ((node , db , builder , id , parent_id)) ?)) , 36u16 => Ok (Self :: ErroneousEndTag (ErroneousEndTag :: try_from ((node , db , builder , id , parent_id)) ?)) , 29u16 => Ok (Self :: ScriptElement (ScriptElement :: try_from ((node , db , builder , id , parent_id)) ?)) , 34u16 => Ok (Self :: SelfClosingTag (SelfClosingTag :: try_from ((node , db , builder , id , parent_id)) ?)) , 31u16 => Ok (Self :: StartTag (StartTag :: try_from ((node , db , builder , id , parent_id)) ?)) , 30u16 => Ok (Self :: StyleElement (StyleElement :: try_from ((node , db , builder , id , parent_id)) ?)) , 16u16 => Ok (Self :: Text (Text :: try_from ((node , db , builder , id , parent_id)) ?)) , _ => Err (auto_lsp :: core :: errors :: AstError :: UnexpectedSymbol { range : node . range () , symbol : node . kind () , parent_name : stringify ! (Doctype_Element_EndTag_Entity_ErroneousEndTag_ScriptElement_SelfClosingTag_StartTag_StyleElement_Text) , }) } } }