#![allow(clippy::all)]
#![allow(unused)]
#![allow(dead_code)]
#![allow(non_camel_case_types)]
#![allow(non_snake_case)]
#[derive(Debug, <PERSON>lone, PartialEq)]
pub struct AliasedImport {
    pub name: std::sync::Arc<DottedName>,
    pub alias: std::sync::Arc<Identifier>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for AliasedImport {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 117u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for AliasedImport {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut name = Ok(None);
        let mut alias = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<DottedName, 19u16>(&mut name)?
                .on_field_id::<Identifier, 1u16>(&mut alias)
        });
        Ok(Self {
            name: name?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(name),
            })?,
            alias: alias?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(alias),
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct ArgumentList {
    pub children: Vec<
        std::sync::Arc<
            DictionarySplat_Expression_KeywordArgument_ListSplat_ParenthesizedExpression,
        >,
    >,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for ArgumentList {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 158u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ArgumentList {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct AsPattern {
    pub alias: Option<std::sync::Arc<AsPatternTarget>>,
    pub children: Vec<std::sync::Arc<CasePattern_Expression_Identifier>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for AsPattern {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 186u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for AsPattern {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut alias = Ok(None);
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<AsPatternTarget, 1u16>(&mut alias)?
                .on_vec_children_id(&mut children)
        });
        Ok(Self {
            alias: alias?,
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct AssertStatement {
    pub children: Vec<std::sync::Arc<Expression>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for AssertStatement {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 121u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for AssertStatement {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Assignment {
    pub right: Option<
        std::sync::Arc<Assignment_AugmentedAssignment_Expression_ExpressionList_PatternList_Yield>,
    >,
    pub Type: Option<std::sync::Arc<Type>>,
    pub left: std::sync::Arc<Pattern_PatternList>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Assignment {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 199u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Assignment {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut right = Ok(None);
        let mut Type = Ok(None);
        let mut left = Ok(None);
        builder . builder (db , & node , Some (id) , | b | { b . on_field_id :: < Assignment_AugmentedAssignment_Expression_ExpressionList_PatternList_Yield , 25u16 > (& mut right) ? . on_field_id :: < Type , 29u16 > (& mut Type) ? . on_field_id :: < Pattern_PatternList , 17u16 > (& mut left) }) ;
        Ok(Self {
            right: right?,
            Type: Type?,
            left: left?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(left),
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Attribute {
    pub object: std::sync::Arc<PrimaryExpression>,
    pub attribute: std::sync::Arc<Identifier>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Attribute {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 204u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Attribute {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut object = Ok(None);
        let mut attribute = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<PrimaryExpression, 20u16>(&mut object)?
                .on_field_id::<Identifier, 5u16>(&mut attribute)
        });
        Ok(Self {
            object: object?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(object),
            })?,
            attribute: attribute?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(attribute),
                }
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct AugmentedAssignment {
    pub left: std::sync::Arc<Pattern_PatternList>,
    pub right:
        std::sync::Arc<Assignment_AugmentedAssignment_Expression_ExpressionList_PatternList_Yield>,
    pub operator: std::sync::Arc<Operators_0>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for AugmentedAssignment {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 200u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for AugmentedAssignment {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut left = Ok(None);
        let mut right = Ok(None);
        let mut operator = Ok(None);
        builder . builder (db , & node , Some (id) , | b | { b . on_field_id :: < Pattern_PatternList , 17u16 > (& mut left) ? . on_field_id :: < Assignment_AugmentedAssignment_Expression_ExpressionList_PatternList_Yield , 25u16 > (& mut right) ? . on_field_id :: < Operators_0 , 21u16 > (& mut operator) }) ;
        Ok(Self {
            left: left?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(left),
            })?,
            right: right?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(right),
            })?,
            operator: operator?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(operator),
                }
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Await {
    pub children: std::sync::Arc<PrimaryExpression>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Await {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 238u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Await {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = Ok(None);
        builder.builder(db, &node, Some(id), |b| b.on_children_id(&mut children));
        Ok(Self {
            children: children?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(PrimaryExpression),
                }
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct BinaryOperator {
    pub operator: std::sync::Arc<Operators_1>,
    pub right: std::sync::Arc<PrimaryExpression>,
    pub left: std::sync::Arc<PrimaryExpression>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for BinaryOperator {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 192u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for BinaryOperator {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut operator = Ok(None);
        let mut right = Ok(None);
        let mut left = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<Operators_1, 21u16>(&mut operator)?
                .on_field_id::<PrimaryExpression, 25u16>(&mut right)?
                .on_field_id::<PrimaryExpression, 17u16>(&mut left)
        });
        Ok(Self {
            operator: operator?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(operator),
                }
            })?,
            right: right?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(right),
            })?,
            left: left?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(left),
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Block {
    pub alternative: Vec<std::sync::Arc<CaseClause>>,
    pub children: Vec<std::sync::Arc<CompoundStatement_SimpleStatement>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Block {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 161u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Block {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut alternative = vec![];
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| {
            b.on_vec_field_id::<CaseClause, 2u16>(&mut alternative)?
                .on_vec_children_id(&mut children)
        });
        Ok(Self {
            alternative,
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct BooleanOperator {
    pub left: std::sync::Arc<Expression>,
    pub right: std::sync::Arc<Expression>,
    pub operator: std::sync::Arc<Operators_2>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for BooleanOperator {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 191u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for BooleanOperator {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut left = Ok(None);
        let mut right = Ok(None);
        let mut operator = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<Expression, 17u16>(&mut left)?
                .on_field_id::<Expression, 25u16>(&mut right)?
                .on_field_id::<Operators_2, 21u16>(&mut operator)
        });
        Ok(Self {
            left: left?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(left),
            })?,
            right: right?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(right),
            })?,
            operator: operator?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(operator),
                }
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct BreakStatement {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for BreakStatement {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 129u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for BreakStatement {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Call {
    pub arguments: std::sync::Arc<ArgumentList_GeneratorExpression>,
    pub function: std::sync::Arc<PrimaryExpression>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Call {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 207u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Call {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut arguments = Ok(None);
        let mut function = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<ArgumentList_GeneratorExpression, 4u16>(&mut arguments)?
                .on_field_id::<PrimaryExpression, 14u16>(&mut function)
        });
        Ok(Self {
            arguments: arguments?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(arguments),
                }
            })?,
            function: function?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(function),
                }
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct CaseClause {
    pub consequence: std::sync::Arc<Block>,
    pub guard: Option<std::sync::Arc<IfClause>>,
    pub children: Vec<std::sync::Arc<CasePattern>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for CaseClause {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 136u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for CaseClause {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut consequence = Ok(None);
        let mut guard = Ok(None);
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<Block, 10u16>(&mut consequence)?
                .on_field_id::<IfClause, 15u16>(&mut guard)?
                .on_vec_children_id(&mut children)
        });
        Ok(Self {
            consequence: consequence?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(consequence),
                }
            })?,
            guard: guard?,
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct CasePattern { pub children : Option < std :: sync :: Arc < AsPattern_ClassPattern_ComplexPattern_ConcatenatedString_DictPattern_DottedName_False_Float_Integer_KeywordPattern_ListPattern_None_SplatPattern_String_True_TuplePattern_UnionPattern >> , _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > }
impl auto_lsp::core::ast::AstNode for CasePattern {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 164u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for CasePattern {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = Ok(None);
        builder.builder(db, &node, Some(id), |b| b.on_children_id(&mut children));
        Ok(Self {
            children: children?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Chevron {
    pub children: std::sync::Arc<Expression>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Chevron {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 120u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Chevron {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = Ok(None);
        builder.builder(db, &node, Some(id), |b| b.on_children_id(&mut children));
        Ok(Self {
            children: children?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(Expression),
                }
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct ClassDefinition {
    pub body: std::sync::Arc<Block>,
    pub type_parameters: Option<std::sync::Arc<TypeParameter>>,
    pub superclasses: Option<std::sync::Arc<ArgumentList>>,
    pub name: std::sync::Arc<Identifier>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for ClassDefinition {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 155u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ClassDefinition {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut body = Ok(None);
        let mut type_parameters = Ok(None);
        let mut superclasses = Ok(None);
        let mut name = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<Block, 6u16>(&mut body)?
                .on_field_id::<TypeParameter, 31u16>(&mut type_parameters)?
                .on_field_id::<ArgumentList, 28u16>(&mut superclasses)?
                .on_field_id::<Identifier, 19u16>(&mut name)
        });
        Ok(Self {
            body: body?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(body),
            })?,
            type_parameters: type_parameters?,
            superclasses: superclasses?,
            name: name?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(name),
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct ClassPattern {
    pub children: Vec<std::sync::Arc<CasePattern_DottedName>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for ClassPattern {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 174u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ClassPattern {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct ComparisonOperator {
    pub operators: Vec<std::sync::Arc<Operators_3>>,
    pub children: Vec<std::sync::Arc<PrimaryExpression>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for ComparisonOperator {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 196u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ComparisonOperator {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut operators = vec![];
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| {
            b.on_vec_field_id::<Operators_3, 22u16>(&mut operators)?
                .on_vec_children_id(&mut children)
        });
        Ok(Self {
            operators,
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct ComplexPattern {
    pub children: Vec<std::sync::Arc<Float_Integer>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for ComplexPattern {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 175u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ComplexPattern {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct ConcatenatedString {
    pub children: Vec<std::sync::Arc<String>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for ConcatenatedString {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 231u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ConcatenatedString {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct ConditionalExpression {
    pub children: Vec<std::sync::Arc<Expression>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for ConditionalExpression {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 230u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ConditionalExpression {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct ConstrainedType {
    pub children: Vec<std::sync::Arc<Type>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for ConstrainedType {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 213u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ConstrainedType {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct ContinueStatement {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for ContinueStatement {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 130u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ContinueStatement {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct DecoratedDefinition {
    pub definition: std::sync::Arc<ClassDefinition_FunctionDefinition>,
    pub children: Vec<std::sync::Arc<Decorator>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for DecoratedDefinition {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 159u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for DecoratedDefinition {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut definition = Ok(None);
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<ClassDefinition_FunctionDefinition, 11u16>(&mut definition)?
                .on_vec_children_id(&mut children)
        });
        Ok(Self {
            definition: definition?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(definition),
                }
            })?,
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Decorator {
    pub children: std::sync::Arc<Expression>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Decorator {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 160u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Decorator {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = Ok(None);
        builder.builder(db, &node, Some(id), |b| b.on_children_id(&mut children));
        Ok(Self {
            children: children?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(Expression),
                }
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct DefaultParameter {
    pub name: std::sync::Arc<Identifier_TuplePattern>,
    pub value: std::sync::Arc<Expression>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for DefaultParameter {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 182u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for DefaultParameter {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut name = Ok(None);
        let mut value = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<Identifier_TuplePattern, 19u16>(&mut name)?
                .on_field_id::<Expression, 32u16>(&mut value)
        });
        Ok(Self {
            name: name?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(name),
            })?,
            value: value?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(value),
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct DeleteStatement {
    pub children: std::sync::Arc<Expression_ExpressionList>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for DeleteStatement {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 126u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for DeleteStatement {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = Ok(None);
        builder.builder(db, &node, Some(id), |b| b.on_children_id(&mut children));
        Ok(Self {
            children: children?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(Expression_ExpressionList),
                }
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct DictPattern { pub key : Vec < std :: sync :: Arc < Minus_Underscore_ClassPattern_ComplexPattern_ConcatenatedString_DictPattern_DottedName_False_Float_Integer_ListPattern_None_SplatPattern_String_True_TuplePattern_UnionPattern >> , pub value : Vec < std :: sync :: Arc < CasePattern >> , pub children : Vec < std :: sync :: Arc < SplatPattern >> , _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > }
impl auto_lsp::core::ast::AstNode for DictPattern {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 170u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for DictPattern {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut key = vec![];
        let mut value = vec![];
        let mut children = vec![];
        builder . builder (db , & node , Some (id) , | b | { b . on_vec_field_id :: < Minus_Underscore_ClassPattern_ComplexPattern_ConcatenatedString_DictPattern_DottedName_False_Float_Integer_ListPattern_None_SplatPattern_String_True_TuplePattern_UnionPattern , 16u16 > (& mut key) ? . on_vec_field_id :: < CasePattern , 32u16 > (& mut value) ? . on_vec_children_id (& mut children) }) ;
        Ok(Self {
            key,
            value,
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Dictionary {
    pub children: Vec<std::sync::Arc<DictionarySplat_Pair>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Dictionary {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 219u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Dictionary {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct DictionaryComprehension {
    pub body: std::sync::Arc<Pair>,
    pub children: Vec<std::sync::Arc<ForInClause_IfClause>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for DictionaryComprehension {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 222u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for DictionaryComprehension {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut body = Ok(None);
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<Pair, 6u16>(&mut body)?
                .on_vec_children_id(&mut children)
        });
        Ok(Self {
            body: body?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(body),
            })?,
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct DictionarySplat {
    pub children: std::sync::Arc<Expression>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for DictionarySplat {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 150u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for DictionarySplat {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = Ok(None);
        builder.builder(db, &node, Some(id), |b| b.on_children_id(&mut children));
        Ok(Self {
            children: children?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(Expression),
                }
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct DictionarySplatPattern {
    pub children: std::sync::Arc<Attribute_Identifier_Subscript>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for DictionarySplatPattern {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 185u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for DictionarySplatPattern {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = Ok(None);
        builder.builder(db, &node, Some(id), |b| b.on_children_id(&mut children));
        Ok(Self {
            children: children?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(Attribute_Identifier_Subscript),
                }
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct DottedName {
    pub children: Vec<std::sync::Arc<Identifier>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for DottedName {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 163u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for DottedName {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct ElifClause {
    pub consequence: std::sync::Arc<Block>,
    pub condition: std::sync::Arc<Expression>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for ElifClause {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 132u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ElifClause {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut consequence = Ok(None);
        let mut condition = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<Block, 10u16>(&mut consequence)?
                .on_field_id::<Expression, 9u16>(&mut condition)
        });
        Ok(Self {
            consequence: consequence?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(consequence),
                }
            })?,
            condition: condition?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(condition),
                }
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct ElseClause {
    pub body: std::sync::Arc<Block>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for ElseClause {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 133u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ElseClause {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut body = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<Block, 6u16>(&mut body)
        });
        Ok(Self {
            body: body?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(body),
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct ExceptClause {
    pub value: Option<std::sync::Arc<Expression>>,
    pub alias: Option<std::sync::Arc<Expression>>,
    pub children: std::sync::Arc<Block>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for ExceptClause {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 140u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ExceptClause {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut value = Ok(None);
        let mut alias = Ok(None);
        let mut children = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<Expression, 32u16>(&mut value)?
                .on_field_id::<Expression, 1u16>(&mut alias)?
                .on_children_id(&mut children)
        });
        Ok(Self {
            value: value?,
            alias: alias?,
            children: children?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(Block),
                }
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct ExceptGroupClause {
    pub children: Vec<std::sync::Arc<Block_Expression>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for ExceptGroupClause {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 141u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ExceptGroupClause {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct ExecStatement {
    pub code: std::sync::Arc<Identifier_String>,
    pub children: Vec<std::sync::Arc<Expression>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for ExecStatement {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 153u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ExecStatement {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut code = Ok(None);
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<Identifier_String, 8u16>(&mut code)?
                .on_vec_children_id(&mut children)
        });
        Ok(Self {
            code: code?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(code),
            })?,
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct ExpressionList {
    pub children: Vec<std::sync::Arc<Expression>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for ExpressionList {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 162u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ExpressionList {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct ExpressionStatement {
    pub children: Vec<std::sync::Arc<Assignment_AugmentedAssignment_Expression_Yield>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for ExpressionStatement {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 122u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ExpressionStatement {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct FinallyClause {
    pub children: std::sync::Arc<Block>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for FinallyClause {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 142u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for FinallyClause {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = Ok(None);
        builder.builder(db, &node, Some(id), |b| b.on_children_id(&mut children));
        Ok(Self {
            children: children?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(Block),
                }
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct ForInClause {
    pub right: Vec<std::sync::Arc<Comma_Expression>>,
    pub left: std::sync::Arc<Pattern_PatternList>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for ForInClause {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 228u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ForInClause {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut right = vec![];
        let mut left = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_vec_field_id::<Comma_Expression, 25u16>(&mut right)?
                .on_field_id::<Pattern_PatternList, 17u16>(&mut left)
        });
        Ok(Self {
            right,
            left: left?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(left),
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct ForStatement {
    pub alternative: Option<std::sync::Arc<ElseClause>>,
    pub body: std::sync::Arc<Block>,
    pub right: std::sync::Arc<Expression_ExpressionList>,
    pub left: std::sync::Arc<Pattern_PatternList>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for ForStatement {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 137u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ForStatement {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut alternative = Ok(None);
        let mut body = Ok(None);
        let mut right = Ok(None);
        let mut left = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<ElseClause, 2u16>(&mut alternative)?
                .on_field_id::<Block, 6u16>(&mut body)?
                .on_field_id::<Expression_ExpressionList, 25u16>(&mut right)?
                .on_field_id::<Pattern_PatternList, 17u16>(&mut left)
        });
        Ok(Self {
            alternative: alternative?,
            body: body?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(body),
            })?,
            right: right?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(right),
            })?,
            left: left?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(left),
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct FormatExpression {
    pub format_specifier: Option<std::sync::Arc<FormatSpecifier>>,
    pub type_conversion: Option<std::sync::Arc<TypeConversion>>,
    pub expression: std::sync::Arc<Expression_ExpressionList_PatternList_Yield>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for FormatExpression {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 274u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for FormatExpression {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut format_specifier = Ok(None);
        let mut type_conversion = Ok(None);
        let mut expression = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<FormatSpecifier, 13u16>(&mut format_specifier)?
                .on_field_id::<TypeConversion, 30u16>(&mut type_conversion)?
                .on_field_id::<Expression_ExpressionList_PatternList_Yield, 12u16>(&mut expression)
        });
        Ok(Self {
            format_specifier: format_specifier?,
            type_conversion: type_conversion?,
            expression: expression?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(expression),
                }
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct FormatSpecifier {
    pub children: Vec<std::sync::Arc<FormatExpression>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for FormatSpecifier {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 237u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for FormatSpecifier {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct FunctionDefinition {
    pub name: std::sync::Arc<Identifier>,
    pub body: std::sync::Arc<Block>,
    pub type_parameters: Option<std::sync::Arc<TypeParameter>>,
    pub parameters: std::sync::Arc<Parameters>,
    pub return_type: Option<std::sync::Arc<Type>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for FunctionDefinition {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 146u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for FunctionDefinition {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut name = Ok(None);
        let mut body = Ok(None);
        let mut type_parameters = Ok(None);
        let mut parameters = Ok(None);
        let mut return_type = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<Identifier, 19u16>(&mut name)?
                .on_field_id::<Block, 6u16>(&mut body)?
                .on_field_id::<TypeParameter, 31u16>(&mut type_parameters)?
                .on_field_id::<Parameters, 23u16>(&mut parameters)?
                .on_field_id::<Type, 24u16>(&mut return_type)
        });
        Ok(Self {
            name: name?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(name),
            })?,
            body: body?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(body),
            })?,
            type_parameters: type_parameters?,
            parameters: parameters?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(parameters),
                }
            })?,
            return_type: return_type?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct FutureImportStatement {
    pub name: Vec<std::sync::Arc<AliasedImport_DottedName>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for FutureImportStatement {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 114u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for FutureImportStatement {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut name = vec![];
        builder.builder(db, &node, Some(id), |b| {
            b.on_vec_field_id::<AliasedImport_DottedName, 19u16>(&mut name)
        });
        Ok(Self {
            name,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct GeneratorExpression {
    pub body: std::sync::Arc<Expression>,
    pub children: Vec<std::sync::Arc<ForInClause_IfClause>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for GeneratorExpression {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 224u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for GeneratorExpression {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut body = Ok(None);
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<Expression, 6u16>(&mut body)?
                .on_vec_children_id(&mut children)
        });
        Ok(Self {
            body: body?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(body),
            })?,
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct GenericType {
    pub children: Vec<std::sync::Arc<Identifier_TypeParameter>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for GenericType {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 211u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for GenericType {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct GlobalStatement {
    pub children: Vec<std::sync::Arc<Identifier>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for GlobalStatement {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 151u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for GlobalStatement {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct IfClause {
    pub children: std::sync::Arc<Expression>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for IfClause {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 229u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for IfClause {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = Ok(None);
        builder.builder(db, &node, Some(id), |b| b.on_children_id(&mut children));
        Ok(Self {
            children: children?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(Expression),
                }
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct IfStatement {
    pub alternative: Vec<std::sync::Arc<ElifClause_ElseClause>>,
    pub condition: std::sync::Arc<Expression>,
    pub consequence: std::sync::Arc<Block>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for IfStatement {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 131u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for IfStatement {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut alternative = vec![];
        let mut condition = Ok(None);
        let mut consequence = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_vec_field_id::<ElifClause_ElseClause, 2u16>(&mut alternative)?
                .on_field_id::<Expression, 9u16>(&mut condition)?
                .on_field_id::<Block, 10u16>(&mut consequence)
        });
        Ok(Self {
            alternative,
            condition: condition?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(condition),
                }
            })?,
            consequence: consequence?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(consequence),
                }
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct ImportFromStatement {
    pub module_name: std::sync::Arc<DottedName_RelativeImport>,
    pub name: Vec<std::sync::Arc<AliasedImport_DottedName>>,
    pub children: Option<std::sync::Arc<WildcardImport>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for ImportFromStatement {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 115u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ImportFromStatement {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut module_name = Ok(None);
        let mut name = vec![];
        let mut children = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<DottedName_RelativeImport, 18u16>(&mut module_name)?
                .on_vec_field_id::<AliasedImport_DottedName, 19u16>(&mut name)?
                .on_children_id(&mut children)
        });
        Ok(Self {
            module_name: module_name?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(module_name),
                }
            })?,
            name,
            children: children?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct ImportPrefix {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for ImportPrefix {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 112u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ImportPrefix {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct ImportStatement {
    pub name: Vec<std::sync::Arc<AliasedImport_DottedName>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for ImportStatement {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 111u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ImportStatement {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut name = vec![];
        builder.builder(db, &node, Some(id), |b| {
            b.on_vec_field_id::<AliasedImport_DottedName, 19u16>(&mut name)
        });
        Ok(Self {
            name,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Interpolation {
    pub expression: std::sync::Arc<Expression_ExpressionList_PatternList_Yield>,
    pub format_specifier: Option<std::sync::Arc<FormatSpecifier>>,
    pub type_conversion: Option<std::sync::Arc<TypeConversion>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Interpolation {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 234u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Interpolation {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut expression = Ok(None);
        let mut format_specifier = Ok(None);
        let mut type_conversion = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<Expression_ExpressionList_PatternList_Yield, 12u16>(&mut expression)?
                .on_field_id::<FormatSpecifier, 13u16>(&mut format_specifier)?
                .on_field_id::<TypeConversion, 30u16>(&mut type_conversion)
        });
        Ok(Self {
            expression: expression?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(expression),
                }
            })?,
            format_specifier: format_specifier?,
            type_conversion: type_conversion?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_IsNot {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_IsNot {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "is not")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_IsNot {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct KeywordArgument {
    pub name: std::sync::Arc<Identifier>,
    pub value: std::sync::Arc<Expression>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for KeywordArgument {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 215u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for KeywordArgument {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut name = Ok(None);
        let mut value = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<Identifier, 19u16>(&mut name)?
                .on_field_id::<Expression, 32u16>(&mut value)
        });
        Ok(Self {
            name: name?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(name),
            })?,
            value: value?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(value),
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct KeywordPattern { pub children : Vec < std :: sync :: Arc < ClassPattern_ComplexPattern_ConcatenatedString_DictPattern_DottedName_False_Float_Identifier_Integer_ListPattern_None_SplatPattern_String_True_TuplePattern_UnionPattern >> , _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > }
impl auto_lsp::core::ast::AstNode for KeywordPattern {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 172u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for KeywordPattern {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct KeywordSeparator {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for KeywordSeparator {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 240u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for KeywordSeparator {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Lambda {
    pub parameters: Option<std::sync::Arc<LambdaParameters>>,
    pub body: std::sync::Arc<Expression>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Lambda {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 197u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Lambda {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut parameters = Ok(None);
        let mut body = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<LambdaParameters, 23u16>(&mut parameters)?
                .on_field_id::<Expression, 6u16>(&mut body)
        });
        Ok(Self {
            parameters: parameters?,
            body: body?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(body),
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct LambdaParameters {
    pub children: Vec<std::sync::Arc<Parameter>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for LambdaParameters {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 148u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for LambdaParameters {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct List {
    pub children: Vec<std::sync::Arc<Expression_ListSplat_ParenthesizedListSplat_Yield>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for List {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 216u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for List {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct ListComprehension {
    pub body: std::sync::Arc<Expression>,
    pub children: Vec<std::sync::Arc<ForInClause_IfClause>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for ListComprehension {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 221u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ListComprehension {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut body = Ok(None);
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<Expression, 6u16>(&mut body)?
                .on_vec_children_id(&mut children)
        });
        Ok(Self {
            body: body?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(body),
            })?,
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct ListPattern {
    pub children: Vec<std::sync::Arc<CasePattern_Pattern>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for ListPattern {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 181u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ListPattern {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct ListSplat {
    pub children: std::sync::Arc<Attribute_Expression_Identifier_Subscript>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for ListSplat {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 149u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ListSplat {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = Ok(None);
        builder.builder(db, &node, Some(id), |b| b.on_children_id(&mut children));
        Ok(Self {
            children: children?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(Attribute_Expression_Identifier_Subscript),
                }
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct ListSplatPattern {
    pub children: std::sync::Arc<Attribute_Identifier_Subscript>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for ListSplatPattern {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 184u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ListSplatPattern {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = Ok(None);
        builder.builder(db, &node, Some(id), |b| b.on_children_id(&mut children));
        Ok(Self {
            children: children?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(Attribute_Identifier_Subscript),
                }
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct MatchStatement {
    pub subject: Vec<std::sync::Arc<Expression>>,
    pub body: std::sync::Arc<Block>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for MatchStatement {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 134u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for MatchStatement {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut subject = vec![];
        let mut body = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_vec_field_id::<Expression, 26u16>(&mut subject)?
                .on_field_id::<Block, 6u16>(&mut body)
        });
        Ok(Self {
            subject,
            body: body?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(body),
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct MemberType {
    pub children: Vec<std::sync::Arc<Identifier_Type>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for MemberType {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 214u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for MemberType {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Module {
    pub children: Vec<std::sync::Arc<CompoundStatement_SimpleStatement>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Module {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 108u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Module {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct NamedExpression {
    pub name: std::sync::Arc<Identifier>,
    pub value: std::sync::Arc<Expression>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for NamedExpression {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 123u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for NamedExpression {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut name = Ok(None);
        let mut value = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<Identifier, 19u16>(&mut name)?
                .on_field_id::<Expression, 32u16>(&mut value)
        });
        Ok(Self {
            name: name?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(name),
            })?,
            value: value?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(value),
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct NonlocalStatement {
    pub children: Vec<std::sync::Arc<Identifier>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for NonlocalStatement {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 152u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for NonlocalStatement {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_NotIn {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_NotIn {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "not in")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_NotIn {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct NotOperator {
    pub argument: std::sync::Arc<Expression>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for NotOperator {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 190u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for NotOperator {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut argument = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<Expression, 3u16>(&mut argument)
        });
        Ok(Self {
            argument: argument?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(argument),
                }
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Pair {
    pub value: std::sync::Arc<Expression>,
    pub key: std::sync::Arc<Expression>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Pair {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 220u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Pair {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut value = Ok(None);
        let mut key = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<Expression, 32u16>(&mut value)?
                .on_field_id::<Expression, 16u16>(&mut key)
        });
        Ok(Self {
            value: value?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(value),
            })?,
            key: key?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(key),
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Parameters {
    pub children: Vec<std::sync::Arc<Parameter>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Parameters {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 147u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Parameters {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct ParenthesizedExpression {
    pub children: std::sync::Arc<Expression_ListSplat_ParenthesizedExpression_Yield>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for ParenthesizedExpression {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 226u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ParenthesizedExpression {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = Ok(None);
        builder.builder(db, &node, Some(id), |b| b.on_children_id(&mut children));
        Ok(Self {
            children: children?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(Expression_ListSplat_ParenthesizedExpression_Yield),
                }
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct ParenthesizedListSplat {
    pub children: std::sync::Arc<ListSplat_ParenthesizedExpression>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for ParenthesizedListSplat {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 157u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ParenthesizedListSplat {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = Ok(None);
        builder.builder(db, &node, Some(id), |b| b.on_children_id(&mut children));
        Ok(Self {
            children: children?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(ListSplat_ParenthesizedExpression),
                }
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct PassStatement {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for PassStatement {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 128u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for PassStatement {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct PatternList {
    pub children: Vec<std::sync::Arc<Pattern>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for PatternList {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 201u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for PatternList {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct PositionalSeparator {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for PositionalSeparator {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 239u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for PositionalSeparator {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct PrintStatement {
    pub argument: Vec<std::sync::Arc<Expression>>,
    pub children: Option<std::sync::Arc<Chevron>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for PrintStatement {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 119u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for PrintStatement {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut argument = vec![];
        let mut children = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_vec_field_id::<Expression, 3u16>(&mut argument)?
                .on_children_id(&mut children)
        });
        Ok(Self {
            argument,
            children: children?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct RaiseStatement {
    pub cause: Option<std::sync::Arc<Expression>>,
    pub children: Option<std::sync::Arc<Expression_ExpressionList>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for RaiseStatement {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 127u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for RaiseStatement {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut cause = Ok(None);
        let mut children = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<Expression, 7u16>(&mut cause)?
                .on_children_id(&mut children)
        });
        Ok(Self {
            cause: cause?,
            children: children?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct RelativeImport {
    pub children: Vec<std::sync::Arc<DottedName_ImportPrefix>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for RelativeImport {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 113u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for RelativeImport {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct ReturnStatement {
    pub children: Option<std::sync::Arc<Expression_ExpressionList>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for ReturnStatement {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 125u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ReturnStatement {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = Ok(None);
        builder.builder(db, &node, Some(id), |b| b.on_children_id(&mut children));
        Ok(Self {
            children: children?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Set {
    pub children: Vec<std::sync::Arc<Expression_ListSplat_ParenthesizedListSplat_Yield>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Set {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 217u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Set {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct SetComprehension {
    pub body: std::sync::Arc<Expression>,
    pub children: Vec<std::sync::Arc<ForInClause_IfClause>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for SetComprehension {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 223u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for SetComprehension {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut body = Ok(None);
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<Expression, 6u16>(&mut body)?
                .on_vec_children_id(&mut children)
        });
        Ok(Self {
            body: body?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(body),
            })?,
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Slice {
    pub children: Vec<std::sync::Arc<Expression>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Slice {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 206u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Slice {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct SplatPattern {
    pub children: Option<std::sync::Arc<Identifier>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for SplatPattern {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 173u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for SplatPattern {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = Ok(None);
        builder.builder(db, &node, Some(id), |b| b.on_children_id(&mut children));
        Ok(Self {
            children: children?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct SplatType {
    pub children: std::sync::Arc<Identifier>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for SplatType {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 210u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for SplatType {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = Ok(None);
        builder.builder(db, &node, Some(id), |b| b.on_children_id(&mut children));
        Ok(Self {
            children: children?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(Identifier),
                }
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct String {
    pub children: Vec<std::sync::Arc<Interpolation_StringContent_StringEnd_StringStart>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for String {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 232u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for String {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct StringContent {
    pub children: Vec<std::sync::Arc<EscapeInterpolation_EscapeSequence>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for StringContent {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 233u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for StringContent {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Subscript {
    pub subscript: Vec<std::sync::Arc<Expression_Slice>>,
    pub value: std::sync::Arc<PrimaryExpression>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Subscript {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 205u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Subscript {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut subscript = vec![];
        let mut value = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_vec_field_id::<Expression_Slice, 27u16>(&mut subscript)?
                .on_field_id::<PrimaryExpression, 32u16>(&mut value)
        });
        Ok(Self {
            subscript,
            value: value?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(value),
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct TryStatement {
    pub body: std::sync::Arc<Block>,
    pub children: Vec<std::sync::Arc<ElseClause_ExceptClause_ExceptGroupClause_FinallyClause>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for TryStatement {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 139u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for TryStatement {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut body = Ok(None);
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<Block, 6u16>(&mut body)?
                .on_vec_children_id(&mut children)
        });
        Ok(Self {
            body: body?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(body),
            })?,
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Tuple {
    pub children: Vec<std::sync::Arc<Expression_ListSplat_ParenthesizedListSplat_Yield>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Tuple {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 218u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Tuple {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct TuplePattern {
    pub children: Vec<std::sync::Arc<CasePattern_Pattern>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for TuplePattern {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 180u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for TuplePattern {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Type {
    pub children:
        std::sync::Arc<ConstrainedType_Expression_GenericType_MemberType_SplatType_UnionType>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Type {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 209u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Type {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = Ok(None);
        builder.builder(db, &node, Some(id), |b| b.on_children_id(&mut children));
        Ok(Self {
            children: children?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(
                        ConstrainedType_Expression_GenericType_MemberType_SplatType_UnionType
                    ),
                }
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct TypeAliasStatement {
    pub left: std::sync::Arc<Type>,
    pub right: std::sync::Arc<Type>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for TypeAliasStatement {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 154u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for TypeAliasStatement {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut left = Ok(None);
        let mut right = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<Type, 17u16>(&mut left)?
                .on_field_id::<Type, 25u16>(&mut right)
        });
        Ok(Self {
            left: left?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(left),
            })?,
            right: right?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(right),
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct TypeParameter {
    pub children: Vec<std::sync::Arc<Type>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for TypeParameter {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 156u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for TypeParameter {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct TypedDefaultParameter {
    pub name: std::sync::Arc<Identifier>,
    pub value: std::sync::Arc<Expression>,
    pub Type: std::sync::Arc<Type>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for TypedDefaultParameter {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 183u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for TypedDefaultParameter {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut name = Ok(None);
        let mut value = Ok(None);
        let mut Type = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<Identifier, 19u16>(&mut name)?
                .on_field_id::<Expression, 32u16>(&mut value)?
                .on_field_id::<Type, 29u16>(&mut Type)
        });
        Ok(Self {
            name: name?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(name),
            })?,
            value: value?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(value),
            })?,
            Type: Type?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(Type),
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct TypedParameter {
    pub Type: std::sync::Arc<Type>,
    pub children: std::sync::Arc<DictionarySplatPattern_Identifier_ListSplatPattern>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for TypedParameter {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 208u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for TypedParameter {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut Type = Ok(None);
        let mut children = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<Type, 29u16>(&mut Type)?
                .on_children_id(&mut children)
        });
        Ok(Self {
            Type: Type?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(Type),
            })?,
            children: children?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(DictionarySplatPattern_Identifier_ListSplatPattern),
                }
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct UnaryOperator {
    pub argument: std::sync::Arc<PrimaryExpression>,
    pub operator: std::sync::Arc<Operators_4>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for UnaryOperator {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 193u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for UnaryOperator {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut argument = Ok(None);
        let mut operator = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<PrimaryExpression, 3u16>(&mut argument)?
                .on_field_id::<Operators_4, 21u16>(&mut operator)
        });
        Ok(Self {
            argument: argument?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(argument),
                }
            })?,
            operator: operator?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(operator),
                }
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct UnionPattern { pub children : Vec < std :: sync :: Arc < ClassPattern_ComplexPattern_ConcatenatedString_DictPattern_DottedName_False_Float_Integer_ListPattern_None_SplatPattern_String_True_TuplePattern_UnionPattern >> , _range : auto_lsp :: tree_sitter :: Range , _id : usize , _parent : Option < usize > }
impl auto_lsp::core::ast::AstNode for UnionPattern {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 167u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for UnionPattern {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct UnionType {
    pub children: Vec<std::sync::Arc<Type>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for UnionType {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 212u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for UnionType {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct WhileStatement {
    pub body: std::sync::Arc<Block>,
    pub alternative: Option<std::sync::Arc<ElseClause>>,
    pub condition: std::sync::Arc<Expression>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for WhileStatement {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 138u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for WhileStatement {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut body = Ok(None);
        let mut alternative = Ok(None);
        let mut condition = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<Block, 6u16>(&mut body)?
                .on_field_id::<ElseClause, 2u16>(&mut alternative)?
                .on_field_id::<Expression, 9u16>(&mut condition)
        });
        Ok(Self {
            body: body?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(body),
            })?,
            alternative: alternative?,
            condition: condition?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(condition),
                }
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct WildcardImport {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for WildcardImport {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 118u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for WildcardImport {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct WithClause {
    pub children: Vec<std::sync::Arc<WithItem>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for WithClause {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 144u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for WithClause {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = vec![];
        builder.builder(db, &node, Some(id), |b| b.on_vec_children_id(&mut children));
        Ok(Self {
            children,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct WithItem {
    pub value: std::sync::Arc<Expression>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for WithItem {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 145u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for WithItem {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut value = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<Expression, 32u16>(&mut value)
        });
        Ok(Self {
            value: value?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(value),
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct WithStatement {
    pub body: std::sync::Arc<Block>,
    pub children: std::sync::Arc<WithClause>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for WithStatement {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 143u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for WithStatement {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut body = Ok(None);
        let mut children = Ok(None);
        builder.builder(db, &node, Some(id), |b| {
            b.on_field_id::<Block, 6u16>(&mut body)?
                .on_children_id(&mut children)
        });
        Ok(Self {
            body: body?.ok_or_else(|| auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(body),
            })?,
            children: children?.ok_or_else(|| {
                auto_lsp::core::errors::AstError::UnexpectedSymbol {
                    range: node.range(),
                    symbol: node.kind(),
                    parent_name: stringify!(WithClause),
                }
            })?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Yield {
    pub children: Option<std::sync::Arc<Expression_ExpressionList>>,
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Yield {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 203u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Yield {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        let mut children = Ok(None);
        builder.builder(db, &node, Some(id), |b| b.on_children_id(&mut children));
        Ok(Self {
            children: children?,
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_NotEqual {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_NotEqual {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "!=")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_NotEqual {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Percent {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Percent {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "%")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Percent {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_PercentEqual {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_PercentEqual {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "%=")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_PercentEqual {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Ampersand {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Ampersand {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "&")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Ampersand {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_AmpersandEqual {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_AmpersandEqual {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "&=")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_AmpersandEqual {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_LeftParen {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_LeftParen {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "(")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_LeftParen {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_RightParen {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_RightParen {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), ")")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_RightParen {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Star {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Star {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "*")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Star {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_StarStar {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_StarStar {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "**")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_StarStar {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_StarStarEqual {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_StarStarEqual {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "**=")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_StarStarEqual {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_StarEqual {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_StarEqual {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "*=")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_StarEqual {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Plus {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Plus {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "+")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Plus {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_PlusEqual {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_PlusEqual {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "+=")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_PlusEqual {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Comma {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Comma {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), ",")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Comma {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Minus {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Minus {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "-")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Minus {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_MinusEqual {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_MinusEqual {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "-=")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_MinusEqual {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Arrow {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Arrow {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "->")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Arrow {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Dot {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Dot {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), ".")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Dot {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Slash {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Slash {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "/")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Slash {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_SlashSlash {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_SlashSlash {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "//")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_SlashSlash {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_SlashSlashEqual {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_SlashSlashEqual {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "//=")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_SlashSlashEqual {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_SlashEqual {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_SlashEqual {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "/=")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_SlashEqual {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Colon {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Colon {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), ":")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Colon {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_ColonEqual {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_ColonEqual {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), ":=")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_ColonEqual {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Semicolon {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Semicolon {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), ";")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Semicolon {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Less {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Less {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "<")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Less {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_ShiftLeft {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_ShiftLeft {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "<<")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_ShiftLeft {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_LessLessEqual {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_LessLessEqual {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "<<=")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_LessLessEqual {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_LessEqual {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_LessEqual {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "<=")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_LessEqual {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_LessGreater {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_LessGreater {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "<>")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_LessGreater {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Equal {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Equal {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "=")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Equal {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_EqualEqual {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_EqualEqual {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "==")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_EqualEqual {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Greater {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Greater {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), ">")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Greater {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_GreaterEqual {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_GreaterEqual {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), ">=")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_GreaterEqual {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_ShiftRight {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_ShiftRight {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), ">>")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_ShiftRight {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_GreaterGreaterEqual {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_GreaterGreaterEqual {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), ">>=")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_GreaterGreaterEqual {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_At {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_At {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "@")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_At {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_AtEqual {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_AtEqual {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "@=")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_AtEqual {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_LeftBracket {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_LeftBracket {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "[")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_LeftBracket {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Backslash {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Backslash {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "\\")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Backslash {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_RightBracket {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_RightBracket {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "]")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_RightBracket {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Caret {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Caret {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "^")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Caret {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_CaretEqual {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_CaretEqual {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "^=")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_CaretEqual {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Underscore {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Underscore {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "_")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Underscore {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Future {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Future {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "__future__")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Future {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_And {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_And {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "and")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_And {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_As {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_As {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "as")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_As {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Assert {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Assert {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "assert")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Assert {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Async {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Async {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "async")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Async {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Await {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Await {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 238u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Await {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Break {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Break {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "break")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Break {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Case {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Case {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "case")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Case {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Class {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Class {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "class")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Class {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Comment {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Comment {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 99u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Comment {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Continue {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Continue {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "continue")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Continue {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Def {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Def {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "def")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Def {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Del {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Del {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "del")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Del {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Elif {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Elif {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "elif")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Elif {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Ellipsis {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Ellipsis {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 88u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Ellipsis {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Else {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Else {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "else")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Else {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct EscapeInterpolation {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for EscapeInterpolation {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 106u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for EscapeInterpolation {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct EscapeSequence {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for EscapeSequence {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 89u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for EscapeSequence {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Except {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Except {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "except")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Except {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_ExceptStar {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_ExceptStar {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "except*")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_ExceptStar {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Exec {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Exec {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "exec")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Exec {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct False {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for False {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 97u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for False {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Finally {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Finally {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "finally")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Finally {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Float {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Float {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 94u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Float {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_For {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_For {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "for")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_For {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_From {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_From {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "from")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_From {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Global {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Global {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "global")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Global {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Identifier {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Identifier {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 1u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Identifier {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_If {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_If {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "if")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_If {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Import {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Import {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "import")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Import {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_In {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_In {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "in")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_In {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Integer {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Integer {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 93u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Integer {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Is {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Is {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "is")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Is {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Lambda {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Lambda {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 197u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Lambda {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct LineContinuation {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for LineContinuation {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 100u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for LineContinuation {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Match {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Match {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "match")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Match {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct None {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for None {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 98u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for None {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Nonlocal {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Nonlocal {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "nonlocal")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Nonlocal {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Not {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Not {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "not")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Not {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Or {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Or {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "or")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Or {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Pass {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Pass {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "pass")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Pass {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Print {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Print {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "print")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Print {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Raise {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Raise {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "raise")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Raise {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Return {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Return {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "return")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Return {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct StringEnd {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for StringEnd {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 107u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for StringEnd {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct StringStart {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for StringStart {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 104u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for StringStart {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct True {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for True {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 96u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for True {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Try {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Try {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "try")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Try {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Type {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Type {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 209u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Type {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct TypeConversion {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for TypeConversion {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 92u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for TypeConversion {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_While {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_While {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "while")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_While {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_With {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_With {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "with")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_With {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Yield {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Yield {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 203u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Yield {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_LeftCurly {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_LeftCurly {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "{")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_LeftCurly {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Pipe {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Pipe {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "|")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Pipe {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_PipeEqual {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_PipeEqual {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "|=")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_PipeEqual {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_RightCurly {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_RightCurly {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "}")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_RightCurly {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct Token_Tilde {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for Token_Tilde {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "~")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Token_Tilde {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum Operators_0 {
    Token_PercentEqual(Token_PercentEqual),
    Token_AmpersandEqual(Token_AmpersandEqual),
    Token_StarStarEqual(Token_StarStarEqual),
    Token_StarEqual(Token_StarEqual),
    Token_PlusEqual(Token_PlusEqual),
    Token_MinusEqual(Token_MinusEqual),
    Token_SlashSlashEqual(Token_SlashSlashEqual),
    Token_SlashEqual(Token_SlashEqual),
    Token_LessLessEqual(Token_LessLessEqual),
    Token_GreaterGreaterEqual(Token_GreaterGreaterEqual),
    Token_AtEqual(Token_AtEqual),
    Token_CaretEqual(Token_CaretEqual),
    Token_PipeEqual(Token_PipeEqual),
}
impl auto_lsp::core::ast::AstNode for Operators_0 {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(
            node.kind_id(),
            80u16
                | 84u16
                | 81u16
                | 76u16
                | 74u16
                | 75u16
                | 79u16
                | 77u16
                | 83u16
                | 82u16
                | 78u16
                | 85u16
                | 86u16
        )
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::Token_PercentEqual(node) => node.lower(),
            Self::Token_AmpersandEqual(node) => node.lower(),
            Self::Token_StarStarEqual(node) => node.lower(),
            Self::Token_StarEqual(node) => node.lower(),
            Self::Token_PlusEqual(node) => node.lower(),
            Self::Token_MinusEqual(node) => node.lower(),
            Self::Token_SlashSlashEqual(node) => node.lower(),
            Self::Token_SlashEqual(node) => node.lower(),
            Self::Token_LessLessEqual(node) => node.lower(),
            Self::Token_GreaterGreaterEqual(node) => node.lower(),
            Self::Token_AtEqual(node) => node.lower(),
            Self::Token_CaretEqual(node) => node.lower(),
            Self::Token_PipeEqual(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::Token_PercentEqual(node) => node.get_id(),
            Self::Token_AmpersandEqual(node) => node.get_id(),
            Self::Token_StarStarEqual(node) => node.get_id(),
            Self::Token_StarEqual(node) => node.get_id(),
            Self::Token_PlusEqual(node) => node.get_id(),
            Self::Token_MinusEqual(node) => node.get_id(),
            Self::Token_SlashSlashEqual(node) => node.get_id(),
            Self::Token_SlashEqual(node) => node.get_id(),
            Self::Token_LessLessEqual(node) => node.get_id(),
            Self::Token_GreaterGreaterEqual(node) => node.get_id(),
            Self::Token_AtEqual(node) => node.get_id(),
            Self::Token_CaretEqual(node) => node.get_id(),
            Self::Token_PipeEqual(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::Token_PercentEqual(node) => node.get_parent_id(),
            Self::Token_AmpersandEqual(node) => node.get_parent_id(),
            Self::Token_StarStarEqual(node) => node.get_parent_id(),
            Self::Token_StarEqual(node) => node.get_parent_id(),
            Self::Token_PlusEqual(node) => node.get_parent_id(),
            Self::Token_MinusEqual(node) => node.get_parent_id(),
            Self::Token_SlashSlashEqual(node) => node.get_parent_id(),
            Self::Token_SlashEqual(node) => node.get_parent_id(),
            Self::Token_LessLessEqual(node) => node.get_parent_id(),
            Self::Token_GreaterGreaterEqual(node) => node.get_parent_id(),
            Self::Token_AtEqual(node) => node.get_parent_id(),
            Self::Token_CaretEqual(node) => node.get_parent_id(),
            Self::Token_PipeEqual(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::Token_PercentEqual(node) => node.get_range(),
            Self::Token_AmpersandEqual(node) => node.get_range(),
            Self::Token_StarStarEqual(node) => node.get_range(),
            Self::Token_StarEqual(node) => node.get_range(),
            Self::Token_PlusEqual(node) => node.get_range(),
            Self::Token_MinusEqual(node) => node.get_range(),
            Self::Token_SlashSlashEqual(node) => node.get_range(),
            Self::Token_SlashEqual(node) => node.get_range(),
            Self::Token_LessLessEqual(node) => node.get_range(),
            Self::Token_GreaterGreaterEqual(node) => node.get_range(),
            Self::Token_AtEqual(node) => node.get_range(),
            Self::Token_CaretEqual(node) => node.get_range(),
            Self::Token_PipeEqual(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Operators_0 {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            80u16 => Ok(Self::Token_PercentEqual(Token_PercentEqual::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            84u16 => Ok(Self::Token_AmpersandEqual(Token_AmpersandEqual::try_from(
                (node, db, builder, id, parent_id),
            )?)),
            81u16 => Ok(Self::Token_StarStarEqual(Token_StarStarEqual::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            76u16 => Ok(Self::Token_StarEqual(Token_StarEqual::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            74u16 => Ok(Self::Token_PlusEqual(Token_PlusEqual::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            75u16 => Ok(Self::Token_MinusEqual(Token_MinusEqual::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            79u16 => Ok(Self::Token_SlashSlashEqual(
                Token_SlashSlashEqual::try_from((node, db, builder, id, parent_id))?,
            )),
            77u16 => Ok(Self::Token_SlashEqual(Token_SlashEqual::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            83u16 => Ok(Self::Token_LessLessEqual(Token_LessLessEqual::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            82u16 => Ok(Self::Token_GreaterGreaterEqual(
                Token_GreaterGreaterEqual::try_from((node, db, builder, id, parent_id))?,
            )),
            78u16 => Ok(Self::Token_AtEqual(Token_AtEqual::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            85u16 => Ok(Self::Token_CaretEqual(Token_CaretEqual::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            86u16 => Ok(Self::Token_PipeEqual(Token_PipeEqual::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(Operators_0),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum Operators_1 {
    Token_Percent(Token_Percent),
    Token_Ampersand(Token_Ampersand),
    Token_Star(Token_Star),
    Token_StarStar(Token_StarStar),
    Token_Plus(Token_Plus),
    Token_Minus(Token_Minus),
    Token_Slash(Token_Slash),
    Token_SlashSlash(Token_SlashSlash),
    Token_ShiftLeft(Token_ShiftLeft),
    Token_ShiftRight(Token_ShiftRight),
    Token_At(Token_At),
    Token_Caret(Token_Caret),
    Token_Pipe(Token_Pipe),
}
impl auto_lsp::core::ast::AstNode for Operators_1 {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(
            node.kind_id(),
            59u16
                | 61u16
                | 11u16
                | 39u16
                | 54u16
                | 49u16
                | 58u16
                | 60u16
                | 63u16
                | 13u16
                | 48u16
                | 62u16
                | 51u16
        )
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::Token_Percent(node) => node.lower(),
            Self::Token_Ampersand(node) => node.lower(),
            Self::Token_Star(node) => node.lower(),
            Self::Token_StarStar(node) => node.lower(),
            Self::Token_Plus(node) => node.lower(),
            Self::Token_Minus(node) => node.lower(),
            Self::Token_Slash(node) => node.lower(),
            Self::Token_SlashSlash(node) => node.lower(),
            Self::Token_ShiftLeft(node) => node.lower(),
            Self::Token_ShiftRight(node) => node.lower(),
            Self::Token_At(node) => node.lower(),
            Self::Token_Caret(node) => node.lower(),
            Self::Token_Pipe(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::Token_Percent(node) => node.get_id(),
            Self::Token_Ampersand(node) => node.get_id(),
            Self::Token_Star(node) => node.get_id(),
            Self::Token_StarStar(node) => node.get_id(),
            Self::Token_Plus(node) => node.get_id(),
            Self::Token_Minus(node) => node.get_id(),
            Self::Token_Slash(node) => node.get_id(),
            Self::Token_SlashSlash(node) => node.get_id(),
            Self::Token_ShiftLeft(node) => node.get_id(),
            Self::Token_ShiftRight(node) => node.get_id(),
            Self::Token_At(node) => node.get_id(),
            Self::Token_Caret(node) => node.get_id(),
            Self::Token_Pipe(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::Token_Percent(node) => node.get_parent_id(),
            Self::Token_Ampersand(node) => node.get_parent_id(),
            Self::Token_Star(node) => node.get_parent_id(),
            Self::Token_StarStar(node) => node.get_parent_id(),
            Self::Token_Plus(node) => node.get_parent_id(),
            Self::Token_Minus(node) => node.get_parent_id(),
            Self::Token_Slash(node) => node.get_parent_id(),
            Self::Token_SlashSlash(node) => node.get_parent_id(),
            Self::Token_ShiftLeft(node) => node.get_parent_id(),
            Self::Token_ShiftRight(node) => node.get_parent_id(),
            Self::Token_At(node) => node.get_parent_id(),
            Self::Token_Caret(node) => node.get_parent_id(),
            Self::Token_Pipe(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::Token_Percent(node) => node.get_range(),
            Self::Token_Ampersand(node) => node.get_range(),
            Self::Token_Star(node) => node.get_range(),
            Self::Token_StarStar(node) => node.get_range(),
            Self::Token_Plus(node) => node.get_range(),
            Self::Token_Minus(node) => node.get_range(),
            Self::Token_Slash(node) => node.get_range(),
            Self::Token_SlashSlash(node) => node.get_range(),
            Self::Token_ShiftLeft(node) => node.get_range(),
            Self::Token_ShiftRight(node) => node.get_range(),
            Self::Token_At(node) => node.get_range(),
            Self::Token_Caret(node) => node.get_range(),
            Self::Token_Pipe(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Operators_1 {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            59u16 => Ok(Self::Token_Percent(Token_Percent::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            61u16 => Ok(Self::Token_Ampersand(Token_Ampersand::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            11u16 => Ok(Self::Token_Star(Token_Star::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            39u16 => Ok(Self::Token_StarStar(Token_StarStar::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            54u16 => Ok(Self::Token_Plus(Token_Plus::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            49u16 => Ok(Self::Token_Minus(Token_Minus::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            58u16 => Ok(Self::Token_Slash(Token_Slash::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            60u16 => Ok(Self::Token_SlashSlash(Token_SlashSlash::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            63u16 => Ok(Self::Token_ShiftLeft(Token_ShiftLeft::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            13u16 => Ok(Self::Token_ShiftRight(Token_ShiftRight::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            48u16 => Ok(Self::Token_At(Token_At::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            62u16 => Ok(Self::Token_Caret(Token_Caret::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            51u16 => Ok(Self::Token_Pipe(Token_Pipe::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(Operators_1),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum Operators_3 {
    Token_NotEqual(Token_NotEqual),
    Token_Less(Token_Less),
    Token_LessEqual(Token_LessEqual),
    Token_LessGreater(Token_LessGreater),
    Token_EqualEqual(Token_EqualEqual),
    Token_Greater(Token_Greater),
    Token_GreaterEqual(Token_GreaterEqual),
    Token_In(Token_In),
    Token_Is(Token_Is),
    Token_IsNot(Token_IsNot),
    Token_NotIn(Token_NotIn),
}
impl auto_lsp::core::ast::AstNode for Operators_3 {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(
            node.kind_id(),
            69u16 | 66u16 | 67u16 | 72u16 | 68u16 | 71u16 | 70u16 | 30u16 | 65u16 | 195u16 | 194u16
        )
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::Token_NotEqual(node) => node.lower(),
            Self::Token_Less(node) => node.lower(),
            Self::Token_LessEqual(node) => node.lower(),
            Self::Token_LessGreater(node) => node.lower(),
            Self::Token_EqualEqual(node) => node.lower(),
            Self::Token_Greater(node) => node.lower(),
            Self::Token_GreaterEqual(node) => node.lower(),
            Self::Token_In(node) => node.lower(),
            Self::Token_Is(node) => node.lower(),
            Self::Token_IsNot(node) => node.lower(),
            Self::Token_NotIn(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::Token_NotEqual(node) => node.get_id(),
            Self::Token_Less(node) => node.get_id(),
            Self::Token_LessEqual(node) => node.get_id(),
            Self::Token_LessGreater(node) => node.get_id(),
            Self::Token_EqualEqual(node) => node.get_id(),
            Self::Token_Greater(node) => node.get_id(),
            Self::Token_GreaterEqual(node) => node.get_id(),
            Self::Token_In(node) => node.get_id(),
            Self::Token_Is(node) => node.get_id(),
            Self::Token_IsNot(node) => node.get_id(),
            Self::Token_NotIn(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::Token_NotEqual(node) => node.get_parent_id(),
            Self::Token_Less(node) => node.get_parent_id(),
            Self::Token_LessEqual(node) => node.get_parent_id(),
            Self::Token_LessGreater(node) => node.get_parent_id(),
            Self::Token_EqualEqual(node) => node.get_parent_id(),
            Self::Token_Greater(node) => node.get_parent_id(),
            Self::Token_GreaterEqual(node) => node.get_parent_id(),
            Self::Token_In(node) => node.get_parent_id(),
            Self::Token_Is(node) => node.get_parent_id(),
            Self::Token_IsNot(node) => node.get_parent_id(),
            Self::Token_NotIn(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::Token_NotEqual(node) => node.get_range(),
            Self::Token_Less(node) => node.get_range(),
            Self::Token_LessEqual(node) => node.get_range(),
            Self::Token_LessGreater(node) => node.get_range(),
            Self::Token_EqualEqual(node) => node.get_range(),
            Self::Token_Greater(node) => node.get_range(),
            Self::Token_GreaterEqual(node) => node.get_range(),
            Self::Token_In(node) => node.get_range(),
            Self::Token_Is(node) => node.get_range(),
            Self::Token_IsNot(node) => node.get_range(),
            Self::Token_NotIn(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Operators_3 {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            69u16 => Ok(Self::Token_NotEqual(Token_NotEqual::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            66u16 => Ok(Self::Token_Less(Token_Less::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            67u16 => Ok(Self::Token_LessEqual(Token_LessEqual::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            72u16 => Ok(Self::Token_LessGreater(Token_LessGreater::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            68u16 => Ok(Self::Token_EqualEqual(Token_EqualEqual::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            71u16 => Ok(Self::Token_Greater(Token_Greater::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            70u16 => Ok(Self::Token_GreaterEqual(Token_GreaterEqual::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            30u16 => Ok(Self::Token_In(Token_In::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            65u16 => Ok(Self::Token_Is(Token_Is::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            195u16 => Ok(Self::Token_IsNot(Token_IsNot::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            194u16 => Ok(Self::Token_NotIn(Token_NotIn::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(Operators_3),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum Operators_2 {
    Token_And(Token_And),
    Token_Or(Token_Or),
}
impl auto_lsp::core::ast::AstNode for Operators_2 {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 56u16 | 57u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::Token_And(node) => node.lower(),
            Self::Token_Or(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::Token_And(node) => node.get_id(),
            Self::Token_Or(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::Token_And(node) => node.get_parent_id(),
            Self::Token_Or(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::Token_And(node) => node.get_range(),
            Self::Token_Or(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Operators_2 {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            56u16 => Ok(Self::Token_And(Token_And::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            57u16 => Ok(Self::Token_Or(Token_Or::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(Operators_2),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum Operators_4 {
    Token_Plus(Token_Plus),
    Token_Minus(Token_Minus),
    Token_Tilde(Token_Tilde),
}
impl auto_lsp::core::ast::AstNode for Operators_4 {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 54u16 | 49u16 | 64u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::Token_Plus(node) => node.lower(),
            Self::Token_Minus(node) => node.lower(),
            Self::Token_Tilde(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::Token_Plus(node) => node.get_id(),
            Self::Token_Minus(node) => node.get_id(),
            Self::Token_Tilde(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::Token_Plus(node) => node.get_parent_id(),
            Self::Token_Minus(node) => node.get_parent_id(),
            Self::Token_Tilde(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::Token_Plus(node) => node.get_range(),
            Self::Token_Minus(node) => node.get_range(),
            Self::Token_Tilde(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Operators_4 {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            54u16 => Ok(Self::Token_Plus(Token_Plus::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            49u16 => Ok(Self::Token_Minus(Token_Minus::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            64u16 => Ok(Self::Token_Tilde(Token_Tilde::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(Operators_4),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum Attribute_Expression_Identifier_Subscript {
    Attribute(Attribute),
    Identifier(Identifier),
    Subscript(Subscript),
    Expression(Expression),
}
impl auto_lsp::core::ast::AstNode for Attribute_Expression_Identifier_Subscript {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(
            node.kind_id(),
            204u16
                | 1u16
                | 205u16
                | 186u16
                | 191u16
                | 196u16
                | 230u16
                | 197u16
                | 123u16
                | 190u16
                | 204u16
                | 238u16
                | 192u16
                | 207u16
                | 231u16
                | 219u16
                | 222u16
                | 88u16
                | 97u16
                | 94u16
                | 224u16
                | 1u16
                | 93u16
                | 216u16
                | 221u16
                | 149u16
                | 98u16
                | 226u16
                | 217u16
                | 223u16
                | 232u16
                | 205u16
                | 96u16
                | 218u16
                | 193u16
        )
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::Attribute(node) => node.lower(),
            Self::Identifier(node) => node.lower(),
            Self::Subscript(node) => node.lower(),
            Self::Expression(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::Attribute(node) => node.get_id(),
            Self::Identifier(node) => node.get_id(),
            Self::Subscript(node) => node.get_id(),
            Self::Expression(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::Attribute(node) => node.get_parent_id(),
            Self::Identifier(node) => node.get_parent_id(),
            Self::Subscript(node) => node.get_parent_id(),
            Self::Expression(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::Attribute(node) => node.get_range(),
            Self::Identifier(node) => node.get_range(),
            Self::Subscript(node) => node.get_range(),
            Self::Expression(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>>
    for Attribute_Expression_Identifier_Subscript
{
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            204u16 => Ok(Self::Attribute(Attribute::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            1u16 => Ok(Self::Identifier(Identifier::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            205u16 => Ok(Self::Subscript(Subscript::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            #[doc = r" Super types"]
            186u16 | 191u16 | 196u16 | 230u16 | 197u16 | 123u16 | 190u16 | 204u16 | 238u16
            | 192u16 | 207u16 | 231u16 | 219u16 | 222u16 | 88u16 | 97u16 | 94u16 | 224u16
            | 1u16 | 93u16 | 216u16 | 221u16 | 149u16 | 98u16 | 226u16 | 217u16 | 223u16
            | 232u16 | 205u16 | 96u16 | 218u16 | 193u16 => Ok(Self::Expression(
                Expression::try_from((node, db, builder, id, parent_id))?,
            )),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(Attribute_Expression_Identifier_Subscript),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum Identifier_TuplePattern {
    Identifier(Identifier),
    TuplePattern(TuplePattern),
}
impl auto_lsp::core::ast::AstNode for Identifier_TuplePattern {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 1u16 | 180u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::Identifier(node) => node.lower(),
            Self::TuplePattern(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::Identifier(node) => node.get_id(),
            Self::TuplePattern(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::Identifier(node) => node.get_parent_id(),
            Self::TuplePattern(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::Identifier(node) => node.get_range(),
            Self::TuplePattern(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Identifier_TuplePattern {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            1u16 => Ok(Self::Identifier(Identifier::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            180u16 => Ok(Self::TuplePattern(TuplePattern::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(Identifier_TuplePattern),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum Identifier_String {
    Identifier(Identifier),
    String(String),
}
impl auto_lsp::core::ast::AstNode for Identifier_String {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 1u16 | 232u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::Identifier(node) => node.lower(),
            Self::String(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::Identifier(node) => node.get_id(),
            Self::String(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::Identifier(node) => node.get_parent_id(),
            Self::String(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::Identifier(node) => node.get_range(),
            Self::String(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Identifier_String {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            1u16 => Ok(Self::Identifier(Identifier::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            232u16 => Ok(Self::String(String::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(Identifier_String),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum ClassDefinition_FunctionDefinition {
    ClassDefinition(ClassDefinition),
    FunctionDefinition(FunctionDefinition),
}
impl auto_lsp::core::ast::AstNode for ClassDefinition_FunctionDefinition {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 155u16 | 146u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::ClassDefinition(node) => node.lower(),
            Self::FunctionDefinition(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::ClassDefinition(node) => node.get_id(),
            Self::FunctionDefinition(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::ClassDefinition(node) => node.get_parent_id(),
            Self::FunctionDefinition(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::ClassDefinition(node) => node.get_range(),
            Self::FunctionDefinition(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ClassDefinition_FunctionDefinition {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            155u16 => Ok(Self::ClassDefinition(ClassDefinition::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            146u16 => Ok(Self::FunctionDefinition(FunctionDefinition::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(ClassDefinition_FunctionDefinition),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum Identifier_TypeParameter {
    Identifier(Identifier),
    TypeParameter(TypeParameter),
}
impl auto_lsp::core::ast::AstNode for Identifier_TypeParameter {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 1u16 | 156u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::Identifier(node) => node.lower(),
            Self::TypeParameter(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::Identifier(node) => node.get_id(),
            Self::TypeParameter(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::Identifier(node) => node.get_parent_id(),
            Self::TypeParameter(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::Identifier(node) => node.get_range(),
            Self::TypeParameter(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Identifier_TypeParameter {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            1u16 => Ok(Self::Identifier(Identifier::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            156u16 => Ok(Self::TypeParameter(TypeParameter::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(Identifier_TypeParameter),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum CasePattern_DottedName {
    CasePattern(CasePattern),
    DottedName(DottedName),
}
impl auto_lsp::core::ast::AstNode for CasePattern_DottedName {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 164u16 | 163u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::CasePattern(node) => node.lower(),
            Self::DottedName(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::CasePattern(node) => node.get_id(),
            Self::DottedName(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::CasePattern(node) => node.get_parent_id(),
            Self::DottedName(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::CasePattern(node) => node.get_range(),
            Self::DottedName(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for CasePattern_DottedName {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            164u16 => Ok(Self::CasePattern(CasePattern::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            163u16 => Ok(Self::DottedName(DottedName::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(CasePattern_DottedName),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum Block_Expression {
    Block(Block),
    Expression(Expression),
}
impl auto_lsp::core::ast::AstNode for Block_Expression {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(
            node.kind_id(),
            161u16
                | 186u16
                | 191u16
                | 196u16
                | 230u16
                | 197u16
                | 123u16
                | 190u16
                | 204u16
                | 238u16
                | 192u16
                | 207u16
                | 231u16
                | 219u16
                | 222u16
                | 88u16
                | 97u16
                | 94u16
                | 224u16
                | 1u16
                | 93u16
                | 216u16
                | 221u16
                | 149u16
                | 98u16
                | 226u16
                | 217u16
                | 223u16
                | 232u16
                | 205u16
                | 96u16
                | 218u16
                | 193u16
        )
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::Block(node) => node.lower(),
            Self::Expression(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::Block(node) => node.get_id(),
            Self::Expression(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::Block(node) => node.get_parent_id(),
            Self::Expression(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::Block(node) => node.get_range(),
            Self::Expression(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Block_Expression {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            161u16 => Ok(Self::Block(Block::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            #[doc = r" Super types"]
            186u16 | 191u16 | 196u16 | 230u16 | 197u16 | 123u16 | 190u16 | 204u16 | 238u16
            | 192u16 | 207u16 | 231u16 | 219u16 | 222u16 | 88u16 | 97u16 | 94u16 | 224u16
            | 1u16 | 93u16 | 216u16 | 221u16 | 149u16 | 98u16 | 226u16 | 217u16 | 223u16
            | 232u16 | 205u16 | 96u16 | 218u16 | 193u16 => Ok(Self::Expression(
                Expression::try_from((node, db, builder, id, parent_id))?,
            )),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(Block_Expression),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum ForInClause_IfClause {
    ForInClause(ForInClause),
    IfClause(IfClause),
}
impl auto_lsp::core::ast::AstNode for ForInClause_IfClause {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 228u16 | 229u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::ForInClause(node) => node.lower(),
            Self::IfClause(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::ForInClause(node) => node.get_id(),
            Self::IfClause(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::ForInClause(node) => node.get_parent_id(),
            Self::IfClause(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::ForInClause(node) => node.get_range(),
            Self::IfClause(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ForInClause_IfClause {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            228u16 => Ok(Self::ForInClause(ForInClause::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            229u16 => Ok(Self::IfClause(IfClause::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(ForInClause_IfClause),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum Attribute_Identifier_Subscript {
    Attribute(Attribute),
    Identifier(Identifier),
    Subscript(Subscript),
}
impl auto_lsp::core::ast::AstNode for Attribute_Identifier_Subscript {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 204u16 | 1u16 | 205u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::Attribute(node) => node.lower(),
            Self::Identifier(node) => node.lower(),
            Self::Subscript(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::Attribute(node) => node.get_id(),
            Self::Identifier(node) => node.get_id(),
            Self::Subscript(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::Attribute(node) => node.get_parent_id(),
            Self::Identifier(node) => node.get_parent_id(),
            Self::Subscript(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::Attribute(node) => node.get_range(),
            Self::Identifier(node) => node.get_range(),
            Self::Subscript(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Attribute_Identifier_Subscript {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            204u16 => Ok(Self::Attribute(Attribute::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            1u16 => Ok(Self::Identifier(Identifier::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            205u16 => Ok(Self::Subscript(Subscript::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(Attribute_Identifier_Subscript),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum ClassPattern_ComplexPattern_ConcatenatedString_DictPattern_DottedName_False_Float_Identifier_Integer_ListPattern_None_SplatPattern_String_True_TuplePattern_UnionPattern
{
    ClassPattern(ClassPattern),
    ComplexPattern(ComplexPattern),
    ConcatenatedString(ConcatenatedString),
    DictPattern(DictPattern),
    DottedName(DottedName),
    False(False),
    Float(Float),
    Identifier(Identifier),
    Integer(Integer),
    ListPattern(ListPattern),
    None(None),
    SplatPattern(SplatPattern),
    String(String),
    True(True),
    TuplePattern(TuplePattern),
    UnionPattern(UnionPattern),
}
impl auto_lsp :: core :: ast :: AstNode for ClassPattern_ComplexPattern_ConcatenatedString_DictPattern_DottedName_False_Float_Identifier_Integer_ListPattern_None_SplatPattern_String_True_TuplePattern_UnionPattern { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind_id () , 174u16 | 175u16 | 231u16 | 170u16 | 163u16 | 97u16 | 94u16 | 1u16 | 93u16 | 181u16 | 98u16 | 173u16 | 232u16 | 96u16 | 180u16 | 167u16) } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { match self { Self :: ClassPattern (node) => node . lower () , Self :: ComplexPattern (node) => node . lower () , Self :: ConcatenatedString (node) => node . lower () , Self :: DictPattern (node) => node . lower () , Self :: DottedName (node) => node . lower () , Self :: False (node) => node . lower () , Self :: Float (node) => node . lower () , Self :: Identifier (node) => node . lower () , Self :: Integer (node) => node . lower () , Self :: ListPattern (node) => node . lower () , Self :: None (node) => node . lower () , Self :: SplatPattern (node) => node . lower () , Self :: String (node) => node . lower () , Self :: True (node) => node . lower () , Self :: TuplePattern (node) => node . lower () , Self :: UnionPattern (node) => node . lower () } } fn get_id (& self) -> usize { match self { Self :: ClassPattern (node) => node . get_id () , Self :: ComplexPattern (node) => node . get_id () , Self :: ConcatenatedString (node) => node . get_id () , Self :: DictPattern (node) => node . get_id () , Self :: DottedName (node) => node . get_id () , Self :: False (node) => node . get_id () , Self :: Float (node) => node . get_id () , Self :: Identifier (node) => node . get_id () , Self :: Integer (node) => node . get_id () , Self :: ListPattern (node) => node . get_id () , Self :: None (node) => node . get_id () , Self :: SplatPattern (node) => node . get_id () , Self :: String (node) => node . get_id () , Self :: True (node) => node . get_id () , Self :: TuplePattern (node) => node . get_id () , Self :: UnionPattern (node) => node . get_id () } } fn get_parent_id (& self) -> Option < usize > { match self { Self :: ClassPattern (node) => node . get_parent_id () , Self :: ComplexPattern (node) => node . get_parent_id () , Self :: ConcatenatedString (node) => node . get_parent_id () , Self :: DictPattern (node) => node . get_parent_id () , Self :: DottedName (node) => node . get_parent_id () , Self :: False (node) => node . get_parent_id () , Self :: Float (node) => node . get_parent_id () , Self :: Identifier (node) => node . get_parent_id () , Self :: Integer (node) => node . get_parent_id () , Self :: ListPattern (node) => node . get_parent_id () , Self :: None (node) => node . get_parent_id () , Self :: SplatPattern (node) => node . get_parent_id () , Self :: String (node) => node . get_parent_id () , Self :: True (node) => node . get_parent_id () , Self :: TuplePattern (node) => node . get_parent_id () , Self :: UnionPattern (node) => node . get_parent_id () } } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { match self { Self :: ClassPattern (node) => node . get_range () , Self :: ComplexPattern (node) => node . get_range () , Self :: ConcatenatedString (node) => node . get_range () , Self :: DictPattern (node) => node . get_range () , Self :: DottedName (node) => node . get_range () , Self :: False (node) => node . get_range () , Self :: Float (node) => node . get_range () , Self :: Identifier (node) => node . get_range () , Self :: Integer (node) => node . get_range () , Self :: ListPattern (node) => node . get_range () , Self :: None (node) => node . get_range () , Self :: SplatPattern (node) => node . get_range () , Self :: String (node) => node . get_range () , Self :: True (node) => node . get_range () , Self :: TuplePattern (node) => node . get_range () , Self :: UnionPattern (node) => node . get_range () } } }
impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for ClassPattern_ComplexPattern_ConcatenatedString_DictPattern_DottedName_False_Float_Identifier_Integer_ListPattern_None_SplatPattern_String_True_TuplePattern_UnionPattern { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { match node . kind_id () { 174u16 => Ok (Self :: ClassPattern (ClassPattern :: try_from ((node , db , builder , id , parent_id)) ?)) , 175u16 => Ok (Self :: ComplexPattern (ComplexPattern :: try_from ((node , db , builder , id , parent_id)) ?)) , 231u16 => Ok (Self :: ConcatenatedString (ConcatenatedString :: try_from ((node , db , builder , id , parent_id)) ?)) , 170u16 => Ok (Self :: DictPattern (DictPattern :: try_from ((node , db , builder , id , parent_id)) ?)) , 163u16 => Ok (Self :: DottedName (DottedName :: try_from ((node , db , builder , id , parent_id)) ?)) , 97u16 => Ok (Self :: False (False :: try_from ((node , db , builder , id , parent_id)) ?)) , 94u16 => Ok (Self :: Float (Float :: try_from ((node , db , builder , id , parent_id)) ?)) , 1u16 => Ok (Self :: Identifier (Identifier :: try_from ((node , db , builder , id , parent_id)) ?)) , 93u16 => Ok (Self :: Integer (Integer :: try_from ((node , db , builder , id , parent_id)) ?)) , 181u16 => Ok (Self :: ListPattern (ListPattern :: try_from ((node , db , builder , id , parent_id)) ?)) , 98u16 => Ok (Self :: None (None :: try_from ((node , db , builder , id , parent_id)) ?)) , 173u16 => Ok (Self :: SplatPattern (SplatPattern :: try_from ((node , db , builder , id , parent_id)) ?)) , 232u16 => Ok (Self :: String (String :: try_from ((node , db , builder , id , parent_id)) ?)) , 96u16 => Ok (Self :: True (True :: try_from ((node , db , builder , id , parent_id)) ?)) , 180u16 => Ok (Self :: TuplePattern (TuplePattern :: try_from ((node , db , builder , id , parent_id)) ?)) , 167u16 => Ok (Self :: UnionPattern (UnionPattern :: try_from ((node , db , builder , id , parent_id)) ?)) , _ => Err (auto_lsp :: core :: errors :: AstError :: UnexpectedSymbol { range : node . range () , symbol : node . kind () , parent_name : stringify ! (ClassPattern_ComplexPattern_ConcatenatedString_DictPattern_DottedName_False_Float_Identifier_Integer_ListPattern_None_SplatPattern_String_True_TuplePattern_UnionPattern) , }) } } }
#[derive(Debug, Clone, PartialEq)]
pub enum Expression_ListSplat_ParenthesizedExpression_Yield {
    ListSplat(ListSplat),
    ParenthesizedExpression(ParenthesizedExpression),
    Yield(Yield),
    Expression(Expression),
}
impl auto_lsp::core::ast::AstNode for Expression_ListSplat_ParenthesizedExpression_Yield {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(
            node.kind_id(),
            149u16
                | 226u16
                | 203u16
                | 186u16
                | 191u16
                | 196u16
                | 230u16
                | 197u16
                | 123u16
                | 190u16
                | 204u16
                | 238u16
                | 192u16
                | 207u16
                | 231u16
                | 219u16
                | 222u16
                | 88u16
                | 97u16
                | 94u16
                | 224u16
                | 1u16
                | 93u16
                | 216u16
                | 221u16
                | 149u16
                | 98u16
                | 226u16
                | 217u16
                | 223u16
                | 232u16
                | 205u16
                | 96u16
                | 218u16
                | 193u16
        )
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::ListSplat(node) => node.lower(),
            Self::ParenthesizedExpression(node) => node.lower(),
            Self::Yield(node) => node.lower(),
            Self::Expression(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::ListSplat(node) => node.get_id(),
            Self::ParenthesizedExpression(node) => node.get_id(),
            Self::Yield(node) => node.get_id(),
            Self::Expression(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::ListSplat(node) => node.get_parent_id(),
            Self::ParenthesizedExpression(node) => node.get_parent_id(),
            Self::Yield(node) => node.get_parent_id(),
            Self::Expression(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::ListSplat(node) => node.get_range(),
            Self::ParenthesizedExpression(node) => node.get_range(),
            Self::Yield(node) => node.get_range(),
            Self::Expression(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>>
    for Expression_ListSplat_ParenthesizedExpression_Yield
{
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            149u16 => Ok(Self::ListSplat(ListSplat::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            226u16 => Ok(Self::ParenthesizedExpression(
                ParenthesizedExpression::try_from((node, db, builder, id, parent_id))?,
            )),
            203u16 => Ok(Self::Yield(Yield::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            #[doc = r" Super types"]
            186u16 | 191u16 | 196u16 | 230u16 | 197u16 | 123u16 | 190u16 | 204u16 | 238u16
            | 192u16 | 207u16 | 231u16 | 219u16 | 222u16 | 88u16 | 97u16 | 94u16 | 224u16
            | 1u16 | 93u16 | 216u16 | 221u16 | 149u16 | 98u16 | 226u16 | 217u16 | 223u16
            | 232u16 | 205u16 | 96u16 | 218u16 | 193u16 => Ok(Self::Expression(
                Expression::try_from((node, db, builder, id, parent_id))?,
            )),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(Expression_ListSplat_ParenthesizedExpression_Yield),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum ElseClause_ExceptClause_ExceptGroupClause_FinallyClause {
    ElseClause(ElseClause),
    ExceptClause(ExceptClause),
    ExceptGroupClause(ExceptGroupClause),
    FinallyClause(FinallyClause),
}
impl auto_lsp::core::ast::AstNode for ElseClause_ExceptClause_ExceptGroupClause_FinallyClause {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 133u16 | 140u16 | 141u16 | 142u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::ElseClause(node) => node.lower(),
            Self::ExceptClause(node) => node.lower(),
            Self::ExceptGroupClause(node) => node.lower(),
            Self::FinallyClause(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::ElseClause(node) => node.get_id(),
            Self::ExceptClause(node) => node.get_id(),
            Self::ExceptGroupClause(node) => node.get_id(),
            Self::FinallyClause(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::ElseClause(node) => node.get_parent_id(),
            Self::ExceptClause(node) => node.get_parent_id(),
            Self::ExceptGroupClause(node) => node.get_parent_id(),
            Self::FinallyClause(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::ElseClause(node) => node.get_range(),
            Self::ExceptClause(node) => node.get_range(),
            Self::ExceptGroupClause(node) => node.get_range(),
            Self::FinallyClause(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>>
    for ElseClause_ExceptClause_ExceptGroupClause_FinallyClause
{
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            133u16 => Ok(Self::ElseClause(ElseClause::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            140u16 => Ok(Self::ExceptClause(ExceptClause::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            141u16 => Ok(Self::ExceptGroupClause(ExceptGroupClause::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            142u16 => Ok(Self::FinallyClause(FinallyClause::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(ElseClause_ExceptClause_ExceptGroupClause_FinallyClause),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum Expression_ExpressionList_PatternList_Yield {
    ExpressionList(ExpressionList),
    PatternList(PatternList),
    Yield(Yield),
    Expression(Expression),
}
impl auto_lsp::core::ast::AstNode for Expression_ExpressionList_PatternList_Yield {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(
            node.kind_id(),
            162u16
                | 201u16
                | 203u16
                | 186u16
                | 191u16
                | 196u16
                | 230u16
                | 197u16
                | 123u16
                | 190u16
                | 204u16
                | 238u16
                | 192u16
                | 207u16
                | 231u16
                | 219u16
                | 222u16
                | 88u16
                | 97u16
                | 94u16
                | 224u16
                | 1u16
                | 93u16
                | 216u16
                | 221u16
                | 149u16
                | 98u16
                | 226u16
                | 217u16
                | 223u16
                | 232u16
                | 205u16
                | 96u16
                | 218u16
                | 193u16
        )
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::ExpressionList(node) => node.lower(),
            Self::PatternList(node) => node.lower(),
            Self::Yield(node) => node.lower(),
            Self::Expression(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::ExpressionList(node) => node.get_id(),
            Self::PatternList(node) => node.get_id(),
            Self::Yield(node) => node.get_id(),
            Self::Expression(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::ExpressionList(node) => node.get_parent_id(),
            Self::PatternList(node) => node.get_parent_id(),
            Self::Yield(node) => node.get_parent_id(),
            Self::Expression(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::ExpressionList(node) => node.get_range(),
            Self::PatternList(node) => node.get_range(),
            Self::Yield(node) => node.get_range(),
            Self::Expression(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>>
    for Expression_ExpressionList_PatternList_Yield
{
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            162u16 => Ok(Self::ExpressionList(ExpressionList::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            201u16 => Ok(Self::PatternList(PatternList::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            203u16 => Ok(Self::Yield(Yield::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            #[doc = r" Super types"]
            186u16 | 191u16 | 196u16 | 230u16 | 197u16 | 123u16 | 190u16 | 204u16 | 238u16
            | 192u16 | 207u16 | 231u16 | 219u16 | 222u16 | 88u16 | 97u16 | 94u16 | 224u16
            | 1u16 | 93u16 | 216u16 | 221u16 | 149u16 | 98u16 | 226u16 | 217u16 | 223u16
            | 232u16 | 205u16 | 96u16 | 218u16 | 193u16 => Ok(Self::Expression(
                Expression::try_from((node, db, builder, id, parent_id))?,
            )),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(Expression_ExpressionList_PatternList_Yield),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum ConstrainedType_Expression_GenericType_MemberType_SplatType_UnionType {
    ConstrainedType(ConstrainedType),
    GenericType(GenericType),
    MemberType(MemberType),
    SplatType(SplatType),
    UnionType(UnionType),
    Expression(Expression),
}
impl auto_lsp::core::ast::AstNode
    for ConstrainedType_Expression_GenericType_MemberType_SplatType_UnionType
{
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(
            node.kind_id(),
            213u16
                | 211u16
                | 214u16
                | 210u16
                | 212u16
                | 186u16
                | 191u16
                | 196u16
                | 230u16
                | 197u16
                | 123u16
                | 190u16
                | 204u16
                | 238u16
                | 192u16
                | 207u16
                | 231u16
                | 219u16
                | 222u16
                | 88u16
                | 97u16
                | 94u16
                | 224u16
                | 1u16
                | 93u16
                | 216u16
                | 221u16
                | 149u16
                | 98u16
                | 226u16
                | 217u16
                | 223u16
                | 232u16
                | 205u16
                | 96u16
                | 218u16
                | 193u16
        )
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::ConstrainedType(node) => node.lower(),
            Self::GenericType(node) => node.lower(),
            Self::MemberType(node) => node.lower(),
            Self::SplatType(node) => node.lower(),
            Self::UnionType(node) => node.lower(),
            Self::Expression(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::ConstrainedType(node) => node.get_id(),
            Self::GenericType(node) => node.get_id(),
            Self::MemberType(node) => node.get_id(),
            Self::SplatType(node) => node.get_id(),
            Self::UnionType(node) => node.get_id(),
            Self::Expression(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::ConstrainedType(node) => node.get_parent_id(),
            Self::GenericType(node) => node.get_parent_id(),
            Self::MemberType(node) => node.get_parent_id(),
            Self::SplatType(node) => node.get_parent_id(),
            Self::UnionType(node) => node.get_parent_id(),
            Self::Expression(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::ConstrainedType(node) => node.get_range(),
            Self::GenericType(node) => node.get_range(),
            Self::MemberType(node) => node.get_range(),
            Self::SplatType(node) => node.get_range(),
            Self::UnionType(node) => node.get_range(),
            Self::Expression(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>>
    for ConstrainedType_Expression_GenericType_MemberType_SplatType_UnionType
{
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            213u16 => Ok(Self::ConstrainedType(ConstrainedType::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            211u16 => Ok(Self::GenericType(GenericType::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            214u16 => Ok(Self::MemberType(MemberType::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            210u16 => Ok(Self::SplatType(SplatType::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            212u16 => Ok(Self::UnionType(UnionType::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            #[doc = r" Super types"]
            186u16 | 191u16 | 196u16 | 230u16 | 197u16 | 123u16 | 190u16 | 204u16 | 238u16
            | 192u16 | 207u16 | 231u16 | 219u16 | 222u16 | 88u16 | 97u16 | 94u16 | 224u16
            | 1u16 | 93u16 | 216u16 | 221u16 | 149u16 | 98u16 | 226u16 | 217u16 | 223u16
            | 232u16 | 205u16 | 96u16 | 218u16 | 193u16 => Ok(Self::Expression(
                Expression::try_from((node, db, builder, id, parent_id))?,
            )),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(
                    ConstrainedType_Expression_GenericType_MemberType_SplatType_UnionType
                ),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum Expression_ExpressionList {
    ExpressionList(ExpressionList),
    Expression(Expression),
}
impl auto_lsp::core::ast::AstNode for Expression_ExpressionList {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(
            node.kind_id(),
            162u16
                | 186u16
                | 191u16
                | 196u16
                | 230u16
                | 197u16
                | 123u16
                | 190u16
                | 204u16
                | 238u16
                | 192u16
                | 207u16
                | 231u16
                | 219u16
                | 222u16
                | 88u16
                | 97u16
                | 94u16
                | 224u16
                | 1u16
                | 93u16
                | 216u16
                | 221u16
                | 149u16
                | 98u16
                | 226u16
                | 217u16
                | 223u16
                | 232u16
                | 205u16
                | 96u16
                | 218u16
                | 193u16
        )
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::ExpressionList(node) => node.lower(),
            Self::Expression(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::ExpressionList(node) => node.get_id(),
            Self::Expression(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::ExpressionList(node) => node.get_parent_id(),
            Self::Expression(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::ExpressionList(node) => node.get_range(),
            Self::Expression(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Expression_ExpressionList {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            162u16 => Ok(Self::ExpressionList(ExpressionList::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            #[doc = r" Super types"]
            186u16 | 191u16 | 196u16 | 230u16 | 197u16 | 123u16 | 190u16 | 204u16 | 238u16
            | 192u16 | 207u16 | 231u16 | 219u16 | 222u16 | 88u16 | 97u16 | 94u16 | 224u16
            | 1u16 | 93u16 | 216u16 | 221u16 | 149u16 | 98u16 | 226u16 | 217u16 | 223u16
            | 232u16 | 205u16 | 96u16 | 218u16 | 193u16 => Ok(Self::Expression(
                Expression::try_from((node, db, builder, id, parent_id))?,
            )),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(Expression_ExpressionList),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum Expression_Slice {
    Slice(Slice),
    Expression(Expression),
}
impl auto_lsp::core::ast::AstNode for Expression_Slice {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(
            node.kind_id(),
            206u16
                | 186u16
                | 191u16
                | 196u16
                | 230u16
                | 197u16
                | 123u16
                | 190u16
                | 204u16
                | 238u16
                | 192u16
                | 207u16
                | 231u16
                | 219u16
                | 222u16
                | 88u16
                | 97u16
                | 94u16
                | 224u16
                | 1u16
                | 93u16
                | 216u16
                | 221u16
                | 149u16
                | 98u16
                | 226u16
                | 217u16
                | 223u16
                | 232u16
                | 205u16
                | 96u16
                | 218u16
                | 193u16
        )
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::Slice(node) => node.lower(),
            Self::Expression(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::Slice(node) => node.get_id(),
            Self::Expression(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::Slice(node) => node.get_parent_id(),
            Self::Expression(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::Slice(node) => node.get_range(),
            Self::Expression(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Expression_Slice {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            206u16 => Ok(Self::Slice(Slice::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            #[doc = r" Super types"]
            186u16 | 191u16 | 196u16 | 230u16 | 197u16 | 123u16 | 190u16 | 204u16 | 238u16
            | 192u16 | 207u16 | 231u16 | 219u16 | 222u16 | 88u16 | 97u16 | 94u16 | 224u16
            | 1u16 | 93u16 | 216u16 | 221u16 | 149u16 | 98u16 | 226u16 | 217u16 | 223u16
            | 232u16 | 205u16 | 96u16 | 218u16 | 193u16 => Ok(Self::Expression(
                Expression::try_from((node, db, builder, id, parent_id))?,
            )),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(Expression_Slice),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum Comma_Expression {
    Token_Comma(Token_Comma),
    Expression(Expression),
}
impl auto_lsp::core::ast::AstNode for Comma_Expression {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(
            node.kind_id(),
            9u16 | 186u16
                | 191u16
                | 196u16
                | 230u16
                | 197u16
                | 123u16
                | 190u16
                | 204u16
                | 238u16
                | 192u16
                | 207u16
                | 231u16
                | 219u16
                | 222u16
                | 88u16
                | 97u16
                | 94u16
                | 224u16
                | 1u16
                | 93u16
                | 216u16
                | 221u16
                | 149u16
                | 98u16
                | 226u16
                | 217u16
                | 223u16
                | 232u16
                | 205u16
                | 96u16
                | 218u16
                | 193u16
        )
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::Token_Comma(node) => node.lower(),
            Self::Expression(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::Token_Comma(node) => node.get_id(),
            Self::Expression(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::Token_Comma(node) => node.get_parent_id(),
            Self::Expression(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::Token_Comma(node) => node.get_range(),
            Self::Expression(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Comma_Expression {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            9u16 => Ok(Self::Token_Comma(Token_Comma::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            #[doc = r" Super types"]
            186u16 | 191u16 | 196u16 | 230u16 | 197u16 | 123u16 | 190u16 | 204u16 | 238u16
            | 192u16 | 207u16 | 231u16 | 219u16 | 222u16 | 88u16 | 97u16 | 94u16 | 224u16
            | 1u16 | 93u16 | 216u16 | 221u16 | 149u16 | 98u16 | 226u16 | 217u16 | 223u16
            | 232u16 | 205u16 | 96u16 | 218u16 | 193u16 => Ok(Self::Expression(
                Expression::try_from((node, db, builder, id, parent_id))?,
            )),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(Comma_Expression),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum DottedName_RelativeImport {
    DottedName(DottedName),
    RelativeImport(RelativeImport),
}
impl auto_lsp::core::ast::AstNode for DottedName_RelativeImport {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 163u16 | 113u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::DottedName(node) => node.lower(),
            Self::RelativeImport(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::DottedName(node) => node.get_id(),
            Self::RelativeImport(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::DottedName(node) => node.get_parent_id(),
            Self::RelativeImport(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::DottedName(node) => node.get_range(),
            Self::RelativeImport(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for DottedName_RelativeImport {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            163u16 => Ok(Self::DottedName(DottedName::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            113u16 => Ok(Self::RelativeImport(RelativeImport::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(DottedName_RelativeImport),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum Float_Integer {
    Float(Float),
    Integer(Integer),
}
impl auto_lsp::core::ast::AstNode for Float_Integer {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 94u16 | 93u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::Float(node) => node.lower(),
            Self::Integer(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::Float(node) => node.get_id(),
            Self::Integer(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::Float(node) => node.get_parent_id(),
            Self::Integer(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::Float(node) => node.get_range(),
            Self::Integer(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Float_Integer {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            94u16 => Ok(Self::Float(Float::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            93u16 => Ok(Self::Integer(Integer::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(Float_Integer),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum Identifier_Type {
    Identifier(Identifier),
    Type(Type),
}
impl auto_lsp::core::ast::AstNode for Identifier_Type {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 1u16 | 209u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::Identifier(node) => node.lower(),
            Self::Type(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::Identifier(node) => node.get_id(),
            Self::Type(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::Identifier(node) => node.get_parent_id(),
            Self::Type(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::Identifier(node) => node.get_range(),
            Self::Type(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Identifier_Type {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            1u16 => Ok(Self::Identifier(Identifier::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            209u16 => Ok(Self::Type(Type::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(Identifier_Type),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum ClassPattern_ComplexPattern_ConcatenatedString_DictPattern_DottedName_False_Float_Integer_ListPattern_None_SplatPattern_String_True_TuplePattern_UnionPattern
{
    ClassPattern(ClassPattern),
    ComplexPattern(ComplexPattern),
    ConcatenatedString(ConcatenatedString),
    DictPattern(DictPattern),
    DottedName(DottedName),
    False(False),
    Float(Float),
    Integer(Integer),
    ListPattern(ListPattern),
    None(None),
    SplatPattern(SplatPattern),
    String(String),
    True(True),
    TuplePattern(TuplePattern),
    UnionPattern(UnionPattern),
}
impl auto_lsp :: core :: ast :: AstNode for ClassPattern_ComplexPattern_ConcatenatedString_DictPattern_DottedName_False_Float_Integer_ListPattern_None_SplatPattern_String_True_TuplePattern_UnionPattern { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind_id () , 174u16 | 175u16 | 231u16 | 170u16 | 163u16 | 97u16 | 94u16 | 93u16 | 181u16 | 98u16 | 173u16 | 232u16 | 96u16 | 180u16 | 167u16) } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { match self { Self :: ClassPattern (node) => node . lower () , Self :: ComplexPattern (node) => node . lower () , Self :: ConcatenatedString (node) => node . lower () , Self :: DictPattern (node) => node . lower () , Self :: DottedName (node) => node . lower () , Self :: False (node) => node . lower () , Self :: Float (node) => node . lower () , Self :: Integer (node) => node . lower () , Self :: ListPattern (node) => node . lower () , Self :: None (node) => node . lower () , Self :: SplatPattern (node) => node . lower () , Self :: String (node) => node . lower () , Self :: True (node) => node . lower () , Self :: TuplePattern (node) => node . lower () , Self :: UnionPattern (node) => node . lower () } } fn get_id (& self) -> usize { match self { Self :: ClassPattern (node) => node . get_id () , Self :: ComplexPattern (node) => node . get_id () , Self :: ConcatenatedString (node) => node . get_id () , Self :: DictPattern (node) => node . get_id () , Self :: DottedName (node) => node . get_id () , Self :: False (node) => node . get_id () , Self :: Float (node) => node . get_id () , Self :: Integer (node) => node . get_id () , Self :: ListPattern (node) => node . get_id () , Self :: None (node) => node . get_id () , Self :: SplatPattern (node) => node . get_id () , Self :: String (node) => node . get_id () , Self :: True (node) => node . get_id () , Self :: TuplePattern (node) => node . get_id () , Self :: UnionPattern (node) => node . get_id () } } fn get_parent_id (& self) -> Option < usize > { match self { Self :: ClassPattern (node) => node . get_parent_id () , Self :: ComplexPattern (node) => node . get_parent_id () , Self :: ConcatenatedString (node) => node . get_parent_id () , Self :: DictPattern (node) => node . get_parent_id () , Self :: DottedName (node) => node . get_parent_id () , Self :: False (node) => node . get_parent_id () , Self :: Float (node) => node . get_parent_id () , Self :: Integer (node) => node . get_parent_id () , Self :: ListPattern (node) => node . get_parent_id () , Self :: None (node) => node . get_parent_id () , Self :: SplatPattern (node) => node . get_parent_id () , Self :: String (node) => node . get_parent_id () , Self :: True (node) => node . get_parent_id () , Self :: TuplePattern (node) => node . get_parent_id () , Self :: UnionPattern (node) => node . get_parent_id () } } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { match self { Self :: ClassPattern (node) => node . get_range () , Self :: ComplexPattern (node) => node . get_range () , Self :: ConcatenatedString (node) => node . get_range () , Self :: DictPattern (node) => node . get_range () , Self :: DottedName (node) => node . get_range () , Self :: False (node) => node . get_range () , Self :: Float (node) => node . get_range () , Self :: Integer (node) => node . get_range () , Self :: ListPattern (node) => node . get_range () , Self :: None (node) => node . get_range () , Self :: SplatPattern (node) => node . get_range () , Self :: String (node) => node . get_range () , Self :: True (node) => node . get_range () , Self :: TuplePattern (node) => node . get_range () , Self :: UnionPattern (node) => node . get_range () } } }
impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for ClassPattern_ComplexPattern_ConcatenatedString_DictPattern_DottedName_False_Float_Integer_ListPattern_None_SplatPattern_String_True_TuplePattern_UnionPattern { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { match node . kind_id () { 174u16 => Ok (Self :: ClassPattern (ClassPattern :: try_from ((node , db , builder , id , parent_id)) ?)) , 175u16 => Ok (Self :: ComplexPattern (ComplexPattern :: try_from ((node , db , builder , id , parent_id)) ?)) , 231u16 => Ok (Self :: ConcatenatedString (ConcatenatedString :: try_from ((node , db , builder , id , parent_id)) ?)) , 170u16 => Ok (Self :: DictPattern (DictPattern :: try_from ((node , db , builder , id , parent_id)) ?)) , 163u16 => Ok (Self :: DottedName (DottedName :: try_from ((node , db , builder , id , parent_id)) ?)) , 97u16 => Ok (Self :: False (False :: try_from ((node , db , builder , id , parent_id)) ?)) , 94u16 => Ok (Self :: Float (Float :: try_from ((node , db , builder , id , parent_id)) ?)) , 93u16 => Ok (Self :: Integer (Integer :: try_from ((node , db , builder , id , parent_id)) ?)) , 181u16 => Ok (Self :: ListPattern (ListPattern :: try_from ((node , db , builder , id , parent_id)) ?)) , 98u16 => Ok (Self :: None (None :: try_from ((node , db , builder , id , parent_id)) ?)) , 173u16 => Ok (Self :: SplatPattern (SplatPattern :: try_from ((node , db , builder , id , parent_id)) ?)) , 232u16 => Ok (Self :: String (String :: try_from ((node , db , builder , id , parent_id)) ?)) , 96u16 => Ok (Self :: True (True :: try_from ((node , db , builder , id , parent_id)) ?)) , 180u16 => Ok (Self :: TuplePattern (TuplePattern :: try_from ((node , db , builder , id , parent_id)) ?)) , 167u16 => Ok (Self :: UnionPattern (UnionPattern :: try_from ((node , db , builder , id , parent_id)) ?)) , _ => Err (auto_lsp :: core :: errors :: AstError :: UnexpectedSymbol { range : node . range () , symbol : node . kind () , parent_name : stringify ! (ClassPattern_ComplexPattern_ConcatenatedString_DictPattern_DottedName_False_Float_Integer_ListPattern_None_SplatPattern_String_True_TuplePattern_UnionPattern) , }) } } }
#[derive(Debug, Clone, PartialEq)]
pub enum CasePattern_Expression_Identifier {
    CasePattern(CasePattern),
    Identifier(Identifier),
    Expression(Expression),
}
impl auto_lsp::core::ast::AstNode for CasePattern_Expression_Identifier {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(
            node.kind_id(),
            164u16
                | 1u16
                | 186u16
                | 191u16
                | 196u16
                | 230u16
                | 197u16
                | 123u16
                | 190u16
                | 204u16
                | 238u16
                | 192u16
                | 207u16
                | 231u16
                | 219u16
                | 222u16
                | 88u16
                | 97u16
                | 94u16
                | 224u16
                | 1u16
                | 93u16
                | 216u16
                | 221u16
                | 149u16
                | 98u16
                | 226u16
                | 217u16
                | 223u16
                | 232u16
                | 205u16
                | 96u16
                | 218u16
                | 193u16
        )
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::CasePattern(node) => node.lower(),
            Self::Identifier(node) => node.lower(),
            Self::Expression(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::CasePattern(node) => node.get_id(),
            Self::Identifier(node) => node.get_id(),
            Self::Expression(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::CasePattern(node) => node.get_parent_id(),
            Self::Identifier(node) => node.get_parent_id(),
            Self::Expression(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::CasePattern(node) => node.get_range(),
            Self::Identifier(node) => node.get_range(),
            Self::Expression(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for CasePattern_Expression_Identifier {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            164u16 => Ok(Self::CasePattern(CasePattern::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            1u16 => Ok(Self::Identifier(Identifier::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            #[doc = r" Super types"]
            186u16 | 191u16 | 196u16 | 230u16 | 197u16 | 123u16 | 190u16 | 204u16 | 238u16
            | 192u16 | 207u16 | 231u16 | 219u16 | 222u16 | 88u16 | 97u16 | 94u16 | 224u16
            | 1u16 | 93u16 | 216u16 | 221u16 | 149u16 | 98u16 | 226u16 | 217u16 | 223u16
            | 232u16 | 205u16 | 96u16 | 218u16 | 193u16 => Ok(Self::Expression(
                Expression::try_from((node, db, builder, id, parent_id))?,
            )),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(CasePattern_Expression_Identifier),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum Assignment_AugmentedAssignment_Expression_Yield {
    Assignment(Assignment),
    AugmentedAssignment(AugmentedAssignment),
    Yield(Yield),
    Expression(Expression),
}
impl auto_lsp::core::ast::AstNode for Assignment_AugmentedAssignment_Expression_Yield {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(
            node.kind_id(),
            199u16
                | 200u16
                | 203u16
                | 186u16
                | 191u16
                | 196u16
                | 230u16
                | 197u16
                | 123u16
                | 190u16
                | 204u16
                | 238u16
                | 192u16
                | 207u16
                | 231u16
                | 219u16
                | 222u16
                | 88u16
                | 97u16
                | 94u16
                | 224u16
                | 1u16
                | 93u16
                | 216u16
                | 221u16
                | 149u16
                | 98u16
                | 226u16
                | 217u16
                | 223u16
                | 232u16
                | 205u16
                | 96u16
                | 218u16
                | 193u16
        )
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::Assignment(node) => node.lower(),
            Self::AugmentedAssignment(node) => node.lower(),
            Self::Yield(node) => node.lower(),
            Self::Expression(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::Assignment(node) => node.get_id(),
            Self::AugmentedAssignment(node) => node.get_id(),
            Self::Yield(node) => node.get_id(),
            Self::Expression(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::Assignment(node) => node.get_parent_id(),
            Self::AugmentedAssignment(node) => node.get_parent_id(),
            Self::Yield(node) => node.get_parent_id(),
            Self::Expression(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::Assignment(node) => node.get_range(),
            Self::AugmentedAssignment(node) => node.get_range(),
            Self::Yield(node) => node.get_range(),
            Self::Expression(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>>
    for Assignment_AugmentedAssignment_Expression_Yield
{
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            199u16 => Ok(Self::Assignment(Assignment::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            200u16 => Ok(Self::AugmentedAssignment(AugmentedAssignment::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            203u16 => Ok(Self::Yield(Yield::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            #[doc = r" Super types"]
            186u16 | 191u16 | 196u16 | 230u16 | 197u16 | 123u16 | 190u16 | 204u16 | 238u16
            | 192u16 | 207u16 | 231u16 | 219u16 | 222u16 | 88u16 | 97u16 | 94u16 | 224u16
            | 1u16 | 93u16 | 216u16 | 221u16 | 149u16 | 98u16 | 226u16 | 217u16 | 223u16
            | 232u16 | 205u16 | 96u16 | 218u16 | 193u16 => Ok(Self::Expression(
                Expression::try_from((node, db, builder, id, parent_id))?,
            )),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(Assignment_AugmentedAssignment_Expression_Yield),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum DictionarySplat_Pair {
    DictionarySplat(DictionarySplat),
    Pair(Pair),
}
impl auto_lsp::core::ast::AstNode for DictionarySplat_Pair {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 150u16 | 220u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::DictionarySplat(node) => node.lower(),
            Self::Pair(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::DictionarySplat(node) => node.get_id(),
            Self::Pair(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::DictionarySplat(node) => node.get_parent_id(),
            Self::Pair(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::DictionarySplat(node) => node.get_range(),
            Self::Pair(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for DictionarySplat_Pair {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            150u16 => Ok(Self::DictionarySplat(DictionarySplat::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            220u16 => Ok(Self::Pair(Pair::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(DictionarySplat_Pair),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum Expression_ListSplat_ParenthesizedListSplat_Yield {
    ListSplat(ListSplat),
    ParenthesizedListSplat(ParenthesizedListSplat),
    Yield(Yield),
    Expression(Expression),
}
impl auto_lsp::core::ast::AstNode for Expression_ListSplat_ParenthesizedListSplat_Yield {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(
            node.kind_id(),
            149u16
                | 157u16
                | 203u16
                | 186u16
                | 191u16
                | 196u16
                | 230u16
                | 197u16
                | 123u16
                | 190u16
                | 204u16
                | 238u16
                | 192u16
                | 207u16
                | 231u16
                | 219u16
                | 222u16
                | 88u16
                | 97u16
                | 94u16
                | 224u16
                | 1u16
                | 93u16
                | 216u16
                | 221u16
                | 149u16
                | 98u16
                | 226u16
                | 217u16
                | 223u16
                | 232u16
                | 205u16
                | 96u16
                | 218u16
                | 193u16
        )
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::ListSplat(node) => node.lower(),
            Self::ParenthesizedListSplat(node) => node.lower(),
            Self::Yield(node) => node.lower(),
            Self::Expression(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::ListSplat(node) => node.get_id(),
            Self::ParenthesizedListSplat(node) => node.get_id(),
            Self::Yield(node) => node.get_id(),
            Self::Expression(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::ListSplat(node) => node.get_parent_id(),
            Self::ParenthesizedListSplat(node) => node.get_parent_id(),
            Self::Yield(node) => node.get_parent_id(),
            Self::Expression(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::ListSplat(node) => node.get_range(),
            Self::ParenthesizedListSplat(node) => node.get_range(),
            Self::Yield(node) => node.get_range(),
            Self::Expression(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>>
    for Expression_ListSplat_ParenthesizedListSplat_Yield
{
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            149u16 => Ok(Self::ListSplat(ListSplat::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            157u16 => Ok(Self::ParenthesizedListSplat(
                ParenthesizedListSplat::try_from((node, db, builder, id, parent_id))?,
            )),
            203u16 => Ok(Self::Yield(Yield::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            #[doc = r" Super types"]
            186u16 | 191u16 | 196u16 | 230u16 | 197u16 | 123u16 | 190u16 | 204u16 | 238u16
            | 192u16 | 207u16 | 231u16 | 219u16 | 222u16 | 88u16 | 97u16 | 94u16 | 224u16
            | 1u16 | 93u16 | 216u16 | 221u16 | 149u16 | 98u16 | 226u16 | 217u16 | 223u16
            | 232u16 | 205u16 | 96u16 | 218u16 | 193u16 => Ok(Self::Expression(
                Expression::try_from((node, db, builder, id, parent_id))?,
            )),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(Expression_ListSplat_ParenthesizedListSplat_Yield),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum AsPattern_ClassPattern_ComplexPattern_ConcatenatedString_DictPattern_DottedName_False_Float_Integer_KeywordPattern_ListPattern_None_SplatPattern_String_True_TuplePattern_UnionPattern
{
    AsPattern(AsPattern),
    ClassPattern(ClassPattern),
    ComplexPattern(ComplexPattern),
    ConcatenatedString(ConcatenatedString),
    DictPattern(DictPattern),
    DottedName(DottedName),
    False(False),
    Float(Float),
    Integer(Integer),
    KeywordPattern(KeywordPattern),
    ListPattern(ListPattern),
    None(None),
    SplatPattern(SplatPattern),
    String(String),
    True(True),
    TuplePattern(TuplePattern),
    UnionPattern(UnionPattern),
}
impl auto_lsp :: core :: ast :: AstNode for AsPattern_ClassPattern_ComplexPattern_ConcatenatedString_DictPattern_DottedName_False_Float_Integer_KeywordPattern_ListPattern_None_SplatPattern_String_True_TuplePattern_UnionPattern { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind_id () , 186u16 | 174u16 | 175u16 | 231u16 | 170u16 | 163u16 | 97u16 | 94u16 | 93u16 | 172u16 | 181u16 | 98u16 | 173u16 | 232u16 | 96u16 | 180u16 | 167u16) } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { match self { Self :: AsPattern (node) => node . lower () , Self :: ClassPattern (node) => node . lower () , Self :: ComplexPattern (node) => node . lower () , Self :: ConcatenatedString (node) => node . lower () , Self :: DictPattern (node) => node . lower () , Self :: DottedName (node) => node . lower () , Self :: False (node) => node . lower () , Self :: Float (node) => node . lower () , Self :: Integer (node) => node . lower () , Self :: KeywordPattern (node) => node . lower () , Self :: ListPattern (node) => node . lower () , Self :: None (node) => node . lower () , Self :: SplatPattern (node) => node . lower () , Self :: String (node) => node . lower () , Self :: True (node) => node . lower () , Self :: TuplePattern (node) => node . lower () , Self :: UnionPattern (node) => node . lower () } } fn get_id (& self) -> usize { match self { Self :: AsPattern (node) => node . get_id () , Self :: ClassPattern (node) => node . get_id () , Self :: ComplexPattern (node) => node . get_id () , Self :: ConcatenatedString (node) => node . get_id () , Self :: DictPattern (node) => node . get_id () , Self :: DottedName (node) => node . get_id () , Self :: False (node) => node . get_id () , Self :: Float (node) => node . get_id () , Self :: Integer (node) => node . get_id () , Self :: KeywordPattern (node) => node . get_id () , Self :: ListPattern (node) => node . get_id () , Self :: None (node) => node . get_id () , Self :: SplatPattern (node) => node . get_id () , Self :: String (node) => node . get_id () , Self :: True (node) => node . get_id () , Self :: TuplePattern (node) => node . get_id () , Self :: UnionPattern (node) => node . get_id () } } fn get_parent_id (& self) -> Option < usize > { match self { Self :: AsPattern (node) => node . get_parent_id () , Self :: ClassPattern (node) => node . get_parent_id () , Self :: ComplexPattern (node) => node . get_parent_id () , Self :: ConcatenatedString (node) => node . get_parent_id () , Self :: DictPattern (node) => node . get_parent_id () , Self :: DottedName (node) => node . get_parent_id () , Self :: False (node) => node . get_parent_id () , Self :: Float (node) => node . get_parent_id () , Self :: Integer (node) => node . get_parent_id () , Self :: KeywordPattern (node) => node . get_parent_id () , Self :: ListPattern (node) => node . get_parent_id () , Self :: None (node) => node . get_parent_id () , Self :: SplatPattern (node) => node . get_parent_id () , Self :: String (node) => node . get_parent_id () , Self :: True (node) => node . get_parent_id () , Self :: TuplePattern (node) => node . get_parent_id () , Self :: UnionPattern (node) => node . get_parent_id () } } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { match self { Self :: AsPattern (node) => node . get_range () , Self :: ClassPattern (node) => node . get_range () , Self :: ComplexPattern (node) => node . get_range () , Self :: ConcatenatedString (node) => node . get_range () , Self :: DictPattern (node) => node . get_range () , Self :: DottedName (node) => node . get_range () , Self :: False (node) => node . get_range () , Self :: Float (node) => node . get_range () , Self :: Integer (node) => node . get_range () , Self :: KeywordPattern (node) => node . get_range () , Self :: ListPattern (node) => node . get_range () , Self :: None (node) => node . get_range () , Self :: SplatPattern (node) => node . get_range () , Self :: String (node) => node . get_range () , Self :: True (node) => node . get_range () , Self :: TuplePattern (node) => node . get_range () , Self :: UnionPattern (node) => node . get_range () } } }
impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for AsPattern_ClassPattern_ComplexPattern_ConcatenatedString_DictPattern_DottedName_False_Float_Integer_KeywordPattern_ListPattern_None_SplatPattern_String_True_TuplePattern_UnionPattern { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { match node . kind_id () { 186u16 => Ok (Self :: AsPattern (AsPattern :: try_from ((node , db , builder , id , parent_id)) ?)) , 174u16 => Ok (Self :: ClassPattern (ClassPattern :: try_from ((node , db , builder , id , parent_id)) ?)) , 175u16 => Ok (Self :: ComplexPattern (ComplexPattern :: try_from ((node , db , builder , id , parent_id)) ?)) , 231u16 => Ok (Self :: ConcatenatedString (ConcatenatedString :: try_from ((node , db , builder , id , parent_id)) ?)) , 170u16 => Ok (Self :: DictPattern (DictPattern :: try_from ((node , db , builder , id , parent_id)) ?)) , 163u16 => Ok (Self :: DottedName (DottedName :: try_from ((node , db , builder , id , parent_id)) ?)) , 97u16 => Ok (Self :: False (False :: try_from ((node , db , builder , id , parent_id)) ?)) , 94u16 => Ok (Self :: Float (Float :: try_from ((node , db , builder , id , parent_id)) ?)) , 93u16 => Ok (Self :: Integer (Integer :: try_from ((node , db , builder , id , parent_id)) ?)) , 172u16 => Ok (Self :: KeywordPattern (KeywordPattern :: try_from ((node , db , builder , id , parent_id)) ?)) , 181u16 => Ok (Self :: ListPattern (ListPattern :: try_from ((node , db , builder , id , parent_id)) ?)) , 98u16 => Ok (Self :: None (None :: try_from ((node , db , builder , id , parent_id)) ?)) , 173u16 => Ok (Self :: SplatPattern (SplatPattern :: try_from ((node , db , builder , id , parent_id)) ?)) , 232u16 => Ok (Self :: String (String :: try_from ((node , db , builder , id , parent_id)) ?)) , 96u16 => Ok (Self :: True (True :: try_from ((node , db , builder , id , parent_id)) ?)) , 180u16 => Ok (Self :: TuplePattern (TuplePattern :: try_from ((node , db , builder , id , parent_id)) ?)) , 167u16 => Ok (Self :: UnionPattern (UnionPattern :: try_from ((node , db , builder , id , parent_id)) ?)) , _ => Err (auto_lsp :: core :: errors :: AstError :: UnexpectedSymbol { range : node . range () , symbol : node . kind () , parent_name : stringify ! (AsPattern_ClassPattern_ComplexPattern_ConcatenatedString_DictPattern_DottedName_False_Float_Integer_KeywordPattern_ListPattern_None_SplatPattern_String_True_TuplePattern_UnionPattern) , }) } } }
#[derive(Debug, Clone, PartialEq)]
pub enum ListSplat_ParenthesizedExpression {
    ListSplat(ListSplat),
    ParenthesizedExpression(ParenthesizedExpression),
}
impl auto_lsp::core::ast::AstNode for ListSplat_ParenthesizedExpression {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 149u16 | 226u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::ListSplat(node) => node.lower(),
            Self::ParenthesizedExpression(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::ListSplat(node) => node.get_id(),
            Self::ParenthesizedExpression(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::ListSplat(node) => node.get_parent_id(),
            Self::ParenthesizedExpression(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::ListSplat(node) => node.get_range(),
            Self::ParenthesizedExpression(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ListSplat_ParenthesizedExpression {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            149u16 => Ok(Self::ListSplat(ListSplat::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            226u16 => Ok(Self::ParenthesizedExpression(
                ParenthesizedExpression::try_from((node, db, builder, id, parent_id))?,
            )),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(ListSplat_ParenthesizedExpression),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum Interpolation_StringContent_StringEnd_StringStart {
    Interpolation(Interpolation),
    StringContent(StringContent),
    StringEnd(StringEnd),
    StringStart(StringStart),
}
impl auto_lsp::core::ast::AstNode for Interpolation_StringContent_StringEnd_StringStart {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 234u16 | 233u16 | 107u16 | 104u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::Interpolation(node) => node.lower(),
            Self::StringContent(node) => node.lower(),
            Self::StringEnd(node) => node.lower(),
            Self::StringStart(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::Interpolation(node) => node.get_id(),
            Self::StringContent(node) => node.get_id(),
            Self::StringEnd(node) => node.get_id(),
            Self::StringStart(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::Interpolation(node) => node.get_parent_id(),
            Self::StringContent(node) => node.get_parent_id(),
            Self::StringEnd(node) => node.get_parent_id(),
            Self::StringStart(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::Interpolation(node) => node.get_range(),
            Self::StringContent(node) => node.get_range(),
            Self::StringEnd(node) => node.get_range(),
            Self::StringStart(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>>
    for Interpolation_StringContent_StringEnd_StringStart
{
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            234u16 => Ok(Self::Interpolation(Interpolation::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            233u16 => Ok(Self::StringContent(StringContent::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            107u16 => Ok(Self::StringEnd(StringEnd::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            104u16 => Ok(Self::StringStart(StringStart::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(Interpolation_StringContent_StringEnd_StringStart),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum DictionarySplat_Expression_KeywordArgument_ListSplat_ParenthesizedExpression {
    DictionarySplat(DictionarySplat),
    KeywordArgument(KeywordArgument),
    ListSplat(ListSplat),
    ParenthesizedExpression(ParenthesizedExpression),
    Expression(Expression),
}
impl auto_lsp::core::ast::AstNode
    for DictionarySplat_Expression_KeywordArgument_ListSplat_ParenthesizedExpression
{
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(
            node.kind_id(),
            150u16
                | 215u16
                | 149u16
                | 226u16
                | 186u16
                | 191u16
                | 196u16
                | 230u16
                | 197u16
                | 123u16
                | 190u16
                | 204u16
                | 238u16
                | 192u16
                | 207u16
                | 231u16
                | 219u16
                | 222u16
                | 88u16
                | 97u16
                | 94u16
                | 224u16
                | 1u16
                | 93u16
                | 216u16
                | 221u16
                | 149u16
                | 98u16
                | 226u16
                | 217u16
                | 223u16
                | 232u16
                | 205u16
                | 96u16
                | 218u16
                | 193u16
        )
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::DictionarySplat(node) => node.lower(),
            Self::KeywordArgument(node) => node.lower(),
            Self::ListSplat(node) => node.lower(),
            Self::ParenthesizedExpression(node) => node.lower(),
            Self::Expression(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::DictionarySplat(node) => node.get_id(),
            Self::KeywordArgument(node) => node.get_id(),
            Self::ListSplat(node) => node.get_id(),
            Self::ParenthesizedExpression(node) => node.get_id(),
            Self::Expression(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::DictionarySplat(node) => node.get_parent_id(),
            Self::KeywordArgument(node) => node.get_parent_id(),
            Self::ListSplat(node) => node.get_parent_id(),
            Self::ParenthesizedExpression(node) => node.get_parent_id(),
            Self::Expression(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::DictionarySplat(node) => node.get_range(),
            Self::KeywordArgument(node) => node.get_range(),
            Self::ListSplat(node) => node.get_range(),
            Self::ParenthesizedExpression(node) => node.get_range(),
            Self::Expression(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>>
    for DictionarySplat_Expression_KeywordArgument_ListSplat_ParenthesizedExpression
{
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            150u16 => Ok(Self::DictionarySplat(DictionarySplat::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            215u16 => Ok(Self::KeywordArgument(KeywordArgument::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            149u16 => Ok(Self::ListSplat(ListSplat::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            226u16 => Ok(Self::ParenthesizedExpression(
                ParenthesizedExpression::try_from((node, db, builder, id, parent_id))?,
            )),
            #[doc = r" Super types"]
            186u16 | 191u16 | 196u16 | 230u16 | 197u16 | 123u16 | 190u16 | 204u16 | 238u16
            | 192u16 | 207u16 | 231u16 | 219u16 | 222u16 | 88u16 | 97u16 | 94u16 | 224u16
            | 1u16 | 93u16 | 216u16 | 221u16 | 149u16 | 98u16 | 226u16 | 217u16 | 223u16
            | 232u16 | 205u16 | 96u16 | 218u16 | 193u16 => Ok(Self::Expression(
                Expression::try_from((node, db, builder, id, parent_id))?,
            )),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(
                    DictionarySplat_Expression_KeywordArgument_ListSplat_ParenthesizedExpression
                ),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum EscapeInterpolation_EscapeSequence {
    EscapeInterpolation(EscapeInterpolation),
    EscapeSequence(EscapeSequence),
}
impl auto_lsp::core::ast::AstNode for EscapeInterpolation_EscapeSequence {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 106u16 | 89u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::EscapeInterpolation(node) => node.lower(),
            Self::EscapeSequence(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::EscapeInterpolation(node) => node.get_id(),
            Self::EscapeSequence(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::EscapeInterpolation(node) => node.get_parent_id(),
            Self::EscapeSequence(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::EscapeInterpolation(node) => node.get_range(),
            Self::EscapeSequence(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for EscapeInterpolation_EscapeSequence {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            106u16 => Ok(Self::EscapeInterpolation(EscapeInterpolation::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            89u16 => Ok(Self::EscapeSequence(EscapeSequence::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(EscapeInterpolation_EscapeSequence),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum Assignment_AugmentedAssignment_Expression_ExpressionList_PatternList_Yield {
    Assignment(Assignment),
    AugmentedAssignment(AugmentedAssignment),
    ExpressionList(ExpressionList),
    PatternList(PatternList),
    Yield(Yield),
    Expression(Expression),
}
impl auto_lsp::core::ast::AstNode
    for Assignment_AugmentedAssignment_Expression_ExpressionList_PatternList_Yield
{
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(
            node.kind_id(),
            199u16
                | 200u16
                | 162u16
                | 201u16
                | 203u16
                | 186u16
                | 191u16
                | 196u16
                | 230u16
                | 197u16
                | 123u16
                | 190u16
                | 204u16
                | 238u16
                | 192u16
                | 207u16
                | 231u16
                | 219u16
                | 222u16
                | 88u16
                | 97u16
                | 94u16
                | 224u16
                | 1u16
                | 93u16
                | 216u16
                | 221u16
                | 149u16
                | 98u16
                | 226u16
                | 217u16
                | 223u16
                | 232u16
                | 205u16
                | 96u16
                | 218u16
                | 193u16
        )
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::Assignment(node) => node.lower(),
            Self::AugmentedAssignment(node) => node.lower(),
            Self::ExpressionList(node) => node.lower(),
            Self::PatternList(node) => node.lower(),
            Self::Yield(node) => node.lower(),
            Self::Expression(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::Assignment(node) => node.get_id(),
            Self::AugmentedAssignment(node) => node.get_id(),
            Self::ExpressionList(node) => node.get_id(),
            Self::PatternList(node) => node.get_id(),
            Self::Yield(node) => node.get_id(),
            Self::Expression(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::Assignment(node) => node.get_parent_id(),
            Self::AugmentedAssignment(node) => node.get_parent_id(),
            Self::ExpressionList(node) => node.get_parent_id(),
            Self::PatternList(node) => node.get_parent_id(),
            Self::Yield(node) => node.get_parent_id(),
            Self::Expression(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::Assignment(node) => node.get_range(),
            Self::AugmentedAssignment(node) => node.get_range(),
            Self::ExpressionList(node) => node.get_range(),
            Self::PatternList(node) => node.get_range(),
            Self::Yield(node) => node.get_range(),
            Self::Expression(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>>
    for Assignment_AugmentedAssignment_Expression_ExpressionList_PatternList_Yield
{
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            199u16 => Ok(Self::Assignment(Assignment::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            200u16 => Ok(Self::AugmentedAssignment(AugmentedAssignment::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            162u16 => Ok(Self::ExpressionList(ExpressionList::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            201u16 => Ok(Self::PatternList(PatternList::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            203u16 => Ok(Self::Yield(Yield::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            #[doc = r" Super types"]
            186u16 | 191u16 | 196u16 | 230u16 | 197u16 | 123u16 | 190u16 | 204u16 | 238u16
            | 192u16 | 207u16 | 231u16 | 219u16 | 222u16 | 88u16 | 97u16 | 94u16 | 224u16
            | 1u16 | 93u16 | 216u16 | 221u16 | 149u16 | 98u16 | 226u16 | 217u16 | 223u16
            | 232u16 | 205u16 | 96u16 | 218u16 | 193u16 => Ok(Self::Expression(
                Expression::try_from((node, db, builder, id, parent_id))?,
            )),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(
                    Assignment_AugmentedAssignment_Expression_ExpressionList_PatternList_Yield
                ),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum AliasedImport_DottedName {
    AliasedImport(AliasedImport),
    DottedName(DottedName),
}
impl auto_lsp::core::ast::AstNode for AliasedImport_DottedName {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 117u16 | 163u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::AliasedImport(node) => node.lower(),
            Self::DottedName(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::AliasedImport(node) => node.get_id(),
            Self::DottedName(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::AliasedImport(node) => node.get_parent_id(),
            Self::DottedName(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::AliasedImport(node) => node.get_range(),
            Self::DottedName(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for AliasedImport_DottedName {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            117u16 => Ok(Self::AliasedImport(AliasedImport::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            163u16 => Ok(Self::DottedName(DottedName::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(AliasedImport_DottedName),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum CompoundStatement_SimpleStatement {
    CompoundStatement(CompoundStatement),
    SimpleStatement(SimpleStatement),
}
impl auto_lsp::core::ast::AstNode for CompoundStatement_SimpleStatement {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(
            node.kind_id(),
            155u16
                | 159u16
                | 137u16
                | 146u16
                | 131u16
                | 134u16
                | 139u16
                | 138u16
                | 143u16
                | 121u16
                | 129u16
                | 130u16
                | 126u16
                | 153u16
                | 122u16
                | 114u16
                | 151u16
                | 115u16
                | 111u16
                | 152u16
                | 128u16
                | 119u16
                | 127u16
                | 125u16
                | 154u16
        )
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::CompoundStatement(node) => node.lower(),
            Self::SimpleStatement(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::CompoundStatement(node) => node.get_id(),
            Self::SimpleStatement(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::CompoundStatement(node) => node.get_parent_id(),
            Self::SimpleStatement(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::CompoundStatement(node) => node.get_range(),
            Self::SimpleStatement(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for CompoundStatement_SimpleStatement {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            #[doc = r" Super types"]
            155u16 | 159u16 | 137u16 | 146u16 | 131u16 | 134u16 | 139u16 | 138u16 | 143u16 => {
                Ok(Self::CompoundStatement(CompoundStatement::try_from((
                    node, db, builder, id, parent_id,
                ))?))
            }
            121u16 | 129u16 | 130u16 | 126u16 | 153u16 | 122u16 | 114u16 | 151u16 | 115u16
            | 111u16 | 152u16 | 128u16 | 119u16 | 127u16 | 125u16 | 154u16 => {
                Ok(Self::SimpleStatement(SimpleStatement::try_from((
                    node, db, builder, id, parent_id,
                ))?))
            }
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(CompoundStatement_SimpleStatement),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum DottedName_ImportPrefix {
    DottedName(DottedName),
    ImportPrefix(ImportPrefix),
}
impl auto_lsp::core::ast::AstNode for DottedName_ImportPrefix {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 163u16 | 112u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::DottedName(node) => node.lower(),
            Self::ImportPrefix(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::DottedName(node) => node.get_id(),
            Self::ImportPrefix(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::DottedName(node) => node.get_parent_id(),
            Self::ImportPrefix(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::DottedName(node) => node.get_range(),
            Self::ImportPrefix(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for DottedName_ImportPrefix {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            163u16 => Ok(Self::DottedName(DottedName::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            112u16 => Ok(Self::ImportPrefix(ImportPrefix::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(DottedName_ImportPrefix),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum Minus_Underscore_ClassPattern_ComplexPattern_ConcatenatedString_DictPattern_DottedName_False_Float_Integer_ListPattern_None_SplatPattern_String_True_TuplePattern_UnionPattern
{
    Token_Minus(Token_Minus),
    Token_Underscore(Token_Underscore),
    ClassPattern(ClassPattern),
    ComplexPattern(ComplexPattern),
    ConcatenatedString(ConcatenatedString),
    DictPattern(DictPattern),
    DottedName(DottedName),
    False(False),
    Float(Float),
    Integer(Integer),
    ListPattern(ListPattern),
    None(None),
    SplatPattern(SplatPattern),
    String(String),
    True(True),
    TuplePattern(TuplePattern),
    UnionPattern(UnionPattern),
}
impl auto_lsp :: core :: ast :: AstNode for Minus_Underscore_ClassPattern_ComplexPattern_ConcatenatedString_DictPattern_DottedName_False_Float_Integer_ListPattern_None_SplatPattern_String_True_TuplePattern_UnionPattern { fn contains (node : & auto_lsp :: tree_sitter :: Node) -> bool { matches ! (node . kind_id () , 49u16 | 50u16 | 174u16 | 175u16 | 231u16 | 170u16 | 163u16 | 97u16 | 94u16 | 93u16 | 181u16 | 98u16 | 173u16 | 232u16 | 96u16 | 180u16 | 167u16) } fn lower (& self) -> & dyn auto_lsp :: core :: ast :: AstNode { match self { Self :: Token_Minus (node) => node . lower () , Self :: Token_Underscore (node) => node . lower () , Self :: ClassPattern (node) => node . lower () , Self :: ComplexPattern (node) => node . lower () , Self :: ConcatenatedString (node) => node . lower () , Self :: DictPattern (node) => node . lower () , Self :: DottedName (node) => node . lower () , Self :: False (node) => node . lower () , Self :: Float (node) => node . lower () , Self :: Integer (node) => node . lower () , Self :: ListPattern (node) => node . lower () , Self :: None (node) => node . lower () , Self :: SplatPattern (node) => node . lower () , Self :: String (node) => node . lower () , Self :: True (node) => node . lower () , Self :: TuplePattern (node) => node . lower () , Self :: UnionPattern (node) => node . lower () } } fn get_id (& self) -> usize { match self { Self :: Token_Minus (node) => node . get_id () , Self :: Token_Underscore (node) => node . get_id () , Self :: ClassPattern (node) => node . get_id () , Self :: ComplexPattern (node) => node . get_id () , Self :: ConcatenatedString (node) => node . get_id () , Self :: DictPattern (node) => node . get_id () , Self :: DottedName (node) => node . get_id () , Self :: False (node) => node . get_id () , Self :: Float (node) => node . get_id () , Self :: Integer (node) => node . get_id () , Self :: ListPattern (node) => node . get_id () , Self :: None (node) => node . get_id () , Self :: SplatPattern (node) => node . get_id () , Self :: String (node) => node . get_id () , Self :: True (node) => node . get_id () , Self :: TuplePattern (node) => node . get_id () , Self :: UnionPattern (node) => node . get_id () } } fn get_parent_id (& self) -> Option < usize > { match self { Self :: Token_Minus (node) => node . get_parent_id () , Self :: Token_Underscore (node) => node . get_parent_id () , Self :: ClassPattern (node) => node . get_parent_id () , Self :: ComplexPattern (node) => node . get_parent_id () , Self :: ConcatenatedString (node) => node . get_parent_id () , Self :: DictPattern (node) => node . get_parent_id () , Self :: DottedName (node) => node . get_parent_id () , Self :: False (node) => node . get_parent_id () , Self :: Float (node) => node . get_parent_id () , Self :: Integer (node) => node . get_parent_id () , Self :: ListPattern (node) => node . get_parent_id () , Self :: None (node) => node . get_parent_id () , Self :: SplatPattern (node) => node . get_parent_id () , Self :: String (node) => node . get_parent_id () , Self :: True (node) => node . get_parent_id () , Self :: TuplePattern (node) => node . get_parent_id () , Self :: UnionPattern (node) => node . get_parent_id () } } fn get_range (& self) -> & auto_lsp :: tree_sitter :: Range { match self { Self :: Token_Minus (node) => node . get_range () , Self :: Token_Underscore (node) => node . get_range () , Self :: ClassPattern (node) => node . get_range () , Self :: ComplexPattern (node) => node . get_range () , Self :: ConcatenatedString (node) => node . get_range () , Self :: DictPattern (node) => node . get_range () , Self :: DottedName (node) => node . get_range () , Self :: False (node) => node . get_range () , Self :: Float (node) => node . get_range () , Self :: Integer (node) => node . get_range () , Self :: ListPattern (node) => node . get_range () , Self :: None (node) => node . get_range () , Self :: SplatPattern (node) => node . get_range () , Self :: String (node) => node . get_range () , Self :: True (node) => node . get_range () , Self :: TuplePattern (node) => node . get_range () , Self :: UnionPattern (node) => node . get_range () } } }
impl < 'a > TryFrom < auto_lsp :: core :: ast :: TryFromParams < 'a >> for Minus_Underscore_ClassPattern_ComplexPattern_ConcatenatedString_DictPattern_DottedName_False_Float_Integer_ListPattern_None_SplatPattern_String_True_TuplePattern_UnionPattern { type Error = auto_lsp :: core :: errors :: AstError ; fn try_from ((node , db , builder , id , parent_id) : auto_lsp :: core :: ast :: TryFromParams) -> Result < Self , Self :: Error > { match node . kind_id () { 49u16 => Ok (Self :: Token_Minus (Token_Minus :: try_from ((node , db , builder , id , parent_id)) ?)) , 50u16 => Ok (Self :: Token_Underscore (Token_Underscore :: try_from ((node , db , builder , id , parent_id)) ?)) , 174u16 => Ok (Self :: ClassPattern (ClassPattern :: try_from ((node , db , builder , id , parent_id)) ?)) , 175u16 => Ok (Self :: ComplexPattern (ComplexPattern :: try_from ((node , db , builder , id , parent_id)) ?)) , 231u16 => Ok (Self :: ConcatenatedString (ConcatenatedString :: try_from ((node , db , builder , id , parent_id)) ?)) , 170u16 => Ok (Self :: DictPattern (DictPattern :: try_from ((node , db , builder , id , parent_id)) ?)) , 163u16 => Ok (Self :: DottedName (DottedName :: try_from ((node , db , builder , id , parent_id)) ?)) , 97u16 => Ok (Self :: False (False :: try_from ((node , db , builder , id , parent_id)) ?)) , 94u16 => Ok (Self :: Float (Float :: try_from ((node , db , builder , id , parent_id)) ?)) , 93u16 => Ok (Self :: Integer (Integer :: try_from ((node , db , builder , id , parent_id)) ?)) , 181u16 => Ok (Self :: ListPattern (ListPattern :: try_from ((node , db , builder , id , parent_id)) ?)) , 98u16 => Ok (Self :: None (None :: try_from ((node , db , builder , id , parent_id)) ?)) , 173u16 => Ok (Self :: SplatPattern (SplatPattern :: try_from ((node , db , builder , id , parent_id)) ?)) , 232u16 => Ok (Self :: String (String :: try_from ((node , db , builder , id , parent_id)) ?)) , 96u16 => Ok (Self :: True (True :: try_from ((node , db , builder , id , parent_id)) ?)) , 180u16 => Ok (Self :: TuplePattern (TuplePattern :: try_from ((node , db , builder , id , parent_id)) ?)) , 167u16 => Ok (Self :: UnionPattern (UnionPattern :: try_from ((node , db , builder , id , parent_id)) ?)) , _ => Err (auto_lsp :: core :: errors :: AstError :: UnexpectedSymbol { range : node . range () , symbol : node . kind () , parent_name : stringify ! (Minus_Underscore_ClassPattern_ComplexPattern_ConcatenatedString_DictPattern_DottedName_False_Float_Integer_ListPattern_None_SplatPattern_String_True_TuplePattern_UnionPattern) , }) } } }
#[derive(Debug, Clone, PartialEq)]
pub enum ElifClause_ElseClause {
    ElifClause(ElifClause),
    ElseClause(ElseClause),
}
impl auto_lsp::core::ast::AstNode for ElifClause_ElseClause {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 132u16 | 133u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::ElifClause(node) => node.lower(),
            Self::ElseClause(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::ElifClause(node) => node.get_id(),
            Self::ElseClause(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::ElifClause(node) => node.get_parent_id(),
            Self::ElseClause(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::ElifClause(node) => node.get_range(),
            Self::ElseClause(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ElifClause_ElseClause {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            132u16 => Ok(Self::ElifClause(ElifClause::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            133u16 => Ok(Self::ElseClause(ElseClause::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(ElifClause_ElseClause),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum Pattern_PatternList {
    PatternList(PatternList),
    Pattern(Pattern),
}
impl auto_lsp::core::ast::AstNode for Pattern_PatternList {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(
            node.kind_id(),
            201u16 | 204u16 | 1u16 | 181u16 | 184u16 | 205u16 | 180u16
        )
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::PatternList(node) => node.lower(),
            Self::Pattern(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::PatternList(node) => node.get_id(),
            Self::Pattern(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::PatternList(node) => node.get_parent_id(),
            Self::Pattern(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::PatternList(node) => node.get_range(),
            Self::Pattern(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Pattern_PatternList {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            201u16 => Ok(Self::PatternList(PatternList::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            #[doc = r" Super types"]
            204u16 | 1u16 | 181u16 | 184u16 | 205u16 | 180u16 => Ok(Self::Pattern(
                Pattern::try_from((node, db, builder, id, parent_id))?,
            )),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(Pattern_PatternList),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum CasePattern_Pattern {
    CasePattern(CasePattern),
    Pattern(Pattern),
}
impl auto_lsp::core::ast::AstNode for CasePattern_Pattern {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(
            node.kind_id(),
            164u16 | 204u16 | 1u16 | 181u16 | 184u16 | 205u16 | 180u16
        )
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::CasePattern(node) => node.lower(),
            Self::Pattern(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::CasePattern(node) => node.get_id(),
            Self::Pattern(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::CasePattern(node) => node.get_parent_id(),
            Self::Pattern(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::CasePattern(node) => node.get_range(),
            Self::Pattern(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for CasePattern_Pattern {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            164u16 => Ok(Self::CasePattern(CasePattern::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            #[doc = r" Super types"]
            204u16 | 1u16 | 181u16 | 184u16 | 205u16 | 180u16 => Ok(Self::Pattern(
                Pattern::try_from((node, db, builder, id, parent_id))?,
            )),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(CasePattern_Pattern),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum DictionarySplatPattern_Identifier_ListSplatPattern {
    DictionarySplatPattern(DictionarySplatPattern),
    Identifier(Identifier),
    ListSplatPattern(ListSplatPattern),
}
impl auto_lsp::core::ast::AstNode for DictionarySplatPattern_Identifier_ListSplatPattern {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 185u16 | 1u16 | 184u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::DictionarySplatPattern(node) => node.lower(),
            Self::Identifier(node) => node.lower(),
            Self::ListSplatPattern(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::DictionarySplatPattern(node) => node.get_id(),
            Self::Identifier(node) => node.get_id(),
            Self::ListSplatPattern(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::DictionarySplatPattern(node) => node.get_parent_id(),
            Self::Identifier(node) => node.get_parent_id(),
            Self::ListSplatPattern(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::DictionarySplatPattern(node) => node.get_range(),
            Self::Identifier(node) => node.get_range(),
            Self::ListSplatPattern(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>>
    for DictionarySplatPattern_Identifier_ListSplatPattern
{
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            185u16 => Ok(Self::DictionarySplatPattern(
                DictionarySplatPattern::try_from((node, db, builder, id, parent_id))?,
            )),
            1u16 => Ok(Self::Identifier(Identifier::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            184u16 => Ok(Self::ListSplatPattern(ListSplatPattern::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(DictionarySplatPattern_Identifier_ListSplatPattern),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum ArgumentList_GeneratorExpression {
    ArgumentList(ArgumentList),
    GeneratorExpression(GeneratorExpression),
}
impl auto_lsp::core::ast::AstNode for ArgumentList_GeneratorExpression {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind_id(), 158u16 | 224u16)
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::ArgumentList(node) => node.lower(),
            Self::GeneratorExpression(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::ArgumentList(node) => node.get_id(),
            Self::GeneratorExpression(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::ArgumentList(node) => node.get_parent_id(),
            Self::GeneratorExpression(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::ArgumentList(node) => node.get_range(),
            Self::GeneratorExpression(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for ArgumentList_GeneratorExpression {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            158u16 => Ok(Self::ArgumentList(ArgumentList::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            224u16 => Ok(Self::GeneratorExpression(GeneratorExpression::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(ArgumentList_GeneratorExpression),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub struct AsPatternTarget {
    _range: auto_lsp::tree_sitter::Range,
    _id: usize,
    _parent: Option<usize>,
}
impl auto_lsp::core::ast::AstNode for AsPatternTarget {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(node.kind(), "as_pattern_target")
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        self
    }
    fn get_id(&self) -> usize {
        self._id
    }
    fn get_parent_id(&self) -> Option<usize> {
        self._parent
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        &self._range
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for AsPatternTarget {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        Ok(Self {
            _range: node.range(),
            _id: id,
            _parent: parent_id,
        })
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum Parameter {
    DefaultParameter(DefaultParameter),
    DictionarySplatPattern(DictionarySplatPattern),
    Identifier(Identifier),
    KeywordSeparator(KeywordSeparator),
    ListSplatPattern(ListSplatPattern),
    PositionalSeparator(PositionalSeparator),
    TuplePattern(TuplePattern),
    TypedDefaultParameter(TypedDefaultParameter),
    TypedParameter(TypedParameter),
}
impl auto_lsp::core::ast::AstNode for Parameter {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(
            node.kind_id(),
            182u16 | 185u16 | 1u16 | 240u16 | 184u16 | 239u16 | 180u16 | 183u16 | 208u16
        )
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::DefaultParameter(node) => node.lower(),
            Self::DictionarySplatPattern(node) => node.lower(),
            Self::Identifier(node) => node.lower(),
            Self::KeywordSeparator(node) => node.lower(),
            Self::ListSplatPattern(node) => node.lower(),
            Self::PositionalSeparator(node) => node.lower(),
            Self::TuplePattern(node) => node.lower(),
            Self::TypedDefaultParameter(node) => node.lower(),
            Self::TypedParameter(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::DefaultParameter(node) => node.get_id(),
            Self::DictionarySplatPattern(node) => node.get_id(),
            Self::Identifier(node) => node.get_id(),
            Self::KeywordSeparator(node) => node.get_id(),
            Self::ListSplatPattern(node) => node.get_id(),
            Self::PositionalSeparator(node) => node.get_id(),
            Self::TuplePattern(node) => node.get_id(),
            Self::TypedDefaultParameter(node) => node.get_id(),
            Self::TypedParameter(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::DefaultParameter(node) => node.get_parent_id(),
            Self::DictionarySplatPattern(node) => node.get_parent_id(),
            Self::Identifier(node) => node.get_parent_id(),
            Self::KeywordSeparator(node) => node.get_parent_id(),
            Self::ListSplatPattern(node) => node.get_parent_id(),
            Self::PositionalSeparator(node) => node.get_parent_id(),
            Self::TuplePattern(node) => node.get_parent_id(),
            Self::TypedDefaultParameter(node) => node.get_parent_id(),
            Self::TypedParameter(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::DefaultParameter(node) => node.get_range(),
            Self::DictionarySplatPattern(node) => node.get_range(),
            Self::Identifier(node) => node.get_range(),
            Self::KeywordSeparator(node) => node.get_range(),
            Self::ListSplatPattern(node) => node.get_range(),
            Self::PositionalSeparator(node) => node.get_range(),
            Self::TuplePattern(node) => node.get_range(),
            Self::TypedDefaultParameter(node) => node.get_range(),
            Self::TypedParameter(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Parameter {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            182u16 => Ok(Self::DefaultParameter(DefaultParameter::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            185u16 => Ok(Self::DictionarySplatPattern(
                DictionarySplatPattern::try_from((node, db, builder, id, parent_id))?,
            )),
            1u16 => Ok(Self::Identifier(Identifier::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            240u16 => Ok(Self::KeywordSeparator(KeywordSeparator::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            184u16 => Ok(Self::ListSplatPattern(ListSplatPattern::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            239u16 => Ok(Self::PositionalSeparator(PositionalSeparator::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            180u16 => Ok(Self::TuplePattern(TuplePattern::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            183u16 => Ok(Self::TypedDefaultParameter(
                TypedDefaultParameter::try_from((node, db, builder, id, parent_id))?,
            )),
            208u16 => Ok(Self::TypedParameter(TypedParameter::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(Parameter),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum PrimaryExpression {
    Attribute(Attribute),
    Await(Await),
    BinaryOperator(BinaryOperator),
    Call(Call),
    ConcatenatedString(ConcatenatedString),
    Dictionary(Dictionary),
    DictionaryComprehension(DictionaryComprehension),
    Ellipsis(Ellipsis),
    False(False),
    Float(Float),
    GeneratorExpression(GeneratorExpression),
    Identifier(Identifier),
    Integer(Integer),
    List(List),
    ListComprehension(ListComprehension),
    ListSplat(ListSplat),
    None(None),
    ParenthesizedExpression(ParenthesizedExpression),
    Set(Set),
    SetComprehension(SetComprehension),
    String(String),
    Subscript(Subscript),
    True(True),
    Tuple(Tuple),
    UnaryOperator(UnaryOperator),
}
impl auto_lsp::core::ast::AstNode for PrimaryExpression {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(
            node.kind_id(),
            204u16
                | 238u16
                | 192u16
                | 207u16
                | 231u16
                | 219u16
                | 222u16
                | 88u16
                | 97u16
                | 94u16
                | 224u16
                | 1u16
                | 93u16
                | 216u16
                | 221u16
                | 149u16
                | 98u16
                | 226u16
                | 217u16
                | 223u16
                | 232u16
                | 205u16
                | 96u16
                | 218u16
                | 193u16
        )
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::Attribute(node) => node.lower(),
            Self::Await(node) => node.lower(),
            Self::BinaryOperator(node) => node.lower(),
            Self::Call(node) => node.lower(),
            Self::ConcatenatedString(node) => node.lower(),
            Self::Dictionary(node) => node.lower(),
            Self::DictionaryComprehension(node) => node.lower(),
            Self::Ellipsis(node) => node.lower(),
            Self::False(node) => node.lower(),
            Self::Float(node) => node.lower(),
            Self::GeneratorExpression(node) => node.lower(),
            Self::Identifier(node) => node.lower(),
            Self::Integer(node) => node.lower(),
            Self::List(node) => node.lower(),
            Self::ListComprehension(node) => node.lower(),
            Self::ListSplat(node) => node.lower(),
            Self::None(node) => node.lower(),
            Self::ParenthesizedExpression(node) => node.lower(),
            Self::Set(node) => node.lower(),
            Self::SetComprehension(node) => node.lower(),
            Self::String(node) => node.lower(),
            Self::Subscript(node) => node.lower(),
            Self::True(node) => node.lower(),
            Self::Tuple(node) => node.lower(),
            Self::UnaryOperator(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::Attribute(node) => node.get_id(),
            Self::Await(node) => node.get_id(),
            Self::BinaryOperator(node) => node.get_id(),
            Self::Call(node) => node.get_id(),
            Self::ConcatenatedString(node) => node.get_id(),
            Self::Dictionary(node) => node.get_id(),
            Self::DictionaryComprehension(node) => node.get_id(),
            Self::Ellipsis(node) => node.get_id(),
            Self::False(node) => node.get_id(),
            Self::Float(node) => node.get_id(),
            Self::GeneratorExpression(node) => node.get_id(),
            Self::Identifier(node) => node.get_id(),
            Self::Integer(node) => node.get_id(),
            Self::List(node) => node.get_id(),
            Self::ListComprehension(node) => node.get_id(),
            Self::ListSplat(node) => node.get_id(),
            Self::None(node) => node.get_id(),
            Self::ParenthesizedExpression(node) => node.get_id(),
            Self::Set(node) => node.get_id(),
            Self::SetComprehension(node) => node.get_id(),
            Self::String(node) => node.get_id(),
            Self::Subscript(node) => node.get_id(),
            Self::True(node) => node.get_id(),
            Self::Tuple(node) => node.get_id(),
            Self::UnaryOperator(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::Attribute(node) => node.get_parent_id(),
            Self::Await(node) => node.get_parent_id(),
            Self::BinaryOperator(node) => node.get_parent_id(),
            Self::Call(node) => node.get_parent_id(),
            Self::ConcatenatedString(node) => node.get_parent_id(),
            Self::Dictionary(node) => node.get_parent_id(),
            Self::DictionaryComprehension(node) => node.get_parent_id(),
            Self::Ellipsis(node) => node.get_parent_id(),
            Self::False(node) => node.get_parent_id(),
            Self::Float(node) => node.get_parent_id(),
            Self::GeneratorExpression(node) => node.get_parent_id(),
            Self::Identifier(node) => node.get_parent_id(),
            Self::Integer(node) => node.get_parent_id(),
            Self::List(node) => node.get_parent_id(),
            Self::ListComprehension(node) => node.get_parent_id(),
            Self::ListSplat(node) => node.get_parent_id(),
            Self::None(node) => node.get_parent_id(),
            Self::ParenthesizedExpression(node) => node.get_parent_id(),
            Self::Set(node) => node.get_parent_id(),
            Self::SetComprehension(node) => node.get_parent_id(),
            Self::String(node) => node.get_parent_id(),
            Self::Subscript(node) => node.get_parent_id(),
            Self::True(node) => node.get_parent_id(),
            Self::Tuple(node) => node.get_parent_id(),
            Self::UnaryOperator(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::Attribute(node) => node.get_range(),
            Self::Await(node) => node.get_range(),
            Self::BinaryOperator(node) => node.get_range(),
            Self::Call(node) => node.get_range(),
            Self::ConcatenatedString(node) => node.get_range(),
            Self::Dictionary(node) => node.get_range(),
            Self::DictionaryComprehension(node) => node.get_range(),
            Self::Ellipsis(node) => node.get_range(),
            Self::False(node) => node.get_range(),
            Self::Float(node) => node.get_range(),
            Self::GeneratorExpression(node) => node.get_range(),
            Self::Identifier(node) => node.get_range(),
            Self::Integer(node) => node.get_range(),
            Self::List(node) => node.get_range(),
            Self::ListComprehension(node) => node.get_range(),
            Self::ListSplat(node) => node.get_range(),
            Self::None(node) => node.get_range(),
            Self::ParenthesizedExpression(node) => node.get_range(),
            Self::Set(node) => node.get_range(),
            Self::SetComprehension(node) => node.get_range(),
            Self::String(node) => node.get_range(),
            Self::Subscript(node) => node.get_range(),
            Self::True(node) => node.get_range(),
            Self::Tuple(node) => node.get_range(),
            Self::UnaryOperator(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for PrimaryExpression {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            204u16 => Ok(Self::Attribute(Attribute::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            238u16 => Ok(Self::Await(Await::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            192u16 => Ok(Self::BinaryOperator(BinaryOperator::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            207u16 => Ok(Self::Call(Call::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            231u16 => Ok(Self::ConcatenatedString(ConcatenatedString::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            219u16 => Ok(Self::Dictionary(Dictionary::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            222u16 => Ok(Self::DictionaryComprehension(
                DictionaryComprehension::try_from((node, db, builder, id, parent_id))?,
            )),
            88u16 => Ok(Self::Ellipsis(Ellipsis::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            97u16 => Ok(Self::False(False::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            94u16 => Ok(Self::Float(Float::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            224u16 => Ok(Self::GeneratorExpression(GeneratorExpression::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            1u16 => Ok(Self::Identifier(Identifier::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            93u16 => Ok(Self::Integer(Integer::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            216u16 => Ok(Self::List(List::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            221u16 => Ok(Self::ListComprehension(ListComprehension::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            149u16 => Ok(Self::ListSplat(ListSplat::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            98u16 => Ok(Self::None(None::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            226u16 => Ok(Self::ParenthesizedExpression(
                ParenthesizedExpression::try_from((node, db, builder, id, parent_id))?,
            )),
            217u16 => Ok(Self::Set(Set::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            223u16 => Ok(Self::SetComprehension(SetComprehension::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            232u16 => Ok(Self::String(String::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            205u16 => Ok(Self::Subscript(Subscript::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            96u16 => Ok(Self::True(True::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            218u16 => Ok(Self::Tuple(Tuple::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            193u16 => Ok(Self::UnaryOperator(UnaryOperator::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(PrimaryExpression),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum Pattern {
    Attribute(Attribute),
    Identifier(Identifier),
    ListPattern(ListPattern),
    ListSplatPattern(ListSplatPattern),
    Subscript(Subscript),
    TuplePattern(TuplePattern),
}
impl auto_lsp::core::ast::AstNode for Pattern {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(
            node.kind_id(),
            204u16 | 1u16 | 181u16 | 184u16 | 205u16 | 180u16
        )
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::Attribute(node) => node.lower(),
            Self::Identifier(node) => node.lower(),
            Self::ListPattern(node) => node.lower(),
            Self::ListSplatPattern(node) => node.lower(),
            Self::Subscript(node) => node.lower(),
            Self::TuplePattern(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::Attribute(node) => node.get_id(),
            Self::Identifier(node) => node.get_id(),
            Self::ListPattern(node) => node.get_id(),
            Self::ListSplatPattern(node) => node.get_id(),
            Self::Subscript(node) => node.get_id(),
            Self::TuplePattern(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::Attribute(node) => node.get_parent_id(),
            Self::Identifier(node) => node.get_parent_id(),
            Self::ListPattern(node) => node.get_parent_id(),
            Self::ListSplatPattern(node) => node.get_parent_id(),
            Self::Subscript(node) => node.get_parent_id(),
            Self::TuplePattern(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::Attribute(node) => node.get_range(),
            Self::Identifier(node) => node.get_range(),
            Self::ListPattern(node) => node.get_range(),
            Self::ListSplatPattern(node) => node.get_range(),
            Self::Subscript(node) => node.get_range(),
            Self::TuplePattern(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Pattern {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            204u16 => Ok(Self::Attribute(Attribute::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            1u16 => Ok(Self::Identifier(Identifier::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            181u16 => Ok(Self::ListPattern(ListPattern::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            184u16 => Ok(Self::ListSplatPattern(ListSplatPattern::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            205u16 => Ok(Self::Subscript(Subscript::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            180u16 => Ok(Self::TuplePattern(TuplePattern::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(Pattern),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum Expression {
    AsPattern(AsPattern),
    BooleanOperator(BooleanOperator),
    ComparisonOperator(ComparisonOperator),
    ConditionalExpression(ConditionalExpression),
    Lambda(Lambda),
    NamedExpression(NamedExpression),
    NotOperator(NotOperator),
    PrimaryExpression(PrimaryExpression),
}
impl auto_lsp::core::ast::AstNode for Expression {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(
            node.kind_id(),
            186u16
                | 191u16
                | 196u16
                | 230u16
                | 197u16
                | 123u16
                | 190u16
                | 204u16
                | 238u16
                | 192u16
                | 207u16
                | 231u16
                | 219u16
                | 222u16
                | 88u16
                | 97u16
                | 94u16
                | 224u16
                | 1u16
                | 93u16
                | 216u16
                | 221u16
                | 149u16
                | 98u16
                | 226u16
                | 217u16
                | 223u16
                | 232u16
                | 205u16
                | 96u16
                | 218u16
                | 193u16
        )
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::AsPattern(node) => node.lower(),
            Self::BooleanOperator(node) => node.lower(),
            Self::ComparisonOperator(node) => node.lower(),
            Self::ConditionalExpression(node) => node.lower(),
            Self::Lambda(node) => node.lower(),
            Self::NamedExpression(node) => node.lower(),
            Self::NotOperator(node) => node.lower(),
            Self::PrimaryExpression(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::AsPattern(node) => node.get_id(),
            Self::BooleanOperator(node) => node.get_id(),
            Self::ComparisonOperator(node) => node.get_id(),
            Self::ConditionalExpression(node) => node.get_id(),
            Self::Lambda(node) => node.get_id(),
            Self::NamedExpression(node) => node.get_id(),
            Self::NotOperator(node) => node.get_id(),
            Self::PrimaryExpression(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::AsPattern(node) => node.get_parent_id(),
            Self::BooleanOperator(node) => node.get_parent_id(),
            Self::ComparisonOperator(node) => node.get_parent_id(),
            Self::ConditionalExpression(node) => node.get_parent_id(),
            Self::Lambda(node) => node.get_parent_id(),
            Self::NamedExpression(node) => node.get_parent_id(),
            Self::NotOperator(node) => node.get_parent_id(),
            Self::PrimaryExpression(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::AsPattern(node) => node.get_range(),
            Self::BooleanOperator(node) => node.get_range(),
            Self::ComparisonOperator(node) => node.get_range(),
            Self::ConditionalExpression(node) => node.get_range(),
            Self::Lambda(node) => node.get_range(),
            Self::NamedExpression(node) => node.get_range(),
            Self::NotOperator(node) => node.get_range(),
            Self::PrimaryExpression(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for Expression {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            186u16 => Ok(Self::AsPattern(AsPattern::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            191u16 => Ok(Self::BooleanOperator(BooleanOperator::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            196u16 => Ok(Self::ComparisonOperator(ComparisonOperator::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            230u16 => Ok(Self::ConditionalExpression(
                ConditionalExpression::try_from((node, db, builder, id, parent_id))?,
            )),
            197u16 => Ok(Self::Lambda(Lambda::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            123u16 => Ok(Self::NamedExpression(NamedExpression::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            190u16 => Ok(Self::NotOperator(NotOperator::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            #[doc = r" Super types"]
            204u16 | 238u16 | 192u16 | 207u16 | 231u16 | 219u16 | 222u16 | 88u16 | 97u16
            | 94u16 | 224u16 | 1u16 | 93u16 | 216u16 | 221u16 | 149u16 | 98u16 | 226u16
            | 217u16 | 223u16 | 232u16 | 205u16 | 96u16 | 218u16 | 193u16 => {
                Ok(Self::PrimaryExpression(PrimaryExpression::try_from((
                    node, db, builder, id, parent_id,
                ))?))
            }
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(Expression),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum SimpleStatement {
    AssertStatement(AssertStatement),
    BreakStatement(BreakStatement),
    ContinueStatement(ContinueStatement),
    DeleteStatement(DeleteStatement),
    ExecStatement(ExecStatement),
    ExpressionStatement(ExpressionStatement),
    FutureImportStatement(FutureImportStatement),
    GlobalStatement(GlobalStatement),
    ImportFromStatement(ImportFromStatement),
    ImportStatement(ImportStatement),
    NonlocalStatement(NonlocalStatement),
    PassStatement(PassStatement),
    PrintStatement(PrintStatement),
    RaiseStatement(RaiseStatement),
    ReturnStatement(ReturnStatement),
    TypeAliasStatement(TypeAliasStatement),
}
impl auto_lsp::core::ast::AstNode for SimpleStatement {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(
            node.kind_id(),
            121u16
                | 129u16
                | 130u16
                | 126u16
                | 153u16
                | 122u16
                | 114u16
                | 151u16
                | 115u16
                | 111u16
                | 152u16
                | 128u16
                | 119u16
                | 127u16
                | 125u16
                | 154u16
        )
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::AssertStatement(node) => node.lower(),
            Self::BreakStatement(node) => node.lower(),
            Self::ContinueStatement(node) => node.lower(),
            Self::DeleteStatement(node) => node.lower(),
            Self::ExecStatement(node) => node.lower(),
            Self::ExpressionStatement(node) => node.lower(),
            Self::FutureImportStatement(node) => node.lower(),
            Self::GlobalStatement(node) => node.lower(),
            Self::ImportFromStatement(node) => node.lower(),
            Self::ImportStatement(node) => node.lower(),
            Self::NonlocalStatement(node) => node.lower(),
            Self::PassStatement(node) => node.lower(),
            Self::PrintStatement(node) => node.lower(),
            Self::RaiseStatement(node) => node.lower(),
            Self::ReturnStatement(node) => node.lower(),
            Self::TypeAliasStatement(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::AssertStatement(node) => node.get_id(),
            Self::BreakStatement(node) => node.get_id(),
            Self::ContinueStatement(node) => node.get_id(),
            Self::DeleteStatement(node) => node.get_id(),
            Self::ExecStatement(node) => node.get_id(),
            Self::ExpressionStatement(node) => node.get_id(),
            Self::FutureImportStatement(node) => node.get_id(),
            Self::GlobalStatement(node) => node.get_id(),
            Self::ImportFromStatement(node) => node.get_id(),
            Self::ImportStatement(node) => node.get_id(),
            Self::NonlocalStatement(node) => node.get_id(),
            Self::PassStatement(node) => node.get_id(),
            Self::PrintStatement(node) => node.get_id(),
            Self::RaiseStatement(node) => node.get_id(),
            Self::ReturnStatement(node) => node.get_id(),
            Self::TypeAliasStatement(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::AssertStatement(node) => node.get_parent_id(),
            Self::BreakStatement(node) => node.get_parent_id(),
            Self::ContinueStatement(node) => node.get_parent_id(),
            Self::DeleteStatement(node) => node.get_parent_id(),
            Self::ExecStatement(node) => node.get_parent_id(),
            Self::ExpressionStatement(node) => node.get_parent_id(),
            Self::FutureImportStatement(node) => node.get_parent_id(),
            Self::GlobalStatement(node) => node.get_parent_id(),
            Self::ImportFromStatement(node) => node.get_parent_id(),
            Self::ImportStatement(node) => node.get_parent_id(),
            Self::NonlocalStatement(node) => node.get_parent_id(),
            Self::PassStatement(node) => node.get_parent_id(),
            Self::PrintStatement(node) => node.get_parent_id(),
            Self::RaiseStatement(node) => node.get_parent_id(),
            Self::ReturnStatement(node) => node.get_parent_id(),
            Self::TypeAliasStatement(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::AssertStatement(node) => node.get_range(),
            Self::BreakStatement(node) => node.get_range(),
            Self::ContinueStatement(node) => node.get_range(),
            Self::DeleteStatement(node) => node.get_range(),
            Self::ExecStatement(node) => node.get_range(),
            Self::ExpressionStatement(node) => node.get_range(),
            Self::FutureImportStatement(node) => node.get_range(),
            Self::GlobalStatement(node) => node.get_range(),
            Self::ImportFromStatement(node) => node.get_range(),
            Self::ImportStatement(node) => node.get_range(),
            Self::NonlocalStatement(node) => node.get_range(),
            Self::PassStatement(node) => node.get_range(),
            Self::PrintStatement(node) => node.get_range(),
            Self::RaiseStatement(node) => node.get_range(),
            Self::ReturnStatement(node) => node.get_range(),
            Self::TypeAliasStatement(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for SimpleStatement {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            121u16 => Ok(Self::AssertStatement(AssertStatement::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            129u16 => Ok(Self::BreakStatement(BreakStatement::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            130u16 => Ok(Self::ContinueStatement(ContinueStatement::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            126u16 => Ok(Self::DeleteStatement(DeleteStatement::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            153u16 => Ok(Self::ExecStatement(ExecStatement::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            122u16 => Ok(Self::ExpressionStatement(ExpressionStatement::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            114u16 => Ok(Self::FutureImportStatement(
                FutureImportStatement::try_from((node, db, builder, id, parent_id))?,
            )),
            151u16 => Ok(Self::GlobalStatement(GlobalStatement::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            115u16 => Ok(Self::ImportFromStatement(ImportFromStatement::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            111u16 => Ok(Self::ImportStatement(ImportStatement::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            152u16 => Ok(Self::NonlocalStatement(NonlocalStatement::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            128u16 => Ok(Self::PassStatement(PassStatement::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            119u16 => Ok(Self::PrintStatement(PrintStatement::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            127u16 => Ok(Self::RaiseStatement(RaiseStatement::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            125u16 => Ok(Self::ReturnStatement(ReturnStatement::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            154u16 => Ok(Self::TypeAliasStatement(TypeAliasStatement::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(SimpleStatement),
            }),
        }
    }
}
#[derive(Debug, Clone, PartialEq)]
pub enum CompoundStatement {
    ClassDefinition(ClassDefinition),
    DecoratedDefinition(DecoratedDefinition),
    ForStatement(ForStatement),
    FunctionDefinition(FunctionDefinition),
    IfStatement(IfStatement),
    MatchStatement(MatchStatement),
    TryStatement(TryStatement),
    WhileStatement(WhileStatement),
    WithStatement(WithStatement),
}
impl auto_lsp::core::ast::AstNode for CompoundStatement {
    fn contains(node: &auto_lsp::tree_sitter::Node) -> bool {
        matches!(
            node.kind_id(),
            155u16 | 159u16 | 137u16 | 146u16 | 131u16 | 134u16 | 139u16 | 138u16 | 143u16
        )
    }
    fn lower(&self) -> &dyn auto_lsp::core::ast::AstNode {
        match self {
            Self::ClassDefinition(node) => node.lower(),
            Self::DecoratedDefinition(node) => node.lower(),
            Self::ForStatement(node) => node.lower(),
            Self::FunctionDefinition(node) => node.lower(),
            Self::IfStatement(node) => node.lower(),
            Self::MatchStatement(node) => node.lower(),
            Self::TryStatement(node) => node.lower(),
            Self::WhileStatement(node) => node.lower(),
            Self::WithStatement(node) => node.lower(),
        }
    }
    fn get_id(&self) -> usize {
        match self {
            Self::ClassDefinition(node) => node.get_id(),
            Self::DecoratedDefinition(node) => node.get_id(),
            Self::ForStatement(node) => node.get_id(),
            Self::FunctionDefinition(node) => node.get_id(),
            Self::IfStatement(node) => node.get_id(),
            Self::MatchStatement(node) => node.get_id(),
            Self::TryStatement(node) => node.get_id(),
            Self::WhileStatement(node) => node.get_id(),
            Self::WithStatement(node) => node.get_id(),
        }
    }
    fn get_parent_id(&self) -> Option<usize> {
        match self {
            Self::ClassDefinition(node) => node.get_parent_id(),
            Self::DecoratedDefinition(node) => node.get_parent_id(),
            Self::ForStatement(node) => node.get_parent_id(),
            Self::FunctionDefinition(node) => node.get_parent_id(),
            Self::IfStatement(node) => node.get_parent_id(),
            Self::MatchStatement(node) => node.get_parent_id(),
            Self::TryStatement(node) => node.get_parent_id(),
            Self::WhileStatement(node) => node.get_parent_id(),
            Self::WithStatement(node) => node.get_parent_id(),
        }
    }
    fn get_range(&self) -> &auto_lsp::tree_sitter::Range {
        match self {
            Self::ClassDefinition(node) => node.get_range(),
            Self::DecoratedDefinition(node) => node.get_range(),
            Self::ForStatement(node) => node.get_range(),
            Self::FunctionDefinition(node) => node.get_range(),
            Self::IfStatement(node) => node.get_range(),
            Self::MatchStatement(node) => node.get_range(),
            Self::TryStatement(node) => node.get_range(),
            Self::WhileStatement(node) => node.get_range(),
            Self::WithStatement(node) => node.get_range(),
        }
    }
}
impl<'a> TryFrom<auto_lsp::core::ast::TryFromParams<'a>> for CompoundStatement {
    type Error = auto_lsp::core::errors::AstError;
    fn try_from(
        (node, db, builder, id, parent_id): auto_lsp::core::ast::TryFromParams,
    ) -> Result<Self, Self::Error> {
        match node.kind_id() {
            155u16 => Ok(Self::ClassDefinition(ClassDefinition::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            159u16 => Ok(Self::DecoratedDefinition(DecoratedDefinition::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            137u16 => Ok(Self::ForStatement(ForStatement::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            146u16 => Ok(Self::FunctionDefinition(FunctionDefinition::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            131u16 => Ok(Self::IfStatement(IfStatement::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            134u16 => Ok(Self::MatchStatement(MatchStatement::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            139u16 => Ok(Self::TryStatement(TryStatement::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            138u16 => Ok(Self::WhileStatement(WhileStatement::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            143u16 => Ok(Self::WithStatement(WithStatement::try_from((
                node, db, builder, id, parent_id,
            ))?)),
            _ => Err(auto_lsp::core::errors::AstError::UnexpectedSymbol {
                range: node.range(),
                symbol: node.kind(),
                parent_name: stringify!(CompoundStatement),
            }),
        }
    }
}
