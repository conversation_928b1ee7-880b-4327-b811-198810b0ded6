---
source: src/tests/corpus/expressions.rs
expression: module
---
Module {
    children: [
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Assignment(
                            Assignment {
                                right: Some(
                                    Expression(
                                        PrimaryExpression(
                                            Integer(
                                                Integer {
                                                    [RANGE]
                                                    _id: 4,
                                                    _parent: Some(
                                                        2,
                                                    ),
                                                },
                                            ),
                                        ),
                                    ),
                                ),
                                Type: None,
                                left: Pattern(
                                    Identifier(
                                        Identifier {
                                            [RANGE]
                                            _id: 3,
                                            _parent: Some(
                                                2,
                                            ),
                                        },
                                    ),
                                ),
                                [RANGE]
                                _id: 2,
                                _parent: Some(
                                    1,
                                ),
                            },
                        ),
                    ],
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Assignment(
                            Assignment {
                                right: Some(
                                    ExpressionList(
                                        ExpressionList {
                                            children: [
                                                PrimaryExpression(
                                                    Integer(
                                                        Integer {
                                                            [RANGE]
                                                            _id: 11,
                                                            _parent: Some(
                                                                10,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                                PrimaryExpression(
                                                    Integer(
                                                        Integer {
                                                            [RANGE]
                                                            _id: 12,
                                                            _parent: Some(
                                                                10,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ],
                                            [RANGE]
                                            _id: 10,
                                            _parent: Some(
                                                6,
                                            ),
                                        },
                                    ),
                                ),
                                Type: None,
                                left: PatternList(
                                    PatternList {
                                        children: [
                                            Identifier(
                                                Identifier {
                                                    [RANGE]
                                                    _id: 8,
                                                    _parent: Some(
                                                        7,
                                                    ),
                                                },
                                            ),
                                            Identifier(
                                                Identifier {
                                                    [RANGE]
                                                    _id: 9,
                                                    _parent: Some(
                                                        7,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 7,
                                        _parent: Some(
                                            6,
                                        ),
                                    },
                                ),
                                [RANGE]
                                _id: 6,
                                _parent: Some(
                                    5,
                                ),
                            },
                        ),
                    ],
                    [RANGE]
                    _id: 5,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Assignment(
                            Assignment {
                                right: Some(
                                    ExpressionList(
                                        ExpressionList {
                                            children: [
                                                PrimaryExpression(
                                                    Integer(
                                                        Integer {
                                                            [RANGE]
                                                            _id: 20,
                                                            _parent: Some(
                                                                19,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                                PrimaryExpression(
                                                    Integer(
                                                        Integer {
                                                            [RANGE]
                                                            _id: 21,
                                                            _parent: Some(
                                                                19,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                                PrimaryExpression(
                                                    Integer(
                                                        Integer {
                                                            [RANGE]
                                                            _id: 22,
                                                            _parent: Some(
                                                                19,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ],
                                            [RANGE]
                                            _id: 19,
                                            _parent: Some(
                                                14,
                                            ),
                                        },
                                    ),
                                ),
                                Type: None,
                                left: PatternList(
                                    PatternList {
                                        children: [
                                            Identifier(
                                                Identifier {
                                                    [RANGE]
                                                    _id: 16,
                                                    _parent: Some(
                                                        15,
                                                    ),
                                                },
                                            ),
                                            ListSplatPattern(
                                                ListSplatPattern {
                                                    children: Identifier(
                                                        Identifier {
                                                            [RANGE]
                                                            _id: 18,
                                                            _parent: Some(
                                                                17,
                                                            ),
                                                        },
                                                    ),
                                                    [RANGE]
                                                    _id: 17,
                                                    _parent: Some(
                                                        15,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 15,
                                        _parent: Some(
                                            14,
                                        ),
                                    },
                                ),
                                [RANGE]
                                _id: 14,
                                _parent: Some(
                                    13,
                                ),
                            },
                        ),
                    ],
                    [RANGE]
                    _id: 13,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Assignment(
                            Assignment {
                                right: Some(
                                    ExpressionList(
                                        ExpressionList {
                                            children: [
                                                PrimaryExpression(
                                                    Integer(
                                                        Integer {
                                                            [RANGE]
                                                            _id: 28,
                                                            _parent: Some(
                                                                27,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                                PrimaryExpression(
                                                    Integer(
                                                        Integer {
                                                            [RANGE]
                                                            _id: 29,
                                                            _parent: Some(
                                                                27,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ],
                                            [RANGE]
                                            _id: 27,
                                            _parent: Some(
                                                24,
                                            ),
                                        },
                                    ),
                                ),
                                Type: None,
                                left: PatternList(
                                    PatternList {
                                        children: [
                                            Identifier(
                                                Identifier {
                                                    [RANGE]
                                                    _id: 26,
                                                    _parent: Some(
                                                        25,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 25,
                                        _parent: Some(
                                            24,
                                        ),
                                    },
                                ),
                                [RANGE]
                                _id: 24,
                                _parent: Some(
                                    23,
                                ),
                            },
                        ),
                    ],
                    [RANGE]
                    _id: 23,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Assignment(
                            Assignment {
                                right: Some(
                                    Assignment(
                                        Assignment {
                                            right: Some(
                                                Expression(
                                                    PrimaryExpression(
                                                        Identifier(
                                                            Identifier {
                                                                [RANGE]
                                                                _id: 37,
                                                                _parent: Some(
                                                                    35,
                                                                ),
                                                            },
                                                        ),
                                                    ),
                                                ),
                                            ),
                                            Type: None,
                                            left: Pattern(
                                                Identifier(
                                                    Identifier {
                                                        [RANGE]
                                                        _id: 36,
                                                        _parent: Some(
                                                            35,
                                                        ),
                                                    },
                                                ),
                                            ),
                                            [RANGE]
                                            _id: 35,
                                            _parent: Some(
                                                31,
                                            ),
                                        },
                                    ),
                                ),
                                Type: None,
                                left: Pattern(
                                    Subscript(
                                        Subscript {
                                            subscript: [
                                                Expression(
                                                    PrimaryExpression(
                                                        Identifier(
                                                            Identifier {
                                                                [RANGE]
                                                                _id: 34,
                                                                _parent: Some(
                                                                    32,
                                                                ),
                                                            },
                                                        ),
                                                    ),
                                                ),
                                            ],
                                            value: Identifier(
                                                Identifier {
                                                    [RANGE]
                                                    _id: 33,
                                                    _parent: Some(
                                                        32,
                                                    ),
                                                },
                                            ),
                                            [RANGE]
                                            _id: 32,
                                            _parent: Some(
                                                31,
                                            ),
                                        },
                                    ),
                                ),
                                [RANGE]
                                _id: 31,
                                _parent: Some(
                                    30,
                                ),
                            },
                        ),
                    ],
                    [RANGE]
                    _id: 30,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Assignment(
                            Assignment {
                                right: Some(
                                    Expression(
                                        PrimaryExpression(
                                            Identifier(
                                                Identifier {
                                                    [RANGE]
                                                    _id: 46,
                                                    _parent: Some(
                                                        39,
                                                    ),
                                                },
                                            ),
                                        ),
                                    ),
                                ),
                                Type: None,
                                left: PatternList(
                                    PatternList {
                                        children: [
                                            Identifier(
                                                Identifier {
                                                    [RANGE]
                                                    _id: 41,
                                                    _parent: Some(
                                                        40,
                                                    ),
                                                },
                                            ),
                                            ListSplatPattern(
                                                ListSplatPattern {
                                                    children: Attribute(
                                                        Attribute {
                                                            object: Identifier(
                                                                Identifier {
                                                                    [RANGE]
                                                                    _id: 44,
                                                                    _parent: Some(
                                                                        43,
                                                                    ),
                                                                },
                                                            ),
                                                            attribute: Identifier {
                                                                [RANGE]
                                                                _id: 45,
                                                                _parent: Some(
                                                                    43,
                                                                ),
                                                            },
                                                            [RANGE]
                                                            _id: 43,
                                                            _parent: Some(
                                                                42,
                                                            ),
                                                        },
                                                    ),
                                                    [RANGE]
                                                    _id: 42,
                                                    _parent: Some(
                                                        40,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 40,
                                        _parent: Some(
                                            39,
                                        ),
                                    },
                                ),
                                [RANGE]
                                _id: 39,
                                _parent: Some(
                                    38,
                                ),
                            },
                        ),
                    ],
                    [RANGE]
                    _id: 38,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
