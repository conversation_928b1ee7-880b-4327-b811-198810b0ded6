---
source: src/tests/corpus/expressions.rs
expression: module
---
Module {
    children: [
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Assignment(
                            Assignment {
                                right: Some(
                                    Expression(
                                        PrimaryExpression(
                                            List(
                                                List {
                                                    children: [],
                                                    [RANGE]
                                                    _id: 10,
                                                    _parent: Some(
                                                        2,
                                                    ),
                                                },
                                            ),
                                        ),
                                    ),
                                ),
                                Type: Some(
                                    Type {
                                        children: GenericType(
                                            GenericType {
                                                children: [
                                                    Identifier(
                                                        Identifier {
                                                            [RANGE]
                                                            _id: 6,
                                                            _parent: Some(
                                                                5,
                                                            ),
                                                        },
                                                    ),
                                                    TypeParameter(
                                                        TypeParameter {
                                                            children: [
                                                                Type {
                                                                    children: Expression(
                                                                        PrimaryExpression(
                                                                            Identifier(
                                                                                Identifier {
                                                                                    [RANGE]
                                                                                    _id: 9,
                                                                                    _parent: Some(
                                                                                        8,
                                                                                    ),
                                                                                },
                                                                            ),
                                                                        ),
                                                                    ),
                                                                    [RANGE]
                                                                    _id: 8,
                                                                    _parent: Some(
                                                                        7,
                                                                    ),
                                                                },
                                                            ],
                                                            [RANGE]
                                                            _id: 7,
                                                            _parent: Some(
                                                                5,
                                                            ),
                                                        },
                                                    ),
                                                ],
                                                [RANGE]
                                                _id: 5,
                                                _parent: Some(
                                                    4,
                                                ),
                                            },
                                        ),
                                        [RANGE]
                                        _id: 4,
                                        _parent: Some(
                                            2,
                                        ),
                                    },
                                ),
                                left: Pattern(
                                    Identifier(
                                        Identifier {
                                            [RANGE]
                                            _id: 3,
                                            _parent: Some(
                                                2,
                                            ),
                                        },
                                    ),
                                ),
                                [RANGE]
                                _id: 2,
                                _parent: Some(
                                    1,
                                ),
                            },
                        ),
                    ],
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
