---
source: src/tests/corpus/expressions.rs
expression: module
---
Module {
    children: [
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                Attribute(
                                    Attribute {
                                        object: Attribute(
                                            Attribute {
                                                object: Identifier(
                                                    Identifier {
                                                        [RANGE]
                                                        _id: 4,
                                                        _parent: Some(
                                                            3,
                                                        ),
                                                    },
                                                ),
                                                attribute: Identifier {
                                                    [RANGE]
                                                    _id: 5,
                                                    _parent: Some(
                                                        3,
                                                    ),
                                                },
                                                [RANGE]
                                                _id: 3,
                                                _parent: Some(
                                                    2,
                                                ),
                                            },
                                        ),
                                        attribute: Identifier {
                                            [RANGE]
                                            _id: 6,
                                            _parent: Some(
                                                2,
                                            ),
                                        },
                                        [RANGE]
                                        _id: 2,
                                        _parent: Some(
                                            1,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
