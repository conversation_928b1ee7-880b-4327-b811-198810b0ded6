---
source: src/tests/corpus/expressions.rs
expression: module
---
Module {
    children: [
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        AugmentedAssignment(
                            AugmentedAssignment {
                                left: Pattern(
                                    Identifier(
                                        Identifier {
                                            [RANGE]
                                            _id: 3,
                                            _parent: Some(
                                                2,
                                            ),
                                        },
                                    ),
                                ),
                                operator: Token_PlusEqual(
                                    Token_PlusEqual {
                                        [RANGE]
                                        _id: 4,
                                        _parent: Some(
                                            2,
                                        ),
                                    },
                                ),
                                right: Expression(
                                    PrimaryExpression(
                                        Integer(
                                            Integer {
                                                [RANGE]
                                                _id: 5,
                                                _parent: Some(
                                                    2,
                                                ),
                                            },
                                        ),
                                    ),
                                ),
                                [RANGE]
                                _id: 2,
                                _parent: Some(
                                    1,
                                ),
                            },
                        ),
                    ],
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        AugmentedAssignment(
                            AugmentedAssignment {
                                left: Pattern(
                                    Identifier(
                                        Identifier {
                                            [RANGE]
                                            _id: 8,
                                            _parent: Some(
                                                7,
                                            ),
                                        },
                                    ),
                                ),
                                operator: Token_GreaterGreaterEqual(
                                    Token_GreaterGreaterEqual {
                                        [RANGE]
                                        _id: 9,
                                        _parent: Some(
                                            7,
                                        ),
                                    },
                                ),
                                right: Expression(
                                    PrimaryExpression(
                                        Integer(
                                            Integer {
                                                [RANGE]
                                                _id: 10,
                                                _parent: Some(
                                                    7,
                                                ),
                                            },
                                        ),
                                    ),
                                ),
                                [RANGE]
                                _id: 7,
                                _parent: Some(
                                    6,
                                ),
                            },
                        ),
                    ],
                    [RANGE]
                    _id: 6,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        AugmentedAssignment(
                            AugmentedAssignment {
                                left: Pattern(
                                    Identifier(
                                        Identifier {
                                            [RANGE]
                                            _id: 13,
                                            _parent: Some(
                                                12,
                                            ),
                                        },
                                    ),
                                ),
                                operator: Token_SlashSlashEqual(
                                    Token_SlashSlashEqual {
                                        [RANGE]
                                        _id: 14,
                                        _parent: Some(
                                            12,
                                        ),
                                    },
                                ),
                                right: Expression(
                                    PrimaryExpression(
                                        Integer(
                                            Integer {
                                                [RANGE]
                                                _id: 15,
                                                _parent: Some(
                                                    12,
                                                ),
                                            },
                                        ),
                                    ),
                                ),
                                [RANGE]
                                _id: 12,
                                _parent: Some(
                                    11,
                                ),
                            },
                        ),
                    ],
                    [RANGE]
                    _id: 11,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        AugmentedAssignment(
                            AugmentedAssignment {
                                left: Pattern(
                                    Identifier(
                                        Identifier {
                                            [RANGE]
                                            _id: 18,
                                            _parent: Some(
                                                17,
                                            ),
                                        },
                                    ),
                                ),
                                operator: Token_AtEqual(
                                    Token_AtEqual {
                                        [RANGE]
                                        _id: 19,
                                        _parent: Some(
                                            17,
                                        ),
                                    },
                                ),
                                right: Expression(
                                    PrimaryExpression(
                                        Identifier(
                                            Identifier {
                                                [RANGE]
                                                _id: 20,
                                                _parent: Some(
                                                    17,
                                                ),
                                            },
                                        ),
                                    ),
                                ),
                                [RANGE]
                                _id: 17,
                                _parent: Some(
                                    16,
                                ),
                            },
                        ),
                    ],
                    [RANGE]
                    _id: 16,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        AugmentedAssignment(
                            AugmentedAssignment {
                                left: Pattern(
                                    Identifier(
                                        Identifier {
                                            [RANGE]
                                            _id: 23,
                                            _parent: Some(
                                                22,
                                            ),
                                        },
                                    ),
                                ),
                                operator: Token_MinusEqual(
                                    Token_MinusEqual {
                                        [RANGE]
                                        _id: 24,
                                        _parent: Some(
                                            22,
                                        ),
                                    },
                                ),
                                right: Expression(
                                    PrimaryExpression(
                                        Integer(
                                            Integer {
                                                [RANGE]
                                                _id: 25,
                                                _parent: Some(
                                                    22,
                                                ),
                                            },
                                        ),
                                    ),
                                ),
                                [RANGE]
                                _id: 22,
                                _parent: Some(
                                    21,
                                ),
                            },
                        ),
                    ],
                    [RANGE]
                    _id: 21,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        AugmentedAssignment(
                            AugmentedAssignment {
                                left: Pattern(
                                    Identifier(
                                        Identifier {
                                            [RANGE]
                                            _id: 28,
                                            _parent: Some(
                                                27,
                                            ),
                                        },
                                    ),
                                ),
                                operator: Token_PercentEqual(
                                    Token_PercentEqual {
                                        [RANGE]
                                        _id: 29,
                                        _parent: Some(
                                            27,
                                        ),
                                    },
                                ),
                                right: Expression(
                                    PrimaryExpression(
                                        Integer(
                                            Integer {
                                                [RANGE]
                                                _id: 30,
                                                _parent: Some(
                                                    27,
                                                ),
                                            },
                                        ),
                                    ),
                                ),
                                [RANGE]
                                _id: 27,
                                _parent: Some(
                                    26,
                                ),
                            },
                        ),
                    ],
                    [RANGE]
                    _id: 26,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        AugmentedAssignment(
                            AugmentedAssignment {
                                left: Pattern(
                                    Identifier(
                                        Identifier {
                                            [RANGE]
                                            _id: 33,
                                            _parent: Some(
                                                32,
                                            ),
                                        },
                                    ),
                                ),
                                operator: Token_SlashEqual(
                                    Token_SlashEqual {
                                        [RANGE]
                                        _id: 34,
                                        _parent: Some(
                                            32,
                                        ),
                                    },
                                ),
                                right: Expression(
                                    PrimaryExpression(
                                        Integer(
                                            Integer {
                                                [RANGE]
                                                _id: 35,
                                                _parent: Some(
                                                    32,
                                                ),
                                            },
                                        ),
                                    ),
                                ),
                                [RANGE]
                                _id: 32,
                                _parent: Some(
                                    31,
                                ),
                            },
                        ),
                    ],
                    [RANGE]
                    _id: 31,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        AugmentedAssignment(
                            AugmentedAssignment {
                                left: Pattern(
                                    Identifier(
                                        Identifier {
                                            [RANGE]
                                            _id: 38,
                                            _parent: Some(
                                                37,
                                            ),
                                        },
                                    ),
                                ),
                                operator: Token_StarEqual(
                                    Token_StarEqual {
                                        [RANGE]
                                        _id: 39,
                                        _parent: Some(
                                            37,
                                        ),
                                    },
                                ),
                                right: Expression(
                                    PrimaryExpression(
                                        Float(
                                            Float {
                                                [RANGE]
                                                _id: 40,
                                                _parent: Some(
                                                    37,
                                                ),
                                            },
                                        ),
                                    ),
                                ),
                                [RANGE]
                                _id: 37,
                                _parent: Some(
                                    36,
                                ),
                            },
                        ),
                    ],
                    [RANGE]
                    _id: 36,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        AugmentedAssignment(
                            AugmentedAssignment {
                                left: Pattern(
                                    Identifier(
                                        Identifier {
                                            [RANGE]
                                            _id: 43,
                                            _parent: Some(
                                                42,
                                            ),
                                        },
                                    ),
                                ),
                                operator: Token_AmpersandEqual(
                                    Token_AmpersandEqual {
                                        [RANGE]
                                        _id: 44,
                                        _parent: Some(
                                            42,
                                        ),
                                    },
                                ),
                                right: Expression(
                                    PrimaryExpression(
                                        Integer(
                                            Integer {
                                                [RANGE]
                                                _id: 45,
                                                _parent: Some(
                                                    42,
                                                ),
                                            },
                                        ),
                                    ),
                                ),
                                [RANGE]
                                _id: 42,
                                _parent: Some(
                                    41,
                                ),
                            },
                        ),
                    ],
                    [RANGE]
                    _id: 41,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        AugmentedAssignment(
                            AugmentedAssignment {
                                left: Pattern(
                                    Identifier(
                                        Identifier {
                                            [RANGE]
                                            _id: 48,
                                            _parent: Some(
                                                47,
                                            ),
                                        },
                                    ),
                                ),
                                operator: Token_CaretEqual(
                                    Token_CaretEqual {
                                        [RANGE]
                                        _id: 49,
                                        _parent: Some(
                                            47,
                                        ),
                                    },
                                ),
                                right: Expression(
                                    PrimaryExpression(
                                        Integer(
                                            Integer {
                                                [RANGE]
                                                _id: 50,
                                                _parent: Some(
                                                    47,
                                                ),
                                            },
                                        ),
                                    ),
                                ),
                                [RANGE]
                                _id: 47,
                                _parent: Some(
                                    46,
                                ),
                            },
                        ),
                    ],
                    [RANGE]
                    _id: 46,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        AugmentedAssignment(
                            AugmentedAssignment {
                                left: Pattern(
                                    Identifier(
                                        Identifier {
                                            [RANGE]
                                            _id: 53,
                                            _parent: Some(
                                                52,
                                            ),
                                        },
                                    ),
                                ),
                                operator: Token_LessLessEqual(
                                    Token_LessLessEqual {
                                        [RANGE]
                                        _id: 54,
                                        _parent: Some(
                                            52,
                                        ),
                                    },
                                ),
                                right: Expression(
                                    PrimaryExpression(
                                        Integer(
                                            Integer {
                                                [RANGE]
                                                _id: 55,
                                                _parent: Some(
                                                    52,
                                                ),
                                            },
                                        ),
                                    ),
                                ),
                                [RANGE]
                                _id: 52,
                                _parent: Some(
                                    51,
                                ),
                            },
                        ),
                    ],
                    [RANGE]
                    _id: 51,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        AugmentedAssignment(
                            AugmentedAssignment {
                                left: Pattern(
                                    Identifier(
                                        Identifier {
                                            [RANGE]
                                            _id: 58,
                                            _parent: Some(
                                                57,
                                            ),
                                        },
                                    ),
                                ),
                                operator: Token_PipeEqual(
                                    Token_PipeEqual {
                                        [RANGE]
                                        _id: 59,
                                        _parent: Some(
                                            57,
                                        ),
                                    },
                                ),
                                right: Expression(
                                    PrimaryExpression(
                                        Identifier(
                                            Identifier {
                                                [RANGE]
                                                _id: 60,
                                                _parent: Some(
                                                    57,
                                                ),
                                            },
                                        ),
                                    ),
                                ),
                                [RANGE]
                                _id: 57,
                                _parent: Some(
                                    56,
                                ),
                            },
                        ),
                    ],
                    [RANGE]
                    _id: 56,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        AugmentedAssignment(
                            AugmentedAssignment {
                                left: Pattern(
                                    Identifier(
                                        Identifier {
                                            [RANGE]
                                            _id: 63,
                                            _parent: Some(
                                                62,
                                            ),
                                        },
                                    ),
                                ),
                                operator: Token_StarStarEqual(
                                    Token_StarStarEqual {
                                        [RANGE]
                                        _id: 64,
                                        _parent: Some(
                                            62,
                                        ),
                                    },
                                ),
                                right: Expression(
                                    PrimaryExpression(
                                        Integer(
                                            Integer {
                                                [RANGE]
                                                _id: 65,
                                                _parent: Some(
                                                    62,
                                                ),
                                            },
                                        ),
                                    ),
                                ),
                                [RANGE]
                                _id: 62,
                                _parent: Some(
                                    61,
                                ),
                            },
                        ),
                    ],
                    [RANGE]
                    _id: 61,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
