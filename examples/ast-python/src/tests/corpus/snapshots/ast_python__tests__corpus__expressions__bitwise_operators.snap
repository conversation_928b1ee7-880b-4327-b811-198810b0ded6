---
source: src/tests/corpus/expressions.rs
expression: module
---
Module {
    children: [
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                BinaryOperator(
                                    BinaryOperator {
                                        operator: Token_Pipe(
                                            Token_Pipe {
                                                [RANGE]
                                                _id: 7,
                                                _parent: Some(
                                                    2,
                                                ),
                                            },
                                        ),
                                        left: BinaryOperator(
                                            BinaryOperator {
                                                operator: Token_ShiftLeft(
                                                    Token_ShiftLeft {
                                                        [RANGE]
                                                        _id: 5,
                                                        _parent: Some(
                                                            3,
                                                        ),
                                                    },
                                                ),
                                                left: Identifier(
                                                    Identifier {
                                                        [RANGE]
                                                        _id: 4,
                                                        _parent: Some(
                                                            3,
                                                        ),
                                                    },
                                                ),
                                                right: Identifier(
                                                    Identifier {
                                                        [RANGE]
                                                        _id: 6,
                                                        _parent: Some(
                                                            3,
                                                        ),
                                                    },
                                                ),
                                                [RANGE]
                                                _id: 3,
                                                _parent: Some(
                                                    2,
                                                ),
                                            },
                                        ),
                                        right: BinaryOperator(
                                            BinaryOperator {
                                                operator: Token_Ampersand(
                                                    Token_Ampersand {
                                                        [RANGE]
                                                        _id: 13,
                                                        _parent: Some(
                                                            8,
                                                        ),
                                                    },
                                                ),
                                                left: BinaryOperator(
                                                    BinaryOperator {
                                                        operator: Token_ShiftRight(
                                                            Token_ShiftRight {
                                                                [RANGE]
                                                                _id: 11,
                                                                _parent: Some(
                                                                    9,
                                                                ),
                                                            },
                                                        ),
                                                        left: Identifier(
                                                            Identifier {
                                                                [RANGE]
                                                                _id: 10,
                                                                _parent: Some(
                                                                    9,
                                                                ),
                                                            },
                                                        ),
                                                        right: Identifier(
                                                            Identifier {
                                                                [RANGE]
                                                                _id: 12,
                                                                _parent: Some(
                                                                    9,
                                                                ),
                                                            },
                                                        ),
                                                        [RANGE]
                                                        _id: 9,
                                                        _parent: Some(
                                                            8,
                                                        ),
                                                    },
                                                ),
                                                right: BinaryOperator(
                                                    BinaryOperator {
                                                        operator: Token_Caret(
                                                            Token_Caret {
                                                                [RANGE]
                                                                _id: 16,
                                                                _parent: Some(
                                                                    14,
                                                                ),
                                                            },
                                                        ),
                                                        left: Identifier(
                                                            Identifier {
                                                                [RANGE]
                                                                _id: 15,
                                                                _parent: Some(
                                                                    14,
                                                                ),
                                                            },
                                                        ),
                                                        right: Identifier(
                                                            Identifier {
                                                                [RANGE]
                                                                _id: 17,
                                                                _parent: Some(
                                                                    14,
                                                                ),
                                                            },
                                                        ),
                                                        [RANGE]
                                                        _id: 14,
                                                        _parent: Some(
                                                            8,
                                                        ),
                                                    },
                                                ),
                                                [RANGE]
                                                _id: 8,
                                                _parent: Some(
                                                    2,
                                                ),
                                            },
                                        ),
                                        [RANGE]
                                        _id: 2,
                                        _parent: Some(
                                            1,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
