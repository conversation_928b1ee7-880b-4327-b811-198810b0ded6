---
source: src/tests/corpus/expressions.rs
expression: module
---
Module {
    children: [
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                Call(
                                    Call {
                                        function: Identifier(
                                            Identifier {
                                                [RANGE]
                                                _id: 3,
                                                _parent: Some(
                                                    2,
                                                ),
                                            },
                                        ),
                                        arguments: ArgumentList(
                                            ArgumentList {
                                                children: [
                                                    ListSplat(
                                                        ListSplat {
                                                            children: Expression(
                                                                PrimaryExpression(
                                                                    Tuple(
                                                                        Tuple {
                                                                            children: [],
                                                                            [RANGE]
                                                                            _id: 6,
                                                                            _parent: Some(
                                                                                5,
                                                                            ),
                                                                        },
                                                                    ),
                                                                ),
                                                            ),
                                                            [RANGE]
                                                            _id: 5,
                                                            _parent: Some(
                                                                4,
                                                            ),
                                                        },
                                                    ),
                                                ],
                                                [RANGE]
                                                _id: 4,
                                                _parent: Some(
                                                    2,
                                                ),
                                            },
                                        ),
                                        [RANGE]
                                        _id: 2,
                                        _parent: Some(
                                            1,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                Call(
                                    Call {
                                        function: Identifier(
                                            Identifier {
                                                [RANGE]
                                                _id: 9,
                                                _parent: Some(
                                                    8,
                                                ),
                                            },
                                        ),
                                        arguments: ArgumentList(
                                            ArgumentList {
                                                children: [
                                                    DictionarySplat(
                                                        DictionarySplat {
                                                            children: PrimaryExpression(
                                                                Dictionary(
                                                                    Dictionary {
                                                                        children: [],
                                                                        [RANGE]
                                                                        _id: 12,
                                                                        _parent: Some(
                                                                            11,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                            [RANGE]
                                                            _id: 11,
                                                            _parent: Some(
                                                                10,
                                                            ),
                                                        },
                                                    ),
                                                ],
                                                [RANGE]
                                                _id: 10,
                                                _parent: Some(
                                                    8,
                                                ),
                                            },
                                        ),
                                        [RANGE]
                                        _id: 8,
                                        _parent: Some(
                                            7,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 7,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                Call(
                                    Call {
                                        function: Identifier(
                                            Identifier {
                                                [RANGE]
                                                _id: 15,
                                                _parent: Some(
                                                    14,
                                                ),
                                            },
                                        ),
                                        arguments: ArgumentList(
                                            ArgumentList {
                                                children: [
                                                    ListSplat(
                                                        ListSplat {
                                                            children: Identifier(
                                                                Identifier {
                                                                    [RANGE]
                                                                    _id: 18,
                                                                    _parent: Some(
                                                                        17,
                                                                    ),
                                                                },
                                                            ),
                                                            [RANGE]
                                                            _id: 17,
                                                            _parent: Some(
                                                                16,
                                                            ),
                                                        },
                                                    ),
                                                ],
                                                [RANGE]
                                                _id: 16,
                                                _parent: Some(
                                                    14,
                                                ),
                                            },
                                        ),
                                        [RANGE]
                                        _id: 14,
                                        _parent: Some(
                                            13,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 13,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                Call(
                                    Call {
                                        function: Identifier(
                                            Identifier {
                                                [RANGE]
                                                _id: 21,
                                                _parent: Some(
                                                    20,
                                                ),
                                            },
                                        ),
                                        arguments: ArgumentList(
                                            ArgumentList {
                                                children: [
                                                    Expression(
                                                        PrimaryExpression(
                                                            Identifier(
                                                                Identifier {
                                                                    [RANGE]
                                                                    _id: 23,
                                                                    _parent: Some(
                                                                        22,
                                                                    ),
                                                                },
                                                            ),
                                                        ),
                                                    ),
                                                    ListSplat(
                                                        ListSplat {
                                                            children: Identifier(
                                                                Identifier {
                                                                    [RANGE]
                                                                    _id: 25,
                                                                    _parent: Some(
                                                                        24,
                                                                    ),
                                                                },
                                                            ),
                                                            [RANGE]
                                                            _id: 24,
                                                            _parent: Some(
                                                                22,
                                                            ),
                                                        },
                                                    ),
                                                    DictionarySplat(
                                                        DictionarySplat {
                                                            children: PrimaryExpression(
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 27,
                                                                        _parent: Some(
                                                                            26,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                            [RANGE]
                                                            _id: 26,
                                                            _parent: Some(
                                                                22,
                                                            ),
                                                        },
                                                    ),
                                                ],
                                                [RANGE]
                                                _id: 22,
                                                _parent: Some(
                                                    20,
                                                ),
                                            },
                                        ),
                                        [RANGE]
                                        _id: 20,
                                        _parent: Some(
                                            19,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 19,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
