---
source: src/tests/corpus/expressions.rs
expression: module
---
Module {
    children: [
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            Lambda(
                                Lambda {
                                    body: PrimaryExpression(
                                        Call(
                                            Call {
                                                function: Identifier(
                                                    Identifier {
                                                        [RANGE]
                                                        _id: 7,
                                                        _parent: Some(
                                                            6,
                                                        ),
                                                    },
                                                ),
                                                arguments: ArgumentList(
                                                    ArgumentList {
                                                        children: [
                                                            Expression(
                                                                PrimaryExpression(
                                                                    BinaryOperator(
                                                                        BinaryOperator {
                                                                            operator: Token_Percent(
                                                                                Token_Percent {
                                                                                    [RANGE]
                                                                                    _id: 14,
                                                                                    _parent: Some(
                                                                                        9,
                                                                                    ),
                                                                                },
                                                                            ),
                                                                            left: String(
                                                                                String {
                                                                                    children: [
                                                                                        StringStart(
                                                                                            StringStart {
                                                                                                [RANGE]
                                                                                                _id: 11,
                                                                                                _parent: Some(
                                                                                                    10,
                                                                                                ),
                                                                                            },
                                                                                        ),
                                                                                        StringContent(
                                                                                            StringContent {
                                                                                                children: [],
                                                                                                [RANGE]
                                                                                                _id: 12,
                                                                                                _parent: Some(
                                                                                                    10,
                                                                                                ),
                                                                                            },
                                                                                        ),
                                                                                        StringEnd(
                                                                                            StringEnd {
                                                                                                [RANGE]
                                                                                                _id: 13,
                                                                                                _parent: Some(
                                                                                                    10,
                                                                                                ),
                                                                                            },
                                                                                        ),
                                                                                    ],
                                                                                    [RANGE]
                                                                                    _id: 10,
                                                                                    _parent: Some(
                                                                                        9,
                                                                                    ),
                                                                                },
                                                                            ),
                                                                            right: Identifier(
                                                                                Identifier {
                                                                                    [RANGE]
                                                                                    _id: 15,
                                                                                    _parent: Some(
                                                                                        9,
                                                                                    ),
                                                                                },
                                                                            ),
                                                                            [RANGE]
                                                                            _id: 9,
                                                                            _parent: Some(
                                                                                8,
                                                                            ),
                                                                        },
                                                                    ),
                                                                ),
                                                            ),
                                                        ],
                                                        [RANGE]
                                                        _id: 8,
                                                        _parent: Some(
                                                            6,
                                                        ),
                                                    },
                                                ),
                                                [RANGE]
                                                _id: 6,
                                                _parent: Some(
                                                    2,
                                                ),
                                            },
                                        ),
                                    ),
                                    parameters: Some(
                                        LambdaParameters {
                                            children: [
                                                Identifier(
                                                    Identifier {
                                                        [RANGE]
                                                        _id: 4,
                                                        _parent: Some(
                                                            3,
                                                        ),
                                                    },
                                                ),
                                                Identifier(
                                                    Identifier {
                                                        [RANGE]
                                                        _id: 5,
                                                        _parent: Some(
                                                            3,
                                                        ),
                                                    },
                                                ),
                                            ],
                                            [RANGE]
                                            _id: 3,
                                            _parent: Some(
                                                2,
                                            ),
                                        },
                                    ),
                                    [RANGE]
                                    _id: 2,
                                    _parent: Some(
                                        1,
                                    ),
                                },
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            Lambda(
                                Lambda {
                                    body: PrimaryExpression(
                                        True(
                                            True {
                                                [RANGE]
                                                _id: 18,
                                                _parent: Some(
                                                    17,
                                                ),
                                            },
                                        ),
                                    ),
                                    parameters: None,
                                    [RANGE]
                                    _id: 17,
                                    _parent: Some(
                                        16,
                                    ),
                                },
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 16,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            Lambda(
                                Lambda {
                                    body: PrimaryExpression(
                                        Identifier(
                                            Identifier {
                                                [RANGE]
                                                _id: 30,
                                                _parent: Some(
                                                    20,
                                                ),
                                            },
                                        ),
                                    ),
                                    parameters: Some(
                                        LambdaParameters {
                                            children: [
                                                Identifier(
                                                    Identifier {
                                                        [RANGE]
                                                        _id: 22,
                                                        _parent: Some(
                                                            21,
                                                        ),
                                                    },
                                                ),
                                                DefaultParameter(
                                                    DefaultParameter {
                                                        value: PrimaryExpression(
                                                            Identifier(
                                                                Identifier {
                                                                    [RANGE]
                                                                    _id: 25,
                                                                    _parent: Some(
                                                                        23,
                                                                    ),
                                                                },
                                                            ),
                                                        ),
                                                        name: Identifier(
                                                            Identifier {
                                                                [RANGE]
                                                                _id: 24,
                                                                _parent: Some(
                                                                    23,
                                                                ),
                                                            },
                                                        ),
                                                        [RANGE]
                                                        _id: 23,
                                                        _parent: Some(
                                                            21,
                                                        ),
                                                    },
                                                ),
                                                ListSplatPattern(
                                                    ListSplatPattern {
                                                        children: Identifier(
                                                            Identifier {
                                                                [RANGE]
                                                                _id: 27,
                                                                _parent: Some(
                                                                    26,
                                                                ),
                                                            },
                                                        ),
                                                        [RANGE]
                                                        _id: 26,
                                                        _parent: Some(
                                                            21,
                                                        ),
                                                    },
                                                ),
                                                DictionarySplatPattern(
                                                    DictionarySplatPattern {
                                                        children: Identifier(
                                                            Identifier {
                                                                [RANGE]
                                                                _id: 29,
                                                                _parent: Some(
                                                                    28,
                                                                ),
                                                            },
                                                        ),
                                                        [RANGE]
                                                        _id: 28,
                                                        _parent: Some(
                                                            21,
                                                        ),
                                                    },
                                                ),
                                            ],
                                            [RANGE]
                                            _id: 21,
                                            _parent: Some(
                                                20,
                                            ),
                                        },
                                    ),
                                    [RANGE]
                                    _id: 20,
                                    _parent: Some(
                                        19,
                                    ),
                                },
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 19,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            Lambda(
                                Lambda {
                                    body: PrimaryExpression(
                                        Tuple(
                                            Tuple {
                                                children: [
                                                    Expression(
                                                        PrimaryExpression(
                                                            Identifier(
                                                                Identifier {
                                                                    [RANGE]
                                                                    _id: 38,
                                                                    _parent: Some(
                                                                        37,
                                                                    ),
                                                                },
                                                            ),
                                                        ),
                                                    ),
                                                    Expression(
                                                        PrimaryExpression(
                                                            Identifier(
                                                                Identifier {
                                                                    [RANGE]
                                                                    _id: 39,
                                                                    _parent: Some(
                                                                        37,
                                                                    ),
                                                                },
                                                            ),
                                                        ),
                                                    ),
                                                ],
                                                [RANGE]
                                                _id: 37,
                                                _parent: Some(
                                                    32,
                                                ),
                                            },
                                        ),
                                    ),
                                    parameters: Some(
                                        LambdaParameters {
                                            children: [
                                                TuplePattern(
                                                    TuplePattern {
                                                        children: [
                                                            Pattern(
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 35,
                                                                        _parent: Some(
                                                                            34,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                            Pattern(
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 36,
                                                                        _parent: Some(
                                                                            34,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ],
                                                        [RANGE]
                                                        _id: 34,
                                                        _parent: Some(
                                                            33,
                                                        ),
                                                    },
                                                ),
                                            ],
                                            [RANGE]
                                            _id: 33,
                                            _parent: Some(
                                                32,
                                            ),
                                        },
                                    ),
                                    [RANGE]
                                    _id: 32,
                                    _parent: Some(
                                        31,
                                    ),
                                },
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 31,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
