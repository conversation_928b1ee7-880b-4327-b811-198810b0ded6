---
source: src/tests/corpus/expressions.rs
expression: module
---
Module {
    children: [
        CompoundStatement(
            FunctionDefinition(
                FunctionDefinition {
                    body: Block {
                        alternative: [],
                        children: [
                            SimpleStatement(
                                ExpressionStatement(
                                    ExpressionStatement {
                                        children: [
                                            Expression(
                                                PrimaryExpression(
                                                    Identifier(
                                                        Identifier {
                                                            [RANGE]
                                                            _id: 7,
                                                            _parent: Some(
                                                                6,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 6,
                                        _parent: Some(
                                            5,
                                        ),
                                    },
                                ),
                            ),
                        ],
                        [RANGE]
                        _id: 5,
                        _parent: Some(
                            1,
                        ),
                    },
                    name: Identifier {
                        [RANGE]
                        _id: 2,
                        _parent: Some(
                            1,
                        ),
                    },
                    parameters: Parameters {
                        children: [
                            Identifier(
                                Identifier {
                                    [RANGE]
                                    _id: 4,
                                    _parent: Some(
                                        3,
                                    ),
                                },
                            ),
                        ],
                        [RANGE]
                        _id: 3,
                        _parent: Some(
                            1,
                        ),
                    },
                    type_parameters: None,
                    return_type: None,
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        CompoundStatement(
            FunctionDefinition(
                FunctionDefinition {
                    body: Block {
                        alternative: [],
                        children: [
                            SimpleStatement(
                                ExpressionStatement(
                                    ExpressionStatement {
                                        children: [
                                            Expression(
                                                PrimaryExpression(
                                                    Identifier(
                                                        Identifier {
                                                            [RANGE]
                                                            _id: 16,
                                                            _parent: Some(
                                                                15,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 15,
                                        _parent: Some(
                                            14,
                                        ),
                                    },
                                ),
                            ),
                        ],
                        [RANGE]
                        _id: 14,
                        _parent: Some(
                            8,
                        ),
                    },
                    name: Identifier {
                        [RANGE]
                        _id: 9,
                        _parent: Some(
                            8,
                        ),
                    },
                    parameters: Parameters {
                        children: [
                            DefaultParameter(
                                DefaultParameter {
                                    value: PrimaryExpression(
                                        Identifier(
                                            Identifier {
                                                [RANGE]
                                                _id: 13,
                                                _parent: Some(
                                                    11,
                                                ),
                                            },
                                        ),
                                    ),
                                    name: Identifier(
                                        Identifier {
                                            [RANGE]
                                            _id: 12,
                                            _parent: Some(
                                                11,
                                            ),
                                        },
                                    ),
                                    [RANGE]
                                    _id: 11,
                                    _parent: Some(
                                        10,
                                    ),
                                },
                            ),
                        ],
                        [RANGE]
                        _id: 10,
                        _parent: Some(
                            8,
                        ),
                    },
                    type_parameters: None,
                    return_type: None,
                    [RANGE]
                    _id: 8,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        CompoundStatement(
            FunctionDefinition(
                FunctionDefinition {
                    body: Block {
                        alternative: [],
                        children: [
                            SimpleStatement(
                                ExpressionStatement(
                                    ExpressionStatement {
                                        children: [
                                            Expression(
                                                PrimaryExpression(
                                                    Identifier(
                                                        Identifier {
                                                            [RANGE]
                                                            _id: 24,
                                                            _parent: Some(
                                                                23,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 23,
                                        _parent: Some(
                                            22,
                                        ),
                                    },
                                ),
                            ),
                        ],
                        [RANGE]
                        _id: 22,
                        _parent: Some(
                            17,
                        ),
                    },
                    name: Identifier {
                        [RANGE]
                        _id: 18,
                        _parent: Some(
                            17,
                        ),
                    },
                    parameters: Parameters {
                        children: [
                            ListSplatPattern(
                                ListSplatPattern {
                                    children: Identifier(
                                        Identifier {
                                            [RANGE]
                                            _id: 21,
                                            _parent: Some(
                                                20,
                                            ),
                                        },
                                    ),
                                    [RANGE]
                                    _id: 20,
                                    _parent: Some(
                                        19,
                                    ),
                                },
                            ),
                        ],
                        [RANGE]
                        _id: 19,
                        _parent: Some(
                            17,
                        ),
                    },
                    type_parameters: None,
                    return_type: None,
                    [RANGE]
                    _id: 17,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        CompoundStatement(
            FunctionDefinition(
                FunctionDefinition {
                    body: Block {
                        alternative: [],
                        children: [
                            SimpleStatement(
                                ExpressionStatement(
                                    ExpressionStatement {
                                        children: [
                                            Expression(
                                                PrimaryExpression(
                                                    Identifier(
                                                        Identifier {
                                                            [RANGE]
                                                            _id: 32,
                                                            _parent: Some(
                                                                31,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 31,
                                        _parent: Some(
                                            30,
                                        ),
                                    },
                                ),
                            ),
                        ],
                        [RANGE]
                        _id: 30,
                        _parent: Some(
                            25,
                        ),
                    },
                    name: Identifier {
                        [RANGE]
                        _id: 26,
                        _parent: Some(
                            25,
                        ),
                    },
                    parameters: Parameters {
                        children: [
                            DictionarySplatPattern(
                                DictionarySplatPattern {
                                    children: Identifier(
                                        Identifier {
                                            [RANGE]
                                            _id: 29,
                                            _parent: Some(
                                                28,
                                            ),
                                        },
                                    ),
                                    [RANGE]
                                    _id: 28,
                                    _parent: Some(
                                        27,
                                    ),
                                },
                            ),
                        ],
                        [RANGE]
                        _id: 27,
                        _parent: Some(
                            25,
                        ),
                    },
                    type_parameters: None,
                    return_type: None,
                    [RANGE]
                    _id: 25,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        CompoundStatement(
            FunctionDefinition(
                FunctionDefinition {
                    body: Block {
                        alternative: [],
                        children: [
                            SimpleStatement(
                                ExpressionStatement(
                                    ExpressionStatement {
                                        children: [
                                            Expression(
                                                PrimaryExpression(
                                                    Identifier(
                                                        Identifier {
                                                            [RANGE]
                                                            _id: 38,
                                                            _parent: Some(
                                                                37,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 37,
                                        _parent: Some(
                                            36,
                                        ),
                                    },
                                ),
                            ),
                        ],
                        [RANGE]
                        _id: 36,
                        _parent: Some(
                            33,
                        ),
                    },
                    name: Identifier {
                        [RANGE]
                        _id: 34,
                        _parent: Some(
                            33,
                        ),
                    },
                    parameters: Parameters {
                        children: [],
                        [RANGE]
                        _id: 35,
                        _parent: Some(
                            33,
                        ),
                    },
                    type_parameters: None,
                    return_type: None,
                    [RANGE]
                    _id: 33,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
