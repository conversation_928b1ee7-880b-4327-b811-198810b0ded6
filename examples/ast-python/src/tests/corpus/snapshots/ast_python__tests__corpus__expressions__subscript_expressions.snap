---
source: src/tests/corpus/expressions.rs
expression: module
---
Module {
    children: [
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                Subscript(
                                    Subscript {
                                        subscript: [
                                            Expression(
                                                PrimaryExpression(
                                                    Integer(
                                                        Integer {
                                                            [RANGE]
                                                            _id: 4,
                                                            _parent: Some(
                                                                2,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ),
                                        ],
                                        value: Identifier(
                                            Identifier {
                                                [RANGE]
                                                _id: 3,
                                                _parent: Some(
                                                    2,
                                                ),
                                            },
                                        ),
                                        [RANGE]
                                        _id: 2,
                                        _parent: Some(
                                            1,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                Subscript(
                                    Subscript {
                                        subscript: [
                                            Expression(
                                                PrimaryExpression(
                                                    Integer(
                                                        Integer {
                                                            [RANGE]
                                                            _id: 8,
                                                            _parent: Some(
                                                                6,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ),
                                            Expression(
                                                PrimaryExpression(
                                                    Integer(
                                                        Integer {
                                                            [RANGE]
                                                            _id: 9,
                                                            _parent: Some(
                                                                6,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ),
                                        ],
                                        value: Identifier(
                                            Identifier {
                                                [RANGE]
                                                _id: 7,
                                                _parent: Some(
                                                    6,
                                                ),
                                            },
                                        ),
                                        [RANGE]
                                        _id: 6,
                                        _parent: Some(
                                            5,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 5,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                Subscript(
                                    Subscript {
                                        subscript: [
                                            Expression(
                                                PrimaryExpression(
                                                    Integer(
                                                        Integer {
                                                            [RANGE]
                                                            _id: 13,
                                                            _parent: Some(
                                                                11,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ),
                                            Expression(
                                                PrimaryExpression(
                                                    Integer(
                                                        Integer {
                                                            [RANGE]
                                                            _id: 14,
                                                            _parent: Some(
                                                                11,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ),
                                        ],
                                        value: Identifier(
                                            Identifier {
                                                [RANGE]
                                                _id: 12,
                                                _parent: Some(
                                                    11,
                                                ),
                                            },
                                        ),
                                        [RANGE]
                                        _id: 11,
                                        _parent: Some(
                                            10,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 10,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
