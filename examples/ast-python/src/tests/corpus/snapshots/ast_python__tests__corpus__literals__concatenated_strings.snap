---
source: src/tests/corpus/literals.rs
expression: module
---
Module {
    children: [
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                ConcatenatedString(
                                    ConcatenatedString {
                                        children: [
                                            String {
                                                children: [
                                                    StringStart(
                                                        StringStart {
                                                            [RANGE]
                                                            _id: 4,
                                                            _parent: Some(
                                                                3,
                                                            ),
                                                        },
                                                    ),
                                                    StringContent(
                                                        StringContent {
                                                            children: [],
                                                            [RANGE]
                                                            _id: 5,
                                                            _parent: Some(
                                                                3,
                                                            ),
                                                        },
                                                    ),
                                                    StringEnd(
                                                        StringEnd {
                                                            [RANGE]
                                                            _id: 6,
                                                            _parent: Some(
                                                                3,
                                                            ),
                                                        },
                                                    ),
                                                ],
                                                [RANGE]
                                                _id: 3,
                                                _parent: Some(
                                                    2,
                                                ),
                                            },
                                            String {
                                                children: [
                                                    StringStart(
                                                        StringStart {
                                                            [RANGE]
                                                            _id: 8,
                                                            _parent: Some(
                                                                7,
                                                            ),
                                                        },
                                                    ),
                                                    StringContent(
                                                        StringContent {
                                                            children: [],
                                                            [RANGE]
                                                            _id: 9,
                                                            _parent: Some(
                                                                7,
                                                            ),
                                                        },
                                                    ),
                                                    StringEnd(
                                                        StringEnd {
                                                            [RANGE]
                                                            _id: 10,
                                                            _parent: Some(
                                                                7,
                                                            ),
                                                        },
                                                    ),
                                                ],
                                                [RANGE]
                                                _id: 7,
                                                _parent: Some(
                                                    2,
                                                ),
                                            },
                                            String {
                                                children: [
                                                    StringStart(
                                                        StringStart {
                                                            [RANGE]
                                                            _id: 12,
                                                            _parent: Some(
                                                                11,
                                                            ),
                                                        },
                                                    ),
                                                    StringContent(
                                                        StringContent {
                                                            children: [],
                                                            [RANGE]
                                                            _id: 13,
                                                            _parent: Some(
                                                                11,
                                                            ),
                                                        },
                                                    ),
                                                    StringEnd(
                                                        StringEnd {
                                                            [RANGE]
                                                            _id: 14,
                                                            _parent: Some(
                                                                11,
                                                            ),
                                                        },
                                                    ),
                                                ],
                                                [RANGE]
                                                _id: 11,
                                                _parent: Some(
                                                    2,
                                                ),
                                            },
                                        ],
                                        [RANGE]
                                        _id: 2,
                                        _parent: Some(
                                            1,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
