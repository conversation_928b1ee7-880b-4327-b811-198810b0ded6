---
source: src/tests/corpus/literals.rs
expression: module
---
Module {
    children: [
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                DictionaryComprehension(
                                    DictionaryComprehension {
                                        body: Pair {
                                            key: PrimaryExpression(
                                                Identifier(
                                                    Identifier {
                                                        [RANGE]
                                                        _id: 4,
                                                        _parent: Some(
                                                            3,
                                                        ),
                                                    },
                                                ),
                                            ),
                                            value: PrimaryExpression(
                                                Identifier(
                                                    Identifier {
                                                        [RANGE]
                                                        _id: 5,
                                                        _parent: Some(
                                                            3,
                                                        ),
                                                    },
                                                ),
                                            ),
                                            [RANGE]
                                            _id: 3,
                                            _parent: Some(
                                                2,
                                            ),
                                        },
                                        children: [
                                            ForInClause(
                                                ForInClause {
                                                    right: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 10,
                                                                        _parent: Some(
                                                                            6,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    left: PatternList(
                                                        PatternList {
                                                            children: [
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 8,
                                                                        _parent: Some(
                                                                            7,
                                                                        ),
                                                                    },
                                                                ),
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 9,
                                                                        _parent: Some(
                                                                            7,
                                                                        ),
                                                                    },
                                                                ),
                                                            ],
                                                            [RANGE]
                                                            _id: 7,
                                                            _parent: Some(
                                                                6,
                                                            ),
                                                        },
                                                    ),
                                                    [RANGE]
                                                    _id: 6,
                                                    _parent: Some(
                                                        2,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 2,
                                        _parent: Some(
                                            1,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                DictionaryComprehension(
                                    DictionaryComprehension {
                                        body: Pair {
                                            key: PrimaryExpression(
                                                Identifier(
                                                    Identifier {
                                                        [RANGE]
                                                        _id: 14,
                                                        _parent: Some(
                                                            13,
                                                        ),
                                                    },
                                                ),
                                            ),
                                            value: PrimaryExpression(
                                                Identifier(
                                                    Identifier {
                                                        [RANGE]
                                                        _id: 15,
                                                        _parent: Some(
                                                            13,
                                                        ),
                                                    },
                                                ),
                                            ),
                                            [RANGE]
                                            _id: 13,
                                            _parent: Some(
                                                12,
                                            ),
                                        },
                                        children: [
                                            ForInClause(
                                                ForInClause {
                                                    right: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 18,
                                                                        _parent: Some(
                                                                            16,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    left: Pattern(
                                                        Identifier(
                                                            Identifier {
                                                                [RANGE]
                                                                _id: 17,
                                                                _parent: Some(
                                                                    16,
                                                                ),
                                                            },
                                                        ),
                                                    ),
                                                    [RANGE]
                                                    _id: 16,
                                                    _parent: Some(
                                                        12,
                                                    ),
                                                },
                                            ),
                                            ForInClause(
                                                ForInClause {
                                                    right: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 21,
                                                                        _parent: Some(
                                                                            19,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    left: Pattern(
                                                        Identifier(
                                                            Identifier {
                                                                [RANGE]
                                                                _id: 20,
                                                                _parent: Some(
                                                                    19,
                                                                ),
                                                            },
                                                        ),
                                                    ),
                                                    [RANGE]
                                                    _id: 19,
                                                    _parent: Some(
                                                        12,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 12,
                                        _parent: Some(
                                            11,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 11,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
