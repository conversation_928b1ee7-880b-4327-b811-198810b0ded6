---
source: src/tests/corpus/literals.rs
expression: module
---
Module {
    children: [
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                String(
                                    String {
                                        children: [
                                            StringStart(
                                                StringStart {
                                                    [RANGE]
                                                    _id: 3,
                                                    _parent: Some(
                                                        2,
                                                    ),
                                                },
                                            ),
                                            StringContent(
                                                StringContent {
                                                    children: [],
                                                    [RANGE]
                                                    _id: 4,
                                                    _parent: Some(
                                                        2,
                                                    ),
                                                },
                                            ),
                                            Interpolation(
                                                Interpolation {
                                                    format_specifier: Some(
                                                        FormatSpecifier {
                                                            children: [],
                                                            [RANGE]
                                                            _id: 7,
                                                            _parent: Some(
                                                                5,
                                                            ),
                                                        },
                                                    ),
                                                    type_conversion: None,
                                                    expression: Expression(
                                                        PrimaryExpression(
                                                            Identifier(
                                                                Identifier {
                                                                    [RANGE]
                                                                    _id: 6,
                                                                    _parent: Some(
                                                                        5,
                                                                    ),
                                                                },
                                                            ),
                                                        ),
                                                    ),
                                                    [RANGE]
                                                    _id: 5,
                                                    _parent: Some(
                                                        2,
                                                    ),
                                                },
                                            ),
                                            StringContent(
                                                StringContent {
                                                    children: [],
                                                    [RANGE]
                                                    _id: 8,
                                                    _parent: Some(
                                                        2,
                                                    ),
                                                },
                                            ),
                                            Interpolation(
                                                Interpolation {
                                                    format_specifier: Some(
                                                        FormatSpecifier {
                                                            children: [],
                                                            [RANGE]
                                                            _id: 11,
                                                            _parent: Some(
                                                                9,
                                                            ),
                                                        },
                                                    ),
                                                    type_conversion: None,
                                                    expression: Expression(
                                                        PrimaryExpression(
                                                            Identifier(
                                                                Identifier {
                                                                    [RANGE]
                                                                    _id: 10,
                                                                    _parent: Some(
                                                                        9,
                                                                    ),
                                                                },
                                                            ),
                                                        ),
                                                    ),
                                                    [RANGE]
                                                    _id: 9,
                                                    _parent: Some(
                                                        2,
                                                    ),
                                                },
                                            ),
                                            StringEnd(
                                                StringEnd {
                                                    [RANGE]
                                                    _id: 12,
                                                    _parent: Some(
                                                        2,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 2,
                                        _parent: Some(
                                            1,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                String(
                                    String {
                                        children: [
                                            StringStart(
                                                StringStart {
                                                    [RANGE]
                                                    _id: 15,
                                                    _parent: Some(
                                                        14,
                                                    ),
                                                },
                                            ),
                                            Interpolation(
                                                Interpolation {
                                                    format_specifier: Some(
                                                        FormatSpecifier {
                                                            children: [
                                                                FormatExpression {
                                                                    expression: Expression(
                                                                        PrimaryExpression(
                                                                            Attribute(
                                                                                Attribute {
                                                                                    object: Identifier(
                                                                                        Identifier {
                                                                                            [RANGE]
                                                                                            _id: 21,
                                                                                            _parent: Some(
                                                                                                20,
                                                                                            ),
                                                                                        },
                                                                                    ),
                                                                                    attribute: Identifier {
                                                                                        [RANGE]
                                                                                        _id: 22,
                                                                                        _parent: Some(
                                                                                            20,
                                                                                        ),
                                                                                    },
                                                                                    [RANGE]
                                                                                    _id: 20,
                                                                                    _parent: Some(
                                                                                        19,
                                                                                    ),
                                                                                },
                                                                            ),
                                                                        ),
                                                                    ),
                                                                    format_specifier: None,
                                                                    type_conversion: None,
                                                                    [RANGE]
                                                                    _id: 19,
                                                                    _parent: Some(
                                                                        18,
                                                                    ),
                                                                },
                                                                FormatExpression {
                                                                    expression: Expression(
                                                                        PrimaryExpression(
                                                                            Attribute(
                                                                                Attribute {
                                                                                    object: Identifier(
                                                                                        Identifier {
                                                                                            [RANGE]
                                                                                            _id: 25,
                                                                                            _parent: Some(
                                                                                                24,
                                                                                            ),
                                                                                        },
                                                                                    ),
                                                                                    attribute: Identifier {
                                                                                        [RANGE]
                                                                                        _id: 26,
                                                                                        _parent: Some(
                                                                                            24,
                                                                                        ),
                                                                                    },
                                                                                    [RANGE]
                                                                                    _id: 24,
                                                                                    _parent: Some(
                                                                                        23,
                                                                                    ),
                                                                                },
                                                                            ),
                                                                        ),
                                                                    ),
                                                                    format_specifier: None,
                                                                    type_conversion: None,
                                                                    [RANGE]
                                                                    _id: 23,
                                                                    _parent: Some(
                                                                        18,
                                                                    ),
                                                                },
                                                            ],
                                                            [RANGE]
                                                            _id: 18,
                                                            _parent: Some(
                                                                16,
                                                            ),
                                                        },
                                                    ),
                                                    type_conversion: None,
                                                    expression: Expression(
                                                        PrimaryExpression(
                                                            Identifier(
                                                                Identifier {
                                                                    [RANGE]
                                                                    _id: 17,
                                                                    _parent: Some(
                                                                        16,
                                                                    ),
                                                                },
                                                            ),
                                                        ),
                                                    ),
                                                    [RANGE]
                                                    _id: 16,
                                                    _parent: Some(
                                                        14,
                                                    ),
                                                },
                                            ),
                                            StringEnd(
                                                StringEnd {
                                                    [RANGE]
                                                    _id: 27,
                                                    _parent: Some(
                                                        14,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 14,
                                        _parent: Some(
                                            13,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 13,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                String(
                                    String {
                                        children: [
                                            StringStart(
                                                StringStart {
                                                    [RANGE]
                                                    _id: 30,
                                                    _parent: Some(
                                                        29,
                                                    ),
                                                },
                                            ),
                                            Interpolation(
                                                Interpolation {
                                                    format_specifier: Some(
                                                        FormatSpecifier {
                                                            children: [],
                                                            [RANGE]
                                                            _id: 33,
                                                            _parent: Some(
                                                                31,
                                                            ),
                                                        },
                                                    ),
                                                    type_conversion: None,
                                                    expression: Expression(
                                                        PrimaryExpression(
                                                            Identifier(
                                                                Identifier {
                                                                    [RANGE]
                                                                    _id: 32,
                                                                    _parent: Some(
                                                                        31,
                                                                    ),
                                                                },
                                                            ),
                                                        ),
                                                    ),
                                                    [RANGE]
                                                    _id: 31,
                                                    _parent: Some(
                                                        29,
                                                    ),
                                                },
                                            ),
                                            StringEnd(
                                                StringEnd {
                                                    [RANGE]
                                                    _id: 34,
                                                    _parent: Some(
                                                        29,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 29,
                                        _parent: Some(
                                            28,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 28,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                String(
                                    String {
                                        children: [
                                            StringStart(
                                                StringStart {
                                                    [RANGE]
                                                    _id: 37,
                                                    _parent: Some(
                                                        36,
                                                    ),
                                                },
                                            ),
                                            Interpolation(
                                                Interpolation {
                                                    format_specifier: None,
                                                    type_conversion: None,
                                                    expression: Expression(
                                                        PrimaryExpression(
                                                            Identifier(
                                                                Identifier {
                                                                    [RANGE]
                                                                    _id: 39,
                                                                    _parent: Some(
                                                                        38,
                                                                    ),
                                                                },
                                                            ),
                                                        ),
                                                    ),
                                                    [RANGE]
                                                    _id: 38,
                                                    _parent: Some(
                                                        36,
                                                    ),
                                                },
                                            ),
                                            StringEnd(
                                                StringEnd {
                                                    [RANGE]
                                                    _id: 40,
                                                    _parent: Some(
                                                        36,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 36,
                                        _parent: Some(
                                            35,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 35,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                String(
                                    String {
                                        children: [
                                            StringStart(
                                                StringStart {
                                                    [RANGE]
                                                    _id: 43,
                                                    _parent: Some(
                                                        42,
                                                    ),
                                                },
                                            ),
                                            Interpolation(
                                                Interpolation {
                                                    format_specifier: Some(
                                                        FormatSpecifier {
                                                            children: [],
                                                            [RANGE]
                                                            _id: 46,
                                                            _parent: Some(
                                                                44,
                                                            ),
                                                        },
                                                    ),
                                                    type_conversion: None,
                                                    expression: Expression(
                                                        PrimaryExpression(
                                                            Identifier(
                                                                Identifier {
                                                                    [RANGE]
                                                                    _id: 45,
                                                                    _parent: Some(
                                                                        44,
                                                                    ),
                                                                },
                                                            ),
                                                        ),
                                                    ),
                                                    [RANGE]
                                                    _id: 44,
                                                    _parent: Some(
                                                        42,
                                                    ),
                                                },
                                            ),
                                            StringEnd(
                                                StringEnd {
                                                    [RANGE]
                                                    _id: 47,
                                                    _parent: Some(
                                                        42,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 42,
                                        _parent: Some(
                                            41,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 41,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                String(
                                    String {
                                        children: [
                                            StringStart(
                                                StringStart {
                                                    [RANGE]
                                                    _id: 50,
                                                    _parent: Some(
                                                        49,
                                                    ),
                                                },
                                            ),
                                            Interpolation(
                                                Interpolation {
                                                    format_specifier: Some(
                                                        FormatSpecifier {
                                                            children: [
                                                                FormatExpression {
                                                                    expression: Expression(
                                                                        PrimaryExpression(
                                                                            BinaryOperator(
                                                                                BinaryOperator {
                                                                                    operator: Token_Plus(
                                                                                        Token_Plus {
                                                                                            [RANGE]
                                                                                            _id: 57,
                                                                                            _parent: Some(
                                                                                                55,
                                                                                            ),
                                                                                        },
                                                                                    ),
                                                                                    left: Identifier(
                                                                                        Identifier {
                                                                                            [RANGE]
                                                                                            _id: 56,
                                                                                            _parent: Some(
                                                                                                55,
                                                                                            ),
                                                                                        },
                                                                                    ),
                                                                                    right: Identifier(
                                                                                        Identifier {
                                                                                            [RANGE]
                                                                                            _id: 58,
                                                                                            _parent: Some(
                                                                                                55,
                                                                                            ),
                                                                                        },
                                                                                    ),
                                                                                    [RANGE]
                                                                                    _id: 55,
                                                                                    _parent: Some(
                                                                                        54,
                                                                                    ),
                                                                                },
                                                                            ),
                                                                        ),
                                                                    ),
                                                                    format_specifier: None,
                                                                    type_conversion: Some(
                                                                        TypeConversion {
                                                                            [RANGE]
                                                                            _id: 59,
                                                                            _parent: Some(
                                                                                54,
                                                                            ),
                                                                        },
                                                                    ),
                                                                    [RANGE]
                                                                    _id: 54,
                                                                    _parent: Some(
                                                                        53,
                                                                    ),
                                                                },
                                                                FormatExpression {
                                                                    expression: Expression(
                                                                        PrimaryExpression(
                                                                            Identifier(
                                                                                Identifier {
                                                                                    [RANGE]
                                                                                    _id: 61,
                                                                                    _parent: Some(
                                                                                        60,
                                                                                    ),
                                                                                },
                                                                            ),
                                                                        ),
                                                                    ),
                                                                    format_specifier: None,
                                                                    type_conversion: None,
                                                                    [RANGE]
                                                                    _id: 60,
                                                                    _parent: Some(
                                                                        53,
                                                                    ),
                                                                },
                                                            ],
                                                            [RANGE]
                                                            _id: 53,
                                                            _parent: Some(
                                                                51,
                                                            ),
                                                        },
                                                    ),
                                                    type_conversion: None,
                                                    expression: Expression(
                                                        PrimaryExpression(
                                                            Identifier(
                                                                Identifier {
                                                                    [RANGE]
                                                                    _id: 52,
                                                                    _parent: Some(
                                                                        51,
                                                                    ),
                                                                },
                                                            ),
                                                        ),
                                                    ),
                                                    [RANGE]
                                                    _id: 51,
                                                    _parent: Some(
                                                        49,
                                                    ),
                                                },
                                            ),
                                            StringEnd(
                                                StringEnd {
                                                    [RANGE]
                                                    _id: 62,
                                                    _parent: Some(
                                                        49,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 49,
                                        _parent: Some(
                                            48,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 48,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
