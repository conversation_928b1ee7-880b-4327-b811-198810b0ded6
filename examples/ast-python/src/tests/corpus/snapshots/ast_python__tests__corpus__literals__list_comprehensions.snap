---
source: src/tests/corpus/literals.rs
expression: module
---
Module {
    children: [
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                ListComprehension(
                                    ListComprehension {
                                        body: PrimaryExpression(
                                            BinaryOperator(
                                                BinaryOperator {
                                                    operator: Token_Plus(
                                                        Token_Plus {
                                                            [RANGE]
                                                            _id: 5,
                                                            _parent: Some(
                                                                3,
                                                            ),
                                                        },
                                                    ),
                                                    left: Identifier(
                                                        Identifier {
                                                            [RANGE]
                                                            _id: 4,
                                                            _parent: Some(
                                                                3,
                                                            ),
                                                        },
                                                    ),
                                                    right: Identifier(
                                                        Identifier {
                                                            [RANGE]
                                                            _id: 6,
                                                            _parent: Some(
                                                                3,
                                                            ),
                                                        },
                                                    ),
                                                    [RANGE]
                                                    _id: 3,
                                                    _parent: Some(
                                                        2,
                                                    ),
                                                },
                                            ),
                                        ),
                                        children: [
                                            ForInClause(
                                                ForInClause {
                                                    right: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 11,
                                                                        _parent: Some(
                                                                            7,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    left: Pattern(
                                                        TuplePattern(
                                                            TuplePattern {
                                                                children: [
                                                                    Pattern(
                                                                        Identifier(
                                                                            Identifier {
                                                                                [RANGE]
                                                                                _id: 9,
                                                                                _parent: Some(
                                                                                    8,
                                                                                ),
                                                                            },
                                                                        ),
                                                                    ),
                                                                    Pattern(
                                                                        Identifier(
                                                                            Identifier {
                                                                                [RANGE]
                                                                                _id: 10,
                                                                                _parent: Some(
                                                                                    8,
                                                                                ),
                                                                            },
                                                                        ),
                                                                    ),
                                                                ],
                                                                [RANGE]
                                                                _id: 8,
                                                                _parent: Some(
                                                                    7,
                                                                ),
                                                            },
                                                        ),
                                                    ),
                                                    [RANGE]
                                                    _id: 7,
                                                    _parent: Some(
                                                        2,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 2,
                                        _parent: Some(
                                            1,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                ListComprehension(
                                    ListComprehension {
                                        body: PrimaryExpression(
                                            Identifier(
                                                Identifier {
                                                    [RANGE]
                                                    _id: 14,
                                                    _parent: Some(
                                                        13,
                                                    ),
                                                },
                                            ),
                                        ),
                                        children: [
                                            ForInClause(
                                                ForInClause {
                                                    right: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 17,
                                                                        _parent: Some(
                                                                            15,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    left: Pattern(
                                                        Identifier(
                                                            Identifier {
                                                                [RANGE]
                                                                _id: 16,
                                                                _parent: Some(
                                                                    15,
                                                                ),
                                                            },
                                                        ),
                                                    ),
                                                    [RANGE]
                                                    _id: 15,
                                                    _parent: Some(
                                                        13,
                                                    ),
                                                },
                                            ),
                                            ForInClause(
                                                ForInClause {
                                                    right: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 20,
                                                                        _parent: Some(
                                                                            18,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    left: Pattern(
                                                        Identifier(
                                                            Identifier {
                                                                [RANGE]
                                                                _id: 19,
                                                                _parent: Some(
                                                                    18,
                                                                ),
                                                            },
                                                        ),
                                                    ),
                                                    [RANGE]
                                                    _id: 18,
                                                    _parent: Some(
                                                        13,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 13,
                                        _parent: Some(
                                            12,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 12,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                ListComprehension(
                                    ListComprehension {
                                        body: PrimaryExpression(
                                            Tuple(
                                                Tuple {
                                                    children: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 24,
                                                                        _parent: Some(
                                                                            23,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                        Expression(
                                                            PrimaryExpression(
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 25,
                                                                        _parent: Some(
                                                                            23,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 23,
                                                    _parent: Some(
                                                        22,
                                                    ),
                                                },
                                            ),
                                        ),
                                        children: [
                                            ForInClause(
                                                ForInClause {
                                                    right: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                List(
                                                                    List {
                                                                        children: [
                                                                            Expression(
                                                                                PrimaryExpression(
                                                                                    Integer(
                                                                                        Integer {
                                                                                            [RANGE]
                                                                                            _id: 29,
                                                                                            _parent: Some(
                                                                                                28,
                                                                                            ),
                                                                                        },
                                                                                    ),
                                                                                ),
                                                                            ),
                                                                            Expression(
                                                                                PrimaryExpression(
                                                                                    Integer(
                                                                                        Integer {
                                                                                            [RANGE]
                                                                                            _id: 30,
                                                                                            _parent: Some(
                                                                                                28,
                                                                                            ),
                                                                                        },
                                                                                    ),
                                                                                ),
                                                                            ),
                                                                            Expression(
                                                                                PrimaryExpression(
                                                                                    Integer(
                                                                                        Integer {
                                                                                            [RANGE]
                                                                                            _id: 31,
                                                                                            _parent: Some(
                                                                                                28,
                                                                                            ),
                                                                                        },
                                                                                    ),
                                                                                ),
                                                                            ),
                                                                        ],
                                                                        [RANGE]
                                                                        _id: 28,
                                                                        _parent: Some(
                                                                            26,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    left: Pattern(
                                                        Identifier(
                                                            Identifier {
                                                                [RANGE]
                                                                _id: 27,
                                                                _parent: Some(
                                                                    26,
                                                                ),
                                                            },
                                                        ),
                                                    ),
                                                    [RANGE]
                                                    _id: 26,
                                                    _parent: Some(
                                                        22,
                                                    ),
                                                },
                                            ),
                                            ForInClause(
                                                ForInClause {
                                                    right: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                List(
                                                                    List {
                                                                        children: [
                                                                            Expression(
                                                                                PrimaryExpression(
                                                                                    Integer(
                                                                                        Integer {
                                                                                            [RANGE]
                                                                                            _id: 35,
                                                                                            _parent: Some(
                                                                                                34,
                                                                                            ),
                                                                                        },
                                                                                    ),
                                                                                ),
                                                                            ),
                                                                            Expression(
                                                                                PrimaryExpression(
                                                                                    Integer(
                                                                                        Integer {
                                                                                            [RANGE]
                                                                                            _id: 36,
                                                                                            _parent: Some(
                                                                                                34,
                                                                                            ),
                                                                                        },
                                                                                    ),
                                                                                ),
                                                                            ),
                                                                            Expression(
                                                                                PrimaryExpression(
                                                                                    Integer(
                                                                                        Integer {
                                                                                            [RANGE]
                                                                                            _id: 37,
                                                                                            _parent: Some(
                                                                                                34,
                                                                                            ),
                                                                                        },
                                                                                    ),
                                                                                ),
                                                                            ),
                                                                        ],
                                                                        [RANGE]
                                                                        _id: 34,
                                                                        _parent: Some(
                                                                            32,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    left: Pattern(
                                                        Identifier(
                                                            Identifier {
                                                                [RANGE]
                                                                _id: 33,
                                                                _parent: Some(
                                                                    32,
                                                                ),
                                                            },
                                                        ),
                                                    ),
                                                    [RANGE]
                                                    _id: 32,
                                                    _parent: Some(
                                                        22,
                                                    ),
                                                },
                                            ),
                                            IfClause(
                                                IfClause {
                                                    children: PrimaryExpression(
                                                        True(
                                                            True {
                                                                [RANGE]
                                                                _id: 39,
                                                                _parent: Some(
                                                                    38,
                                                                ),
                                                            },
                                                        ),
                                                    ),
                                                    [RANGE]
                                                    _id: 38,
                                                    _parent: Some(
                                                        22,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 22,
                                        _parent: Some(
                                            21,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 21,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                ListComprehension(
                                    ListComprehension {
                                        body: PrimaryExpression(
                                            Identifier(
                                                Identifier {
                                                    [RANGE]
                                                    _id: 42,
                                                    _parent: Some(
                                                        41,
                                                    ),
                                                },
                                            ),
                                        ),
                                        children: [
                                            ForInClause(
                                                ForInClause {
                                                    right: [
                                                        Expression(
                                                            Lambda(
                                                                Lambda {
                                                                    body: PrimaryExpression(
                                                                        True(
                                                                            True {
                                                                                [RANGE]
                                                                                _id: 46,
                                                                                _parent: Some(
                                                                                    45,
                                                                                ),
                                                                            },
                                                                        ),
                                                                    ),
                                                                    parameters: None,
                                                                    [RANGE]
                                                                    _id: 45,
                                                                    _parent: Some(
                                                                        43,
                                                                    ),
                                                                },
                                                            ),
                                                        ),
                                                        Token_Comma(
                                                            Token_Comma {
                                                                [RANGE]
                                                                _id: 47,
                                                                _parent: Some(
                                                                    43,
                                                                ),
                                                            },
                                                        ),
                                                        Expression(
                                                            Lambda(
                                                                Lambda {
                                                                    body: PrimaryExpression(
                                                                        False(
                                                                            False {
                                                                                [RANGE]
                                                                                _id: 49,
                                                                                _parent: Some(
                                                                                    48,
                                                                                ),
                                                                            },
                                                                        ),
                                                                    ),
                                                                    parameters: None,
                                                                    [RANGE]
                                                                    _id: 48,
                                                                    _parent: Some(
                                                                        43,
                                                                    ),
                                                                },
                                                            ),
                                                        ),
                                                    ],
                                                    left: Pattern(
                                                        Identifier(
                                                            Identifier {
                                                                [RANGE]
                                                                _id: 44,
                                                                _parent: Some(
                                                                    43,
                                                                ),
                                                            },
                                                        ),
                                                    ),
                                                    [RANGE]
                                                    _id: 43,
                                                    _parent: Some(
                                                        41,
                                                    ),
                                                },
                                            ),
                                            IfClause(
                                                IfClause {
                                                    children: PrimaryExpression(
                                                        Call(
                                                            Call {
                                                                function: Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 52,
                                                                        _parent: Some(
                                                                            51,
                                                                        ),
                                                                    },
                                                                ),
                                                                arguments: ArgumentList(
                                                                    ArgumentList {
                                                                        children: [],
                                                                        [RANGE]
                                                                        _id: 53,
                                                                        _parent: Some(
                                                                            51,
                                                                        ),
                                                                    },
                                                                ),
                                                                [RANGE]
                                                                _id: 51,
                                                                _parent: Some(
                                                                    50,
                                                                ),
                                                            },
                                                        ),
                                                    ),
                                                    [RANGE]
                                                    _id: 50,
                                                    _parent: Some(
                                                        41,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 41,
                                        _parent: Some(
                                            40,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 40,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
