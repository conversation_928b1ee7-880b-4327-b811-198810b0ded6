---
source: src/tests/corpus/literals.rs
expression: module
---
Module {
    children: [
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                String(
                                    String {
                                        children: [
                                            StringStart(
                                                StringStart {
                                                    [RANGE]
                                                    _id: 3,
                                                    _parent: Some(
                                                        2,
                                                    ),
                                                },
                                            ),
                                            StringContent(
                                                StringContent {
                                                    children: [
                                                        EscapeSequence(
                                                            EscapeSequence {
                                                                [RANGE]
                                                                _id: 5,
                                                                _parent: Some(
                                                                    4,
                                                                ),
                                                            },
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 4,
                                                    _parent: Some(
                                                        2,
                                                    ),
                                                },
                                            ),
                                            StringEnd(
                                                StringEnd {
                                                    [RANGE]
                                                    _id: 6,
                                                    _parent: Some(
                                                        2,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 2,
                                        _parent: Some(
                                            1,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                String(
                                    String {
                                        children: [
                                            StringStart(
                                                StringStart {
                                                    [RANGE]
                                                    _id: 9,
                                                    _parent: Some(
                                                        8,
                                                    ),
                                                },
                                            ),
                                            StringContent(
                                                StringContent {
                                                    children: [
                                                        EscapeSequence(
                                                            EscapeSequence {
                                                                [RANGE]
                                                                _id: 11,
                                                                _parent: Some(
                                                                    10,
                                                                ),
                                                            },
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 10,
                                                    _parent: Some(
                                                        8,
                                                    ),
                                                },
                                            ),
                                            StringEnd(
                                                StringEnd {
                                                    [RANGE]
                                                    _id: 12,
                                                    _parent: Some(
                                                        8,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 8,
                                        _parent: Some(
                                            7,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 7,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                String(
                                    String {
                                        children: [
                                            StringStart(
                                                StringStart {
                                                    [RANGE]
                                                    _id: 15,
                                                    _parent: Some(
                                                        14,
                                                    ),
                                                },
                                            ),
                                            StringContent(
                                                StringContent {
                                                    children: [],
                                                    [RANGE]
                                                    _id: 16,
                                                    _parent: Some(
                                                        14,
                                                    ),
                                                },
                                            ),
                                            StringEnd(
                                                StringEnd {
                                                    [RANGE]
                                                    _id: 17,
                                                    _parent: Some(
                                                        14,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 14,
                                        _parent: Some(
                                            13,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 13,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                String(
                                    String {
                                        children: [
                                            StringStart(
                                                StringStart {
                                                    [RANGE]
                                                    _id: 20,
                                                    _parent: Some(
                                                        19,
                                                    ),
                                                },
                                            ),
                                            StringContent(
                                                StringContent {
                                                    children: [],
                                                    [RANGE]
                                                    _id: 21,
                                                    _parent: Some(
                                                        19,
                                                    ),
                                                },
                                            ),
                                            StringEnd(
                                                StringEnd {
                                                    [RANGE]
                                                    _id: 22,
                                                    _parent: Some(
                                                        19,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 19,
                                        _parent: Some(
                                            18,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 18,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                String(
                                    String {
                                        children: [
                                            StringStart(
                                                StringStart {
                                                    [RANGE]
                                                    _id: 25,
                                                    _parent: Some(
                                                        24,
                                                    ),
                                                },
                                            ),
                                            StringContent(
                                                StringContent {
                                                    children: [],
                                                    [RANGE]
                                                    _id: 26,
                                                    _parent: Some(
                                                        24,
                                                    ),
                                                },
                                            ),
                                            Interpolation(
                                                Interpolation {
                                                    format_specifier: None,
                                                    type_conversion: None,
                                                    expression: Expression(
                                                        PrimaryExpression(
                                                            Integer(
                                                                Integer {
                                                                    [RANGE]
                                                                    _id: 28,
                                                                    _parent: Some(
                                                                        27,
                                                                    ),
                                                                },
                                                            ),
                                                        ),
                                                    ),
                                                    [RANGE]
                                                    _id: 27,
                                                    _parent: Some(
                                                        24,
                                                    ),
                                                },
                                            ),
                                            StringEnd(
                                                StringEnd {
                                                    [RANGE]
                                                    _id: 29,
                                                    _parent: Some(
                                                        24,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 24,
                                        _parent: Some(
                                            23,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 23,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                String(
                                    String {
                                        children: [
                                            StringStart(
                                                StringStart {
                                                    [RANGE]
                                                    _id: 32,
                                                    _parent: Some(
                                                        31,
                                                    ),
                                                },
                                            ),
                                            StringEnd(
                                                StringEnd {
                                                    [RANGE]
                                                    _id: 33,
                                                    _parent: Some(
                                                        31,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 31,
                                        _parent: Some(
                                            30,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 30,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                String(
                                    String {
                                        children: [
                                            StringStart(
                                                StringStart {
                                                    [RANGE]
                                                    _id: 36,
                                                    _parent: Some(
                                                        35,
                                                    ),
                                                },
                                            ),
                                            StringContent(
                                                StringContent {
                                                    children: [],
                                                    [RANGE]
                                                    _id: 37,
                                                    _parent: Some(
                                                        35,
                                                    ),
                                                },
                                            ),
                                            StringEnd(
                                                StringEnd {
                                                    [RANGE]
                                                    _id: 38,
                                                    _parent: Some(
                                                        35,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 35,
                                        _parent: Some(
                                            34,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 34,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
