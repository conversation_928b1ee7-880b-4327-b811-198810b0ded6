---
source: src/tests/corpus/literals.rs
expression: module
---
Module {
    children: [
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                Call(
                                    Call {
                                        function: Attribute(
                                            Attribute {
                                                object: Identifier(
                                                    Identifier {
                                                        [RANGE]
                                                        _id: 4,
                                                        _parent: Some(
                                                            3,
                                                        ),
                                                    },
                                                ),
                                                attribute: Identifier {
                                                    [RANGE]
                                                    _id: 5,
                                                    _parent: Some(
                                                        3,
                                                    ),
                                                },
                                                [RANGE]
                                                _id: 3,
                                                _parent: Some(
                                                    2,
                                                ),
                                            },
                                        ),
                                        arguments: ArgumentList(
                                            ArgumentList {
                                                children: [
                                                    Expression(
                                                        PrimaryExpression(
                                                            ConcatenatedString(
                                                                ConcatenatedString {
                                                                    children: [
                                                                        String {
                                                                            children: [
                                                                                StringStart(
                                                                                    StringStart {
                                                                                        [RANGE]
                                                                                        _id: 9,
                                                                                        _parent: Some(
                                                                                            8,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                                StringContent(
                                                                                    StringContent {
                                                                                        children: [],
                                                                                        [RANGE]
                                                                                        _id: 10,
                                                                                        _parent: Some(
                                                                                            8,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                                StringEnd(
                                                                                    StringEnd {
                                                                                        [RANGE]
                                                                                        _id: 11,
                                                                                        _parent: Some(
                                                                                            8,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                            ],
                                                                            [RANGE]
                                                                            _id: 8,
                                                                            _parent: Some(
                                                                                7,
                                                                            ),
                                                                        },
                                                                        String {
                                                                            children: [
                                                                                StringStart(
                                                                                    StringStart {
                                                                                        [RANGE]
                                                                                        _id: 13,
                                                                                        _parent: Some(
                                                                                            12,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                                StringContent(
                                                                                    StringContent {
                                                                                        children: [],
                                                                                        [RANGE]
                                                                                        _id: 14,
                                                                                        _parent: Some(
                                                                                            12,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                                StringEnd(
                                                                                    StringEnd {
                                                                                        [RANGE]
                                                                                        _id: 15,
                                                                                        _parent: Some(
                                                                                            12,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                            ],
                                                                            [RANGE]
                                                                            _id: 12,
                                                                            _parent: Some(
                                                                                7,
                                                                            ),
                                                                        },
                                                                    ],
                                                                    [RANGE]
                                                                    _id: 7,
                                                                    _parent: Some(
                                                                        6,
                                                                    ),
                                                                },
                                                            ),
                                                        ),
                                                    ),
                                                ],
                                                [RANGE]
                                                _id: 6,
                                                _parent: Some(
                                                    2,
                                                ),
                                            },
                                        ),
                                        [RANGE]
                                        _id: 2,
                                        _parent: Some(
                                            1,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
