---
source: src/tests/corpus/literals.rs
expression: module
---
Module {
    children: [
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                Float(
                                    Float {
                                        [RANGE]
                                        _id: 2,
                                        _parent: Some(
                                            1,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                Float(
                                    Float {
                                        [RANGE]
                                        _id: 4,
                                        _parent: Some(
                                            3,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 3,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                Float(
                                    Float {
                                        [RANGE]
                                        _id: 6,
                                        _parent: Some(
                                            5,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 5,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                Float(
                                    Float {
                                        [RANGE]
                                        _id: 8,
                                        _parent: Some(
                                            7,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 7,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                Float(
                                    Float {
                                        [RANGE]
                                        _id: 10,
                                        _parent: Some(
                                            9,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 9,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                UnaryOperator(
                                    UnaryOperator {
                                        operator: Token_Minus(
                                            Token_Minus {
                                                [RANGE]
                                                _id: 13,
                                                _parent: Some(
                                                    12,
                                                ),
                                            },
                                        ),
                                        argument: Float(
                                            Float {
                                                [RANGE]
                                                _id: 14,
                                                _parent: Some(
                                                    12,
                                                ),
                                            },
                                        ),
                                        [RANGE]
                                        _id: 12,
                                        _parent: Some(
                                            11,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 11,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
