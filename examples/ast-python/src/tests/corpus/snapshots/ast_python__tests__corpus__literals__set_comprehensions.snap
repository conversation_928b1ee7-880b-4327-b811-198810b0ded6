---
source: src/tests/corpus/literals.rs
expression: module
---
Module {
    children: [
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                SetComprehension(
                                    SetComprehension {
                                        body: PrimaryExpression(
                                            Subscript(
                                                Subscript {
                                                    subscript: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 7,
                                                                        _parent: Some(
                                                                            3,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    value: Subscript(
                                                        Subscript {
                                                            subscript: [
                                                                Expression(
                                                                    PrimaryExpression(
                                                                        Identifier(
                                                                            Identifier {
                                                                                [RANGE]
                                                                                _id: 6,
                                                                                _parent: Some(
                                                                                    4,
                                                                                ),
                                                                            },
                                                                        ),
                                                                    ),
                                                                ),
                                                            ],
                                                            value: Identifier(
                                                                Identifier {
                                                                    [RANGE]
                                                                    _id: 5,
                                                                    _parent: Some(
                                                                        4,
                                                                    ),
                                                                },
                                                            ),
                                                            [RANGE]
                                                            _id: 4,
                                                            _parent: Some(
                                                                3,
                                                            ),
                                                        },
                                                    ),
                                                    [RANGE]
                                                    _id: 3,
                                                    _parent: Some(
                                                        2,
                                                    ),
                                                },
                                            ),
                                        ),
                                        children: [
                                            ForInClause(
                                                ForInClause {
                                                    right: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 13,
                                                                        _parent: Some(
                                                                            8,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    left: PatternList(
                                                        PatternList {
                                                            children: [
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 10,
                                                                        _parent: Some(
                                                                            9,
                                                                        ),
                                                                    },
                                                                ),
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 11,
                                                                        _parent: Some(
                                                                            9,
                                                                        ),
                                                                    },
                                                                ),
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 12,
                                                                        _parent: Some(
                                                                            9,
                                                                        ),
                                                                    },
                                                                ),
                                                            ],
                                                            [RANGE]
                                                            _id: 9,
                                                            _parent: Some(
                                                                8,
                                                            ),
                                                        },
                                                    ),
                                                    [RANGE]
                                                    _id: 8,
                                                    _parent: Some(
                                                        2,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 2,
                                        _parent: Some(
                                            1,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                SetComprehension(
                                    SetComprehension {
                                        body: PrimaryExpression(
                                            Identifier(
                                                Identifier {
                                                    [RANGE]
                                                    _id: 16,
                                                    _parent: Some(
                                                        15,
                                                    ),
                                                },
                                            ),
                                        ),
                                        children: [
                                            ForInClause(
                                                ForInClause {
                                                    right: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 19,
                                                                        _parent: Some(
                                                                            17,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    left: Pattern(
                                                        Identifier(
                                                            Identifier {
                                                                [RANGE]
                                                                _id: 18,
                                                                _parent: Some(
                                                                    17,
                                                                ),
                                                            },
                                                        ),
                                                    ),
                                                    [RANGE]
                                                    _id: 17,
                                                    _parent: Some(
                                                        15,
                                                    ),
                                                },
                                            ),
                                            ForInClause(
                                                ForInClause {
                                                    right: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 22,
                                                                        _parent: Some(
                                                                            20,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    left: Pattern(
                                                        Identifier(
                                                            Identifier {
                                                                [RANGE]
                                                                _id: 21,
                                                                _parent: Some(
                                                                    20,
                                                                ),
                                                            },
                                                        ),
                                                    ),
                                                    [RANGE]
                                                    _id: 20,
                                                    _parent: Some(
                                                        15,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 15,
                                        _parent: Some(
                                            14,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 14,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
