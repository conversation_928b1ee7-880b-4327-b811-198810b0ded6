---
source: src/tests/corpus/literals.rs
expression: module
---
Module {
    children: [
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                Set(
                                    Set {
                                        children: [
                                            Expression(
                                                PrimaryExpression(
                                                    Identifier(
                                                        Identifier {
                                                            [RANGE]
                                                            _id: 3,
                                                            _parent: Some(
                                                                2,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ),
                                            Expression(
                                                PrimaryExpression(
                                                    Identifier(
                                                        Identifier {
                                                            [RANGE]
                                                            _id: 4,
                                                            _parent: Some(
                                                                2,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ),
                                            Expression(
                                                PrimaryExpression(
                                                    Identifier(
                                                        Identifier {
                                                            [RANGE]
                                                            _id: 5,
                                                            _parent: Some(
                                                                2,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 2,
                                        _parent: Some(
                                            1,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                Set(
                                    Set {
                                        children: [
                                            ListSplat(
                                                ListSplat {
                                                    children: Expression(
                                                        PrimaryExpression(
                                                            Dictionary(
                                                                Dictionary {
                                                                    children: [],
                                                                    [RANGE]
                                                                    _id: 9,
                                                                    _parent: Some(
                                                                        8,
                                                                    ),
                                                                },
                                                            ),
                                                        ),
                                                    ),
                                                    [RANGE]
                                                    _id: 8,
                                                    _parent: Some(
                                                        7,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 7,
                                        _parent: Some(
                                            6,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 6,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
