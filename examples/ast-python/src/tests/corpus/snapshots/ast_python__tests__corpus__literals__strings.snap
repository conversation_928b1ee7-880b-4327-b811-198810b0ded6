---
source: src/tests/corpus/literals.rs
expression: module
---
Module {
    children: [
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                String(
                                    String {
                                        children: [
                                            StringStart(
                                                StringStart {
                                                    [RANGE]
                                                    _id: 3,
                                                    _parent: Some(
                                                        2,
                                                    ),
                                                },
                                            ),
                                            StringContent(
                                                StringContent {
                                                    children: [],
                                                    [RANGE]
                                                    _id: 4,
                                                    _parent: Some(
                                                        2,
                                                    ),
                                                },
                                            ),
                                            StringEnd(
                                                StringEnd {
                                                    [RANGE]
                                                    _id: 5,
                                                    _parent: Some(
                                                        2,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 2,
                                        _parent: Some(
                                            1,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                String(
                                    String {
                                        children: [
                                            StringStart(
                                                StringStart {
                                                    [RANGE]
                                                    _id: 8,
                                                    _parent: Some(
                                                        7,
                                                    ),
                                                },
                                            ),
                                            StringContent(
                                                StringContent {
                                                    children: [],
                                                    [RANGE]
                                                    _id: 9,
                                                    _parent: Some(
                                                        7,
                                                    ),
                                                },
                                            ),
                                            StringEnd(
                                                StringEnd {
                                                    [RANGE]
                                                    _id: 10,
                                                    _parent: Some(
                                                        7,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 7,
                                        _parent: Some(
                                            6,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 6,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                String(
                                    String {
                                        children: [
                                            StringStart(
                                                StringStart {
                                                    [RANGE]
                                                    _id: 13,
                                                    _parent: Some(
                                                        12,
                                                    ),
                                                },
                                            ),
                                            StringContent(
                                                StringContent {
                                                    children: [],
                                                    [RANGE]
                                                    _id: 14,
                                                    _parent: Some(
                                                        12,
                                                    ),
                                                },
                                            ),
                                            StringEnd(
                                                StringEnd {
                                                    [RANGE]
                                                    _id: 15,
                                                    _parent: Some(
                                                        12,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 12,
                                        _parent: Some(
                                            11,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 11,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                String(
                                    String {
                                        children: [
                                            StringStart(
                                                StringStart {
                                                    [RANGE]
                                                    _id: 18,
                                                    _parent: Some(
                                                        17,
                                                    ),
                                                },
                                            ),
                                            StringContent(
                                                StringContent {
                                                    children: [],
                                                    [RANGE]
                                                    _id: 19,
                                                    _parent: Some(
                                                        17,
                                                    ),
                                                },
                                            ),
                                            StringEnd(
                                                StringEnd {
                                                    [RANGE]
                                                    _id: 20,
                                                    _parent: Some(
                                                        17,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 17,
                                        _parent: Some(
                                            16,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 16,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                String(
                                    String {
                                        children: [
                                            StringStart(
                                                StringStart {
                                                    [RANGE]
                                                    _id: 23,
                                                    _parent: Some(
                                                        22,
                                                    ),
                                                },
                                            ),
                                            StringContent(
                                                StringContent {
                                                    children: [],
                                                    [RANGE]
                                                    _id: 24,
                                                    _parent: Some(
                                                        22,
                                                    ),
                                                },
                                            ),
                                            StringEnd(
                                                StringEnd {
                                                    [RANGE]
                                                    _id: 25,
                                                    _parent: Some(
                                                        22,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 22,
                                        _parent: Some(
                                            21,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 21,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                String(
                                    String {
                                        children: [
                                            StringStart(
                                                StringStart {
                                                    [RANGE]
                                                    _id: 28,
                                                    _parent: Some(
                                                        27,
                                                    ),
                                                },
                                            ),
                                            StringContent(
                                                StringContent {
                                                    children: [],
                                                    [RANGE]
                                                    _id: 29,
                                                    _parent: Some(
                                                        27,
                                                    ),
                                                },
                                            ),
                                            StringEnd(
                                                StringEnd {
                                                    [RANGE]
                                                    _id: 30,
                                                    _parent: Some(
                                                        27,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 27,
                                        _parent: Some(
                                            26,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 26,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                String(
                                    String {
                                        children: [
                                            StringStart(
                                                StringStart {
                                                    [RANGE]
                                                    _id: 33,
                                                    _parent: Some(
                                                        32,
                                                    ),
                                                },
                                            ),
                                            StringContent(
                                                StringContent {
                                                    children: [
                                                        EscapeSequence(
                                                            EscapeSequence {
                                                                [RANGE]
                                                                _id: 35,
                                                                _parent: Some(
                                                                    34,
                                                                ),
                                                            },
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 34,
                                                    _parent: Some(
                                                        32,
                                                    ),
                                                },
                                            ),
                                            StringEnd(
                                                StringEnd {
                                                    [RANGE]
                                                    _id: 36,
                                                    _parent: Some(
                                                        32,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 32,
                                        _parent: Some(
                                            31,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 31,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                String(
                                    String {
                                        children: [
                                            StringStart(
                                                StringStart {
                                                    [RANGE]
                                                    _id: 39,
                                                    _parent: Some(
                                                        38,
                                                    ),
                                                },
                                            ),
                                            StringContent(
                                                StringContent {
                                                    children: [],
                                                    [RANGE]
                                                    _id: 40,
                                                    _parent: Some(
                                                        38,
                                                    ),
                                                },
                                            ),
                                            StringEnd(
                                                StringEnd {
                                                    [RANGE]
                                                    _id: 41,
                                                    _parent: Some(
                                                        38,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 38,
                                        _parent: Some(
                                            37,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 37,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                String(
                                    String {
                                        children: [
                                            StringStart(
                                                StringStart {
                                                    [RANGE]
                                                    _id: 44,
                                                    _parent: Some(
                                                        43,
                                                    ),
                                                },
                                            ),
                                            StringContent(
                                                StringContent {
                                                    children: [
                                                        EscapeSequence(
                                                            EscapeSequence {
                                                                [RANGE]
                                                                _id: 46,
                                                                _parent: Some(
                                                                    45,
                                                                ),
                                                            },
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 45,
                                                    _parent: Some(
                                                        43,
                                                    ),
                                                },
                                            ),
                                            StringEnd(
                                                StringEnd {
                                                    [RANGE]
                                                    _id: 47,
                                                    _parent: Some(
                                                        43,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 43,
                                        _parent: Some(
                                            42,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 42,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                String(
                                    String {
                                        children: [
                                            StringStart(
                                                StringStart {
                                                    [RANGE]
                                                    _id: 50,
                                                    _parent: Some(
                                                        49,
                                                    ),
                                                },
                                            ),
                                            StringContent(
                                                StringContent {
                                                    children: [
                                                        EscapeSequence(
                                                            EscapeSequence {
                                                                [RANGE]
                                                                _id: 52,
                                                                _parent: Some(
                                                                    51,
                                                                ),
                                                            },
                                                        ),
                                                        EscapeSequence(
                                                            EscapeSequence {
                                                                [RANGE]
                                                                _id: 53,
                                                                _parent: Some(
                                                                    51,
                                                                ),
                                                            },
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 51,
                                                    _parent: Some(
                                                        49,
                                                    ),
                                                },
                                            ),
                                            StringEnd(
                                                StringEnd {
                                                    [RANGE]
                                                    _id: 54,
                                                    _parent: Some(
                                                        49,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 49,
                                        _parent: Some(
                                            48,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 48,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                String(
                                    String {
                                        children: [
                                            StringStart(
                                                StringStart {
                                                    [RANGE]
                                                    _id: 57,
                                                    _parent: Some(
                                                        56,
                                                    ),
                                                },
                                            ),
                                            StringContent(
                                                StringContent {
                                                    children: [
                                                        EscapeSequence(
                                                            EscapeSequence {
                                                                [RANGE]
                                                                _id: 59,
                                                                _parent: Some(
                                                                    58,
                                                                ),
                                                            },
                                                        ),
                                                        EscapeSequence(
                                                            EscapeSequence {
                                                                [RANGE]
                                                                _id: 60,
                                                                _parent: Some(
                                                                    58,
                                                                ),
                                                            },
                                                        ),
                                                        EscapeSequence(
                                                            EscapeSequence {
                                                                [RANGE]
                                                                _id: 61,
                                                                _parent: Some(
                                                                    58,
                                                                ),
                                                            },
                                                        ),
                                                        EscapeSequence(
                                                            EscapeSequence {
                                                                [RANGE]
                                                                _id: 62,
                                                                _parent: Some(
                                                                    58,
                                                                ),
                                                            },
                                                        ),
                                                        EscapeSequence(
                                                            EscapeSequence {
                                                                [RANGE]
                                                                _id: 63,
                                                                _parent: Some(
                                                                    58,
                                                                ),
                                                            },
                                                        ),
                                                        EscapeSequence(
                                                            EscapeSequence {
                                                                [RANGE]
                                                                _id: 64,
                                                                _parent: Some(
                                                                    58,
                                                                ),
                                                            },
                                                        ),
                                                        EscapeSequence(
                                                            EscapeSequence {
                                                                [RANGE]
                                                                _id: 65,
                                                                _parent: Some(
                                                                    58,
                                                                ),
                                                            },
                                                        ),
                                                        EscapeSequence(
                                                            EscapeSequence {
                                                                [RANGE]
                                                                _id: 66,
                                                                _parent: Some(
                                                                    58,
                                                                ),
                                                            },
                                                        ),
                                                        EscapeSequence(
                                                            EscapeSequence {
                                                                [RANGE]
                                                                _id: 67,
                                                                _parent: Some(
                                                                    58,
                                                                ),
                                                            },
                                                        ),
                                                        EscapeSequence(
                                                            EscapeSequence {
                                                                [RANGE]
                                                                _id: 68,
                                                                _parent: Some(
                                                                    58,
                                                                ),
                                                            },
                                                        ),
                                                        EscapeSequence(
                                                            EscapeSequence {
                                                                [RANGE]
                                                                _id: 69,
                                                                _parent: Some(
                                                                    58,
                                                                ),
                                                            },
                                                        ),
                                                        EscapeSequence(
                                                            EscapeSequence {
                                                                [RANGE]
                                                                _id: 70,
                                                                _parent: Some(
                                                                    58,
                                                                ),
                                                            },
                                                        ),
                                                        EscapeSequence(
                                                            EscapeSequence {
                                                                [RANGE]
                                                                _id: 71,
                                                                _parent: Some(
                                                                    58,
                                                                ),
                                                            },
                                                        ),
                                                        EscapeSequence(
                                                            EscapeSequence {
                                                                [RANGE]
                                                                _id: 72,
                                                                _parent: Some(
                                                                    58,
                                                                ),
                                                            },
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 58,
                                                    _parent: Some(
                                                        56,
                                                    ),
                                                },
                                            ),
                                            StringEnd(
                                                StringEnd {
                                                    [RANGE]
                                                    _id: 73,
                                                    _parent: Some(
                                                        56,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 56,
                                        _parent: Some(
                                            55,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 55,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                String(
                                    String {
                                        children: [
                                            StringStart(
                                                StringStart {
                                                    [RANGE]
                                                    _id: 76,
                                                    _parent: Some(
                                                        75,
                                                    ),
                                                },
                                            ),
                                            StringContent(
                                                StringContent {
                                                    children: [],
                                                    [RANGE]
                                                    _id: 77,
                                                    _parent: Some(
                                                        75,
                                                    ),
                                                },
                                            ),
                                            StringEnd(
                                                StringEnd {
                                                    [RANGE]
                                                    _id: 78,
                                                    _parent: Some(
                                                        75,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 75,
                                        _parent: Some(
                                            74,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 74,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                String(
                                    String {
                                        children: [
                                            StringStart(
                                                StringStart {
                                                    [RANGE]
                                                    _id: 81,
                                                    _parent: Some(
                                                        80,
                                                    ),
                                                },
                                            ),
                                            StringContent(
                                                StringContent {
                                                    children: [
                                                        EscapeSequence(
                                                            EscapeSequence {
                                                                [RANGE]
                                                                _id: 83,
                                                                _parent: Some(
                                                                    82,
                                                                ),
                                                            },
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 82,
                                                    _parent: Some(
                                                        80,
                                                    ),
                                                },
                                            ),
                                            StringEnd(
                                                StringEnd {
                                                    [RANGE]
                                                    _id: 84,
                                                    _parent: Some(
                                                        80,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 80,
                                        _parent: Some(
                                            79,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 79,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
