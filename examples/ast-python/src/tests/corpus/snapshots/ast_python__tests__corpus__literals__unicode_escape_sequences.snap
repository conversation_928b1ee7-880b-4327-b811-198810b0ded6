---
source: src/tests/corpus/literals.rs
expression: module
---
Module {
    children: [
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                String(
                                    String {
                                        children: [
                                            StringStart(
                                                StringStart {
                                                    [RANGE]
                                                    _id: 3,
                                                    _parent: Some(
                                                        2,
                                                    ),
                                                },
                                            ),
                                            StringContent(
                                                StringContent {
                                                    children: [
                                                        EscapeSequence(
                                                            EscapeSequence {
                                                                [RANGE]
                                                                _id: 5,
                                                                _parent: Some(
                                                                    4,
                                                                ),
                                                            },
                                                        ),
                                                        EscapeSequence(
                                                            EscapeSequence {
                                                                [RANGE]
                                                                _id: 6,
                                                                _parent: Some(
                                                                    4,
                                                                ),
                                                            },
                                                        ),
                                                        EscapeSequence(
                                                            EscapeSequence {
                                                                [RANGE]
                                                                _id: 7,
                                                                _parent: Some(
                                                                    4,
                                                                ),
                                                            },
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 4,
                                                    _parent: Some(
                                                        2,
                                                    ),
                                                },
                                            ),
                                            StringEnd(
                                                StringEnd {
                                                    [RANGE]
                                                    _id: 8,
                                                    _parent: Some(
                                                        2,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 2,
                                        _parent: Some(
                                            1,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
