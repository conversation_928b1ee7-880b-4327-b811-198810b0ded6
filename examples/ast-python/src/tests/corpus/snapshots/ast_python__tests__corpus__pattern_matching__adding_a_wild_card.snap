---
source: src/tests/corpus/pattern_matching.rs
expression: module
---
Module {
    children: [
        CompoundStatement(
            MatchStatement(
                MatchStatement {
                    body: Block {
                        alternative: [
                            CaseClause {
                                consequence: Block {
                                    alternative: [],
                                    children: [
                                        SimpleStatement(
                                            ExpressionStatement(
                                                ExpressionStatement {
                                                    children: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                Ellipsis(
                                                                    Ellipsis {
                                                                        [RANGE]
                                                                        _id: 18,
                                                                        _parent: Some(
                                                                            17,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 17,
                                                    _parent: Some(
                                                        16,
                                                    ),
                                                },
                                            ),
                                        ),
                                    ],
                                    [RANGE]
                                    _id: 16,
                                    _parent: Some(
                                        8,
                                    ),
                                },
                                guard: None,
                                children: [
                                    CasePattern {
                                        children: Some(
                                            ListPattern(
                                                ListPattern {
                                                    children: [
                                                        CasePattern(
                                                            CasePattern {
                                                                children: Some(
                                                                    String(
                                                                        String {
                                                                            children: [
                                                                                StringStart(
                                                                                    StringStart {
                                                                                        [RANGE]
                                                                                        _id: 13,
                                                                                        _parent: Some(
                                                                                            12,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                                StringContent(
                                                                                    StringContent {
                                                                                        children: [],
                                                                                        [RANGE]
                                                                                        _id: 14,
                                                                                        _parent: Some(
                                                                                            12,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                                StringEnd(
                                                                                    StringEnd {
                                                                                        [RANGE]
                                                                                        _id: 15,
                                                                                        _parent: Some(
                                                                                            12,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                            ],
                                                                            [RANGE]
                                                                            _id: 12,
                                                                            _parent: Some(
                                                                                11,
                                                                            ),
                                                                        },
                                                                    ),
                                                                ),
                                                                [RANGE]
                                                                _id: 11,
                                                                _parent: Some(
                                                                    10,
                                                                ),
                                                            },
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 10,
                                                    _parent: Some(
                                                        9,
                                                    ),
                                                },
                                            ),
                                        ),
                                        [RANGE]
                                        _id: 9,
                                        _parent: Some(
                                            8,
                                        ),
                                    },
                                ],
                                [RANGE]
                                _id: 8,
                                _parent: Some(
                                    7,
                                ),
                            },
                            CaseClause {
                                consequence: Block {
                                    alternative: [],
                                    children: [
                                        SimpleStatement(
                                            PassStatement(
                                                PassStatement {
                                                    [RANGE]
                                                    _id: 31,
                                                    _parent: Some(
                                                        30,
                                                    ),
                                                },
                                            ),
                                        ),
                                    ],
                                    [RANGE]
                                    _id: 30,
                                    _parent: Some(
                                        19,
                                    ),
                                },
                                guard: None,
                                children: [
                                    CasePattern {
                                        children: Some(
                                            ListPattern(
                                                ListPattern {
                                                    children: [
                                                        CasePattern(
                                                            CasePattern {
                                                                children: Some(
                                                                    String(
                                                                        String {
                                                                            children: [
                                                                                StringStart(
                                                                                    StringStart {
                                                                                        [RANGE]
                                                                                        _id: 24,
                                                                                        _parent: Some(
                                                                                            23,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                                StringContent(
                                                                                    StringContent {
                                                                                        children: [],
                                                                                        [RANGE]
                                                                                        _id: 25,
                                                                                        _parent: Some(
                                                                                            23,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                                StringEnd(
                                                                                    StringEnd {
                                                                                        [RANGE]
                                                                                        _id: 26,
                                                                                        _parent: Some(
                                                                                            23,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                            ],
                                                                            [RANGE]
                                                                            _id: 23,
                                                                            _parent: Some(
                                                                                22,
                                                                            ),
                                                                        },
                                                                    ),
                                                                ),
                                                                [RANGE]
                                                                _id: 22,
                                                                _parent: Some(
                                                                    21,
                                                                ),
                                                            },
                                                        ),
                                                        CasePattern(
                                                            CasePattern {
                                                                children: Some(
                                                                    DottedName(
                                                                        DottedName {
                                                                            children: [
                                                                                Identifier {
                                                                                    [RANGE]
                                                                                    _id: 29,
                                                                                    _parent: Some(
                                                                                        28,
                                                                                    ),
                                                                                },
                                                                            ],
                                                                            [RANGE]
                                                                            _id: 28,
                                                                            _parent: Some(
                                                                                27,
                                                                            ),
                                                                        },
                                                                    ),
                                                                ),
                                                                [RANGE]
                                                                _id: 27,
                                                                _parent: Some(
                                                                    21,
                                                                ),
                                                            },
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 21,
                                                    _parent: Some(
                                                        20,
                                                    ),
                                                },
                                            ),
                                        ),
                                        [RANGE]
                                        _id: 20,
                                        _parent: Some(
                                            19,
                                        ),
                                    },
                                ],
                                [RANGE]
                                _id: 19,
                                _parent: Some(
                                    7,
                                ),
                            },
                            CaseClause {
                                consequence: Block {
                                    alternative: [],
                                    children: [
                                        SimpleStatement(
                                            PassStatement(
                                                PassStatement {
                                                    [RANGE]
                                                    _id: 44,
                                                    _parent: Some(
                                                        43,
                                                    ),
                                                },
                                            ),
                                        ),
                                    ],
                                    [RANGE]
                                    _id: 43,
                                    _parent: Some(
                                        32,
                                    ),
                                },
                                guard: None,
                                children: [
                                    CasePattern {
                                        children: Some(
                                            ListPattern(
                                                ListPattern {
                                                    children: [
                                                        CasePattern(
                                                            CasePattern {
                                                                children: Some(
                                                                    String(
                                                                        String {
                                                                            children: [
                                                                                StringStart(
                                                                                    StringStart {
                                                                                        [RANGE]
                                                                                        _id: 37,
                                                                                        _parent: Some(
                                                                                            36,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                                StringContent(
                                                                                    StringContent {
                                                                                        children: [],
                                                                                        [RANGE]
                                                                                        _id: 38,
                                                                                        _parent: Some(
                                                                                            36,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                                StringEnd(
                                                                                    StringEnd {
                                                                                        [RANGE]
                                                                                        _id: 39,
                                                                                        _parent: Some(
                                                                                            36,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                            ],
                                                                            [RANGE]
                                                                            _id: 36,
                                                                            _parent: Some(
                                                                                35,
                                                                            ),
                                                                        },
                                                                    ),
                                                                ),
                                                                [RANGE]
                                                                _id: 35,
                                                                _parent: Some(
                                                                    34,
                                                                ),
                                                            },
                                                        ),
                                                        CasePattern(
                                                            CasePattern {
                                                                children: Some(
                                                                    SplatPattern(
                                                                        SplatPattern {
                                                                            children: Some(
                                                                                Identifier {
                                                                                    [RANGE]
                                                                                    _id: 42,
                                                                                    _parent: Some(
                                                                                        41,
                                                                                    ),
                                                                                },
                                                                            ),
                                                                            [RANGE]
                                                                            _id: 41,
                                                                            _parent: Some(
                                                                                40,
                                                                            ),
                                                                        },
                                                                    ),
                                                                ),
                                                                [RANGE]
                                                                _id: 40,
                                                                _parent: Some(
                                                                    34,
                                                                ),
                                                            },
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 34,
                                                    _parent: Some(
                                                        33,
                                                    ),
                                                },
                                            ),
                                        ),
                                        [RANGE]
                                        _id: 33,
                                        _parent: Some(
                                            32,
                                        ),
                                    },
                                ],
                                [RANGE]
                                _id: 32,
                                _parent: Some(
                                    7,
                                ),
                            },
                            CaseClause {
                                consequence: Block {
                                    alternative: [],
                                    children: [
                                        SimpleStatement(
                                            ExpressionStatement(
                                                ExpressionStatement {
                                                    children: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                Call(
                                                                    Call {
                                                                        function: Identifier(
                                                                            Identifier {
                                                                                [RANGE]
                                                                                _id: 50,
                                                                                _parent: Some(
                                                                                    49,
                                                                                ),
                                                                            },
                                                                        ),
                                                                        arguments: ArgumentList(
                                                                            ArgumentList {
                                                                                children: [
                                                                                    Expression(
                                                                                        PrimaryExpression(
                                                                                            String(
                                                                                                String {
                                                                                                    children: [
                                                                                                        StringStart(
                                                                                                            StringStart {
                                                                                                                [RANGE]
                                                                                                                _id: 53,
                                                                                                                _parent: Some(
                                                                                                                    52,
                                                                                                                ),
                                                                                                            },
                                                                                                        ),
                                                                                                        StringContent(
                                                                                                            StringContent {
                                                                                                                children: [],
                                                                                                                [RANGE]
                                                                                                                _id: 54,
                                                                                                                _parent: Some(
                                                                                                                    52,
                                                                                                                ),
                                                                                                            },
                                                                                                        ),
                                                                                                        Interpolation(
                                                                                                            Interpolation {
                                                                                                                format_specifier: None,
                                                                                                                type_conversion: Some(
                                                                                                                    TypeConversion {
                                                                                                                        [RANGE]
                                                                                                                        _id: 57,
                                                                                                                        _parent: Some(
                                                                                                                            55,
                                                                                                                        ),
                                                                                                                    },
                                                                                                                ),
                                                                                                                expression: Expression(
                                                                                                                    PrimaryExpression(
                                                                                                                        Identifier(
                                                                                                                            Identifier {
                                                                                                                                [RANGE]
                                                                                                                                _id: 56,
                                                                                                                                _parent: Some(
                                                                                                                                    55,
                                                                                                                                ),
                                                                                                                            },
                                                                                                                        ),
                                                                                                                    ),
                                                                                                                ),
                                                                                                                [RANGE]
                                                                                                                _id: 55,
                                                                                                                _parent: Some(
                                                                                                                    52,
                                                                                                                ),
                                                                                                            },
                                                                                                        ),
                                                                                                        StringEnd(
                                                                                                            StringEnd {
                                                                                                                [RANGE]
                                                                                                                _id: 58,
                                                                                                                _parent: Some(
                                                                                                                    52,
                                                                                                                ),
                                                                                                            },
                                                                                                        ),
                                                                                                    ],
                                                                                                    [RANGE]
                                                                                                    _id: 52,
                                                                                                    _parent: Some(
                                                                                                        51,
                                                                                                    ),
                                                                                                },
                                                                                            ),
                                                                                        ),
                                                                                    ),
                                                                                ],
                                                                                [RANGE]
                                                                                _id: 51,
                                                                                _parent: Some(
                                                                                    49,
                                                                                ),
                                                                            },
                                                                        ),
                                                                        [RANGE]
                                                                        _id: 49,
                                                                        _parent: Some(
                                                                            48,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 48,
                                                    _parent: Some(
                                                        47,
                                                    ),
                                                },
                                            ),
                                        ),
                                    ],
                                    [RANGE]
                                    _id: 47,
                                    _parent: Some(
                                        45,
                                    ),
                                },
                                guard: None,
                                children: [
                                    CasePattern {
                                        children: None,
                                        [RANGE]
                                        _id: 46,
                                        _parent: Some(
                                            45,
                                        ),
                                    },
                                ],
                                [RANGE]
                                _id: 45,
                                _parent: Some(
                                    7,
                                ),
                            },
                        ],
                        children: [],
                        [RANGE]
                        _id: 7,
                        _parent: Some(
                            1,
                        ),
                    },
                    subject: [
                        PrimaryExpression(
                            Call(
                                Call {
                                    function: Attribute(
                                        Attribute {
                                            object: Identifier(
                                                Identifier {
                                                    [RANGE]
                                                    _id: 4,
                                                    _parent: Some(
                                                        3,
                                                    ),
                                                },
                                            ),
                                            attribute: Identifier {
                                                [RANGE]
                                                _id: 5,
                                                _parent: Some(
                                                    3,
                                                ),
                                            },
                                            [RANGE]
                                            _id: 3,
                                            _parent: Some(
                                                2,
                                            ),
                                        },
                                    ),
                                    arguments: ArgumentList(
                                        ArgumentList {
                                            children: [],
                                            [RANGE]
                                            _id: 6,
                                            _parent: Some(
                                                2,
                                            ),
                                        },
                                    ),
                                    [RANGE]
                                    _id: 2,
                                    _parent: Some(
                                        1,
                                    ),
                                },
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
