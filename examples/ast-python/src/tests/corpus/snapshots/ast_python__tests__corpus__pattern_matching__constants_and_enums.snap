---
source: src/tests/corpus/pattern_matching.rs
expression: module
---
Module {
    children: [
        CompoundStatement(
            MatchStatement(
                MatchStatement {
                    body: Block {
                        alternative: [
                            CaseClause {
                                consequence: Block {
                                    alternative: [],
                                    children: [
                                        SimpleStatement(
                                            ExpressionStatement(
                                                ExpressionStatement {
                                                    children: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                Call(
                                                                    Call {
                                                                        function: Identifier(
                                                                            Identifier {
                                                                                [RANGE]
                                                                                _id: 30,
                                                                                _parent: Some(
                                                                                    29,
                                                                                ),
                                                                            },
                                                                        ),
                                                                        arguments: ArgumentList(
                                                                            ArgumentList {
                                                                                children: [
                                                                                    Expression(
                                                                                        PrimaryExpression(
                                                                                            Identifier(
                                                                                                Identifier {
                                                                                                    [RANGE]
                                                                                                    _id: 32,
                                                                                                    _parent: Some(
                                                                                                        31,
                                                                                                    ),
                                                                                                },
                                                                                            ),
                                                                                        ),
                                                                                    ),
                                                                                    Expression(
                                                                                        PrimaryExpression(
                                                                                            Identifier(
                                                                                                Identifier {
                                                                                                    [RANGE]
                                                                                                    _id: 33,
                                                                                                    _parent: Some(
                                                                                                        31,
                                                                                                    ),
                                                                                                },
                                                                                            ),
                                                                                        ),
                                                                                    ),
                                                                                ],
                                                                                [RANGE]
                                                                                _id: 31,
                                                                                _parent: Some(
                                                                                    29,
                                                                                ),
                                                                            },
                                                                        ),
                                                                        [RANGE]
                                                                        _id: 29,
                                                                        _parent: Some(
                                                                            28,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 28,
                                                    _parent: Some(
                                                        27,
                                                    ),
                                                },
                                            ),
                                        ),
                                    ],
                                    [RANGE]
                                    _id: 27,
                                    _parent: Some(
                                        8,
                                    ),
                                },
                                guard: None,
                                children: [
                                    CasePattern {
                                        children: Some(
                                            ClassPattern(
                                                ClassPattern {
                                                    children: [
                                                        DottedName(
                                                            DottedName {
                                                                children: [
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 12,
                                                                        _parent: Some(
                                                                            11,
                                                                        ),
                                                                    },
                                                                ],
                                                                [RANGE]
                                                                _id: 11,
                                                                _parent: Some(
                                                                    10,
                                                                ),
                                                            },
                                                        ),
                                                        CasePattern(
                                                            CasePattern {
                                                                children: Some(
                                                                    TuplePattern(
                                                                        TuplePattern {
                                                                            children: [
                                                                                CasePattern(
                                                                                    CasePattern {
                                                                                        children: Some(
                                                                                            DottedName(
                                                                                                DottedName {
                                                                                                    children: [
                                                                                                        Identifier {
                                                                                                            [RANGE]
                                                                                                            _id: 17,
                                                                                                            _parent: Some(
                                                                                                                16,
                                                                                                            ),
                                                                                                        },
                                                                                                    ],
                                                                                                    [RANGE]
                                                                                                    _id: 16,
                                                                                                    _parent: Some(
                                                                                                        15,
                                                                                                    ),
                                                                                                },
                                                                                            ),
                                                                                        ),
                                                                                        [RANGE]
                                                                                        _id: 15,
                                                                                        _parent: Some(
                                                                                            14,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                                CasePattern(
                                                                                    CasePattern {
                                                                                        children: Some(
                                                                                            DottedName(
                                                                                                DottedName {
                                                                                                    children: [
                                                                                                        Identifier {
                                                                                                            [RANGE]
                                                                                                            _id: 20,
                                                                                                            _parent: Some(
                                                                                                                19,
                                                                                                            ),
                                                                                                        },
                                                                                                    ],
                                                                                                    [RANGE]
                                                                                                    _id: 19,
                                                                                                    _parent: Some(
                                                                                                        18,
                                                                                                    ),
                                                                                                },
                                                                                            ),
                                                                                        ),
                                                                                        [RANGE]
                                                                                        _id: 18,
                                                                                        _parent: Some(
                                                                                            14,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                            ],
                                                                            [RANGE]
                                                                            _id: 14,
                                                                            _parent: Some(
                                                                                13,
                                                                            ),
                                                                        },
                                                                    ),
                                                                ),
                                                                [RANGE]
                                                                _id: 13,
                                                                _parent: Some(
                                                                    10,
                                                                ),
                                                            },
                                                        ),
                                                        CasePattern(
                                                            CasePattern {
                                                                children: Some(
                                                                    KeywordPattern(
                                                                        KeywordPattern {
                                                                            children: [
                                                                                Identifier(
                                                                                    Identifier {
                                                                                        [RANGE]
                                                                                        _id: 23,
                                                                                        _parent: Some(
                                                                                            22,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                                DottedName(
                                                                                    DottedName {
                                                                                        children: [
                                                                                            Identifier {
                                                                                                [RANGE]
                                                                                                _id: 25,
                                                                                                _parent: Some(
                                                                                                    24,
                                                                                                ),
                                                                                            },
                                                                                            Identifier {
                                                                                                [RANGE]
                                                                                                _id: 26,
                                                                                                _parent: Some(
                                                                                                    24,
                                                                                                ),
                                                                                            },
                                                                                        ],
                                                                                        [RANGE]
                                                                                        _id: 24,
                                                                                        _parent: Some(
                                                                                            22,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                            ],
                                                                            [RANGE]
                                                                            _id: 22,
                                                                            _parent: Some(
                                                                                21,
                                                                            ),
                                                                        },
                                                                    ),
                                                                ),
                                                                [RANGE]
                                                                _id: 21,
                                                                _parent: Some(
                                                                    10,
                                                                ),
                                                            },
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 10,
                                                    _parent: Some(
                                                        9,
                                                    ),
                                                },
                                            ),
                                        ),
                                        [RANGE]
                                        _id: 9,
                                        _parent: Some(
                                            8,
                                        ),
                                    },
                                ],
                                [RANGE]
                                _id: 8,
                                _parent: Some(
                                    7,
                                ),
                            },
                            CaseClause {
                                consequence: Block {
                                    alternative: [],
                                    children: [
                                        SimpleStatement(
                                            PassStatement(
                                                PassStatement {
                                                    [RANGE]
                                                    _id: 40,
                                                    _parent: Some(
                                                        39,
                                                    ),
                                                },
                                            ),
                                        ),
                                    ],
                                    [RANGE]
                                    _id: 39,
                                    _parent: Some(
                                        34,
                                    ),
                                },
                                guard: None,
                                children: [
                                    CasePattern {
                                        children: Some(
                                            ClassPattern(
                                                ClassPattern {
                                                    children: [
                                                        DottedName(
                                                            DottedName {
                                                                children: [
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 38,
                                                                        _parent: Some(
                                                                            37,
                                                                        ),
                                                                    },
                                                                ],
                                                                [RANGE]
                                                                _id: 37,
                                                                _parent: Some(
                                                                    36,
                                                                ),
                                                            },
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 36,
                                                    _parent: Some(
                                                        35,
                                                    ),
                                                },
                                            ),
                                        ),
                                        [RANGE]
                                        _id: 35,
                                        _parent: Some(
                                            34,
                                        ),
                                    },
                                ],
                                [RANGE]
                                _id: 34,
                                _parent: Some(
                                    7,
                                ),
                            },
                        ],
                        children: [],
                        [RANGE]
                        _id: 7,
                        _parent: Some(
                            1,
                        ),
                    },
                    subject: [
                        PrimaryExpression(
                            Call(
                                Call {
                                    function: Attribute(
                                        Attribute {
                                            object: Identifier(
                                                Identifier {
                                                    [RANGE]
                                                    _id: 4,
                                                    _parent: Some(
                                                        3,
                                                    ),
                                                },
                                            ),
                                            attribute: Identifier {
                                                [RANGE]
                                                _id: 5,
                                                _parent: Some(
                                                    3,
                                                ),
                                            },
                                            [RANGE]
                                            _id: 3,
                                            _parent: Some(
                                                2,
                                            ),
                                        },
                                    ),
                                    arguments: ArgumentList(
                                        ArgumentList {
                                            children: [],
                                            [RANGE]
                                            _id: 6,
                                            _parent: Some(
                                                2,
                                            ),
                                        },
                                    ),
                                    [RANGE]
                                    _id: 2,
                                    _parent: Some(
                                        1,
                                    ),
                                },
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
