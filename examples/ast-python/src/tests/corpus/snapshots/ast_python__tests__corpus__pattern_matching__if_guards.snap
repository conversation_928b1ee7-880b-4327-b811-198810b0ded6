---
source: src/tests/corpus/pattern_matching.rs
expression: module
---
Module {
    children: [
        CompoundStatement(
            MatchStatement(
                MatchStatement {
                    body: Block {
                        alternative: [
                            CaseClause {
                                consequence: Block {
                                    alternative: [],
                                    children: [
                                        SimpleStatement(
                                            ExpressionStatement(
                                                ExpressionStatement {
                                                    children: [
                                                        Assignment(
                                                            Assignment {
                                                                right: Some(
                                                                    Expression(
                                                                        PrimaryExpression(
                                                                            False(
                                                                                False {
                                                                                    [RANGE]
                                                                                    _id: 13,
                                                                                    _parent: Some(
                                                                                        11,
                                                                                    ),
                                                                                },
                                                                            ),
                                                                        ),
                                                                    ),
                                                                ),
                                                                Type: None,
                                                                left: Pattern(
                                                                    Identifier(
                                                                        Identifier {
                                                                            [RANGE]
                                                                            _id: 12,
                                                                            _parent: Some(
                                                                                11,
                                                                            ),
                                                                        },
                                                                    ),
                                                                ),
                                                                [RANGE]
                                                                _id: 11,
                                                                _parent: Some(
                                                                    10,
                                                                ),
                                                            },
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 10,
                                                    _parent: Some(
                                                        9,
                                                    ),
                                                },
                                            ),
                                        ),
                                    ],
                                    [RANGE]
                                    _id: 9,
                                    _parent: Some(
                                        4,
                                    ),
                                },
                                guard: Some(
                                    IfClause {
                                        children: PrimaryExpression(
                                            False(
                                                False {
                                                    [RANGE]
                                                    _id: 8,
                                                    _parent: Some(
                                                        7,
                                                    ),
                                                },
                                            ),
                                        ),
                                        [RANGE]
                                        _id: 7,
                                        _parent: Some(
                                            4,
                                        ),
                                    },
                                ),
                                children: [
                                    CasePattern {
                                        children: Some(
                                            Integer(
                                                Integer {
                                                    [RANGE]
                                                    _id: 6,
                                                    _parent: Some(
                                                        5,
                                                    ),
                                                },
                                            ),
                                        ),
                                        [RANGE]
                                        _id: 5,
                                        _parent: Some(
                                            4,
                                        ),
                                    },
                                ],
                                [RANGE]
                                _id: 4,
                                _parent: Some(
                                    3,
                                ),
                            },
                            CaseClause {
                                consequence: Block {
                                    alternative: [],
                                    children: [
                                        SimpleStatement(
                                            ExpressionStatement(
                                                ExpressionStatement {
                                                    children: [
                                                        Assignment(
                                                            Assignment {
                                                                right: Some(
                                                                    Expression(
                                                                        PrimaryExpression(
                                                                            True(
                                                                                True {
                                                                                    [RANGE]
                                                                                    _id: 23,
                                                                                    _parent: Some(
                                                                                        21,
                                                                                    ),
                                                                                },
                                                                            ),
                                                                        ),
                                                                    ),
                                                                ),
                                                                Type: None,
                                                                left: Pattern(
                                                                    Identifier(
                                                                        Identifier {
                                                                            [RANGE]
                                                                            _id: 22,
                                                                            _parent: Some(
                                                                                21,
                                                                            ),
                                                                        },
                                                                    ),
                                                                ),
                                                                [RANGE]
                                                                _id: 21,
                                                                _parent: Some(
                                                                    20,
                                                                ),
                                                            },
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 20,
                                                    _parent: Some(
                                                        19,
                                                    ),
                                                },
                                            ),
                                        ),
                                    ],
                                    [RANGE]
                                    _id: 19,
                                    _parent: Some(
                                        14,
                                    ),
                                },
                                guard: Some(
                                    IfClause {
                                        children: PrimaryExpression(
                                            True(
                                                True {
                                                    [RANGE]
                                                    _id: 18,
                                                    _parent: Some(
                                                        17,
                                                    ),
                                                },
                                            ),
                                        ),
                                        [RANGE]
                                        _id: 17,
                                        _parent: Some(
                                            14,
                                        ),
                                    },
                                ),
                                children: [
                                    CasePattern {
                                        children: Some(
                                            Integer(
                                                Integer {
                                                    [RANGE]
                                                    _id: 16,
                                                    _parent: Some(
                                                        15,
                                                    ),
                                                },
                                            ),
                                        ),
                                        [RANGE]
                                        _id: 15,
                                        _parent: Some(
                                            14,
                                        ),
                                    },
                                ],
                                [RANGE]
                                _id: 14,
                                _parent: Some(
                                    3,
                                ),
                            },
                        ],
                        children: [],
                        [RANGE]
                        _id: 3,
                        _parent: Some(
                            1,
                        ),
                    },
                    subject: [
                        PrimaryExpression(
                            Integer(
                                Integer {
                                    [RANGE]
                                    _id: 2,
                                    _parent: Some(
                                        1,
                                    ),
                                },
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
