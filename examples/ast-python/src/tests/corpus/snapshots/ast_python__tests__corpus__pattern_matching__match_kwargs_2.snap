---
source: src/tests/corpus/pattern_matching.rs
expression: module
---
Module {
    children: [
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Assignment(
                            Assignment {
                                right: Some(
                                    Expression(
                                        PrimaryExpression(
                                            Call(
                                                Call {
                                                    function: Identifier(
                                                        Identifier {
                                                            [RANGE]
                                                            _id: 5,
                                                            _parent: Some(
                                                                4,
                                                            ),
                                                        },
                                                    ),
                                                    arguments: ArgumentList(
                                                        ArgumentList {
                                                            children: [
                                                                KeywordArgument(
                                                                    KeywordArgument {
                                                                        name: Identifier {
                                                                            [RANGE]
                                                                            _id: 8,
                                                                            _parent: Some(
                                                                                7,
                                                                            ),
                                                                        },
                                                                        value: PrimaryExpression(
                                                                            Identifier(
                                                                                Identifier {
                                                                                    [RANGE]
                                                                                    _id: 9,
                                                                                    _parent: Some(
                                                                                        7,
                                                                                    ),
                                                                                },
                                                                            ),
                                                                        ),
                                                                        [RANGE]
                                                                        _id: 7,
                                                                        _parent: Some(
                                                                            6,
                                                                        ),
                                                                    },
                                                                ),
                                                                Expression(
                                                                    PrimaryExpression(
                                                                        Identifier(
                                                                            Identifier {
                                                                                [RANGE]
                                                                                _id: 10,
                                                                                _parent: Some(
                                                                                    6,
                                                                                ),
                                                                            },
                                                                        ),
                                                                    ),
                                                                ),
                                                            ],
                                                            [RANGE]
                                                            _id: 6,
                                                            _parent: Some(
                                                                4,
                                                            ),
                                                        },
                                                    ),
                                                    [RANGE]
                                                    _id: 4,
                                                    _parent: Some(
                                                        2,
                                                    ),
                                                },
                                            ),
                                        ),
                                    ),
                                ),
                                Type: None,
                                left: Pattern(
                                    Identifier(
                                        Identifier {
                                            [RANGE]
                                            _id: 3,
                                            _parent: Some(
                                                2,
                                            ),
                                        },
                                    ),
                                ),
                                [RANGE]
                                _id: 2,
                                _parent: Some(
                                    1,
                                ),
                            },
                        ),
                    ],
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
