---
source: src/tests/corpus/pattern_matching.rs
expression: module
---
Module {
    children: [
        CompoundStatement(
            MatchStatement(
                MatchStatement {
                    body: Block {
                        alternative: [
                            CaseClause {
                                consequence: Block {
                                    alternative: [],
                                    children: [
                                        CompoundStatement(
                                            ForStatement(
                                                ForStatement {
                                                    alternative: None,
                                                    body: Block {
                                                        alternative: [],
                                                        children: [
                                                            SimpleStatement(
                                                                ExpressionStatement(
                                                                    ExpressionStatement {
                                                                        children: [
                                                                            Expression(
                                                                                PrimaryExpression(
                                                                                    Call(
                                                                                        Call {
                                                                                            function: Attribute(
                                                                                                Attribute {
                                                                                                    object: Identifier(
                                                                                                        Identifier {
                                                                                                            [RANGE]
                                                                                                            _id: 27,
                                                                                                            _parent: Some(
                                                                                                                26,
                                                                                                            ),
                                                                                                        },
                                                                                                    ),
                                                                                                    attribute: Identifier {
                                                                                                        [RANGE]
                                                                                                        _id: 28,
                                                                                                        _parent: Some(
                                                                                                            26,
                                                                                                        ),
                                                                                                    },
                                                                                                    [RANGE]
                                                                                                    _id: 26,
                                                                                                    _parent: Some(
                                                                                                        25,
                                                                                                    ),
                                                                                                },
                                                                                            ),
                                                                                            arguments: ArgumentList(
                                                                                                ArgumentList {
                                                                                                    children: [
                                                                                                        Expression(
                                                                                                            PrimaryExpression(
                                                                                                                Identifier(
                                                                                                                    Identifier {
                                                                                                                        [RANGE]
                                                                                                                        _id: 30,
                                                                                                                        _parent: Some(
                                                                                                                            29,
                                                                                                                        ),
                                                                                                                    },
                                                                                                                ),
                                                                                                            ),
                                                                                                        ),
                                                                                                        Expression(
                                                                                                            PrimaryExpression(
                                                                                                                Identifier(
                                                                                                                    Identifier {
                                                                                                                        [RANGE]
                                                                                                                        _id: 31,
                                                                                                                        _parent: Some(
                                                                                                                            29,
                                                                                                                        ),
                                                                                                                    },
                                                                                                                ),
                                                                                                            ),
                                                                                                        ),
                                                                                                    ],
                                                                                                    [RANGE]
                                                                                                    _id: 29,
                                                                                                    _parent: Some(
                                                                                                        25,
                                                                                                    ),
                                                                                                },
                                                                                            ),
                                                                                            [RANGE]
                                                                                            _id: 25,
                                                                                            _parent: Some(
                                                                                                24,
                                                                                            ),
                                                                                        },
                                                                                    ),
                                                                                ),
                                                                            ),
                                                                        ],
                                                                        [RANGE]
                                                                        _id: 24,
                                                                        _parent: Some(
                                                                            23,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ],
                                                        [RANGE]
                                                        _id: 23,
                                                        _parent: Some(
                                                            20,
                                                        ),
                                                    },
                                                    right: Expression(
                                                        PrimaryExpression(
                                                            Identifier(
                                                                Identifier {
                                                                    [RANGE]
                                                                    _id: 22,
                                                                    _parent: Some(
                                                                        20,
                                                                    ),
                                                                },
                                                            ),
                                                        ),
                                                    ),
                                                    left: Pattern(
                                                        Identifier(
                                                            Identifier {
                                                                [RANGE]
                                                                _id: 21,
                                                                _parent: Some(
                                                                    20,
                                                                ),
                                                            },
                                                        ),
                                                    ),
                                                    [RANGE]
                                                    _id: 20,
                                                    _parent: Some(
                                                        19,
                                                    ),
                                                },
                                            ),
                                        ),
                                    ],
                                    [RANGE]
                                    _id: 19,
                                    _parent: Some(
                                        8,
                                    ),
                                },
                                guard: None,
                                children: [
                                    CasePattern {
                                        children: Some(
                                            ListPattern(
                                                ListPattern {
                                                    children: [
                                                        CasePattern(
                                                            CasePattern {
                                                                children: Some(
                                                                    String(
                                                                        String {
                                                                            children: [
                                                                                StringStart(
                                                                                    StringStart {
                                                                                        [RANGE]
                                                                                        _id: 13,
                                                                                        _parent: Some(
                                                                                            12,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                                StringContent(
                                                                                    StringContent {
                                                                                        children: [],
                                                                                        [RANGE]
                                                                                        _id: 14,
                                                                                        _parent: Some(
                                                                                            12,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                                StringEnd(
                                                                                    StringEnd {
                                                                                        [RANGE]
                                                                                        _id: 15,
                                                                                        _parent: Some(
                                                                                            12,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                            ],
                                                                            [RANGE]
                                                                            _id: 12,
                                                                            _parent: Some(
                                                                                11,
                                                                            ),
                                                                        },
                                                                    ),
                                                                ),
                                                                [RANGE]
                                                                _id: 11,
                                                                _parent: Some(
                                                                    10,
                                                                ),
                                                            },
                                                        ),
                                                        CasePattern(
                                                            CasePattern {
                                                                children: Some(
                                                                    SplatPattern(
                                                                        SplatPattern {
                                                                            children: Some(
                                                                                Identifier {
                                                                                    [RANGE]
                                                                                    _id: 18,
                                                                                    _parent: Some(
                                                                                        17,
                                                                                    ),
                                                                                },
                                                                            ),
                                                                            [RANGE]
                                                                            _id: 17,
                                                                            _parent: Some(
                                                                                16,
                                                                            ),
                                                                        },
                                                                    ),
                                                                ),
                                                                [RANGE]
                                                                _id: 16,
                                                                _parent: Some(
                                                                    10,
                                                                ),
                                                            },
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 10,
                                                    _parent: Some(
                                                        9,
                                                    ),
                                                },
                                            ),
                                        ),
                                        [RANGE]
                                        _id: 9,
                                        _parent: Some(
                                            8,
                                        ),
                                    },
                                ],
                                [RANGE]
                                _id: 8,
                                _parent: Some(
                                    7,
                                ),
                            },
                        ],
                        children: [],
                        [RANGE]
                        _id: 7,
                        _parent: Some(
                            1,
                        ),
                    },
                    subject: [
                        PrimaryExpression(
                            Call(
                                Call {
                                    function: Attribute(
                                        Attribute {
                                            object: Identifier(
                                                Identifier {
                                                    [RANGE]
                                                    _id: 4,
                                                    _parent: Some(
                                                        3,
                                                    ),
                                                },
                                            ),
                                            attribute: Identifier {
                                                [RANGE]
                                                _id: 5,
                                                _parent: Some(
                                                    3,
                                                ),
                                            },
                                            [RANGE]
                                            _id: 3,
                                            _parent: Some(
                                                2,
                                            ),
                                        },
                                    ),
                                    arguments: ArgumentList(
                                        ArgumentList {
                                            children: [],
                                            [RANGE]
                                            _id: 6,
                                            _parent: Some(
                                                2,
                                            ),
                                        },
                                    ),
                                    [RANGE]
                                    _id: 2,
                                    _parent: Some(
                                        1,
                                    ),
                                },
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
