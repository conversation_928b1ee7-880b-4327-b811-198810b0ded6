---
source: src/tests/corpus/pattern_matching.rs
expression: module
---
Module {
    children: [
        CompoundStatement(
            MatchStatement(
                MatchStatement {
                    body: Block {
                        alternative: [
                            CaseClause {
                                consequence: Block {
                                    alternative: [],
                                    children: [
                                        SimpleStatement(
                                            ExpressionStatement(
                                                ExpressionStatement {
                                                    children: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                Call(
                                                                    Call {
                                                                        function: Identifier(
                                                                            Identifier {
                                                                                [RANGE]
                                                                                _id: 26,
                                                                                _parent: Some(
                                                                                    25,
                                                                                ),
                                                                            },
                                                                        ),
                                                                        arguments: ArgumentList(
                                                                            ArgumentList {
                                                                                children: [
                                                                                    Expression(
                                                                                        PrimaryExpression(
                                                                                            Identifier(
                                                                                                Identifier {
                                                                                                    [RANGE]
                                                                                                    _id: 28,
                                                                                                    _parent: Some(
                                                                                                        27,
                                                                                                    ),
                                                                                                },
                                                                                            ),
                                                                                        ),
                                                                                    ),
                                                                                    Expression(
                                                                                        PrimaryExpression(
                                                                                            Identifier(
                                                                                                Identifier {
                                                                                                    [RANGE]
                                                                                                    _id: 29,
                                                                                                    _parent: Some(
                                                                                                        27,
                                                                                                    ),
                                                                                                },
                                                                                            ),
                                                                                        ),
                                                                                    ),
                                                                                ],
                                                                                [RANGE]
                                                                                _id: 27,
                                                                                _parent: Some(
                                                                                    25,
                                                                                ),
                                                                            },
                                                                        ),
                                                                        [RANGE]
                                                                        _id: 25,
                                                                        _parent: Some(
                                                                            24,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 24,
                                                    _parent: Some(
                                                        23,
                                                    ),
                                                },
                                            ),
                                        ),
                                    ],
                                    [RANGE]
                                    _id: 23,
                                    _parent: Some(
                                        8,
                                    ),
                                },
                                guard: None,
                                children: [
                                    CasePattern {
                                        children: Some(
                                            ClassPattern(
                                                ClassPattern {
                                                    children: [
                                                        DottedName(
                                                            DottedName {
                                                                children: [
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 12,
                                                                        _parent: Some(
                                                                            11,
                                                                        ),
                                                                    },
                                                                ],
                                                                [RANGE]
                                                                _id: 11,
                                                                _parent: Some(
                                                                    10,
                                                                ),
                                                            },
                                                        ),
                                                        CasePattern(
                                                            CasePattern {
                                                                children: Some(
                                                                    KeywordPattern(
                                                                        KeywordPattern {
                                                                            children: [
                                                                                Identifier(
                                                                                    Identifier {
                                                                                        [RANGE]
                                                                                        _id: 15,
                                                                                        _parent: Some(
                                                                                            14,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                                TuplePattern(
                                                                                    TuplePattern {
                                                                                        children: [
                                                                                            CasePattern(
                                                                                                CasePattern {
                                                                                                    children: Some(
                                                                                                        DottedName(
                                                                                                            DottedName {
                                                                                                                children: [
                                                                                                                    Identifier {
                                                                                                                        [RANGE]
                                                                                                                        _id: 19,
                                                                                                                        _parent: Some(
                                                                                                                            18,
                                                                                                                        ),
                                                                                                                    },
                                                                                                                ],
                                                                                                                [RANGE]
                                                                                                                _id: 18,
                                                                                                                _parent: Some(
                                                                                                                    17,
                                                                                                                ),
                                                                                                            },
                                                                                                        ),
                                                                                                    ),
                                                                                                    [RANGE]
                                                                                                    _id: 17,
                                                                                                    _parent: Some(
                                                                                                        16,
                                                                                                    ),
                                                                                                },
                                                                                            ),
                                                                                            CasePattern(
                                                                                                CasePattern {
                                                                                                    children: Some(
                                                                                                        DottedName(
                                                                                                            DottedName {
                                                                                                                children: [
                                                                                                                    Identifier {
                                                                                                                        [RANGE]
                                                                                                                        _id: 22,
                                                                                                                        _parent: Some(
                                                                                                                            21,
                                                                                                                        ),
                                                                                                                    },
                                                                                                                ],
                                                                                                                [RANGE]
                                                                                                                _id: 21,
                                                                                                                _parent: Some(
                                                                                                                    20,
                                                                                                                ),
                                                                                                            },
                                                                                                        ),
                                                                                                    ),
                                                                                                    [RANGE]
                                                                                                    _id: 20,
                                                                                                    _parent: Some(
                                                                                                        16,
                                                                                                    ),
                                                                                                },
                                                                                            ),
                                                                                        ],
                                                                                        [RANGE]
                                                                                        _id: 16,
                                                                                        _parent: Some(
                                                                                            14,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                            ],
                                                                            [RANGE]
                                                                            _id: 14,
                                                                            _parent: Some(
                                                                                13,
                                                                            ),
                                                                        },
                                                                    ),
                                                                ),
                                                                [RANGE]
                                                                _id: 13,
                                                                _parent: Some(
                                                                    10,
                                                                ),
                                                            },
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 10,
                                                    _parent: Some(
                                                        9,
                                                    ),
                                                },
                                            ),
                                        ),
                                        [RANGE]
                                        _id: 9,
                                        _parent: Some(
                                            8,
                                        ),
                                    },
                                ],
                                [RANGE]
                                _id: 8,
                                _parent: Some(
                                    7,
                                ),
                            },
                            CaseClause {
                                consequence: Block {
                                    alternative: [],
                                    children: [
                                        SimpleStatement(
                                            ExpressionStatement(
                                                ExpressionStatement {
                                                    children: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                Call(
                                                                    Call {
                                                                        function: Attribute(
                                                                            Attribute {
                                                                                object: Identifier(
                                                                                    Identifier {
                                                                                        [RANGE]
                                                                                        _id: 50,
                                                                                        _parent: Some(
                                                                                            49,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                                attribute: Identifier {
                                                                                    [RANGE]
                                                                                    _id: 51,
                                                                                    _parent: Some(
                                                                                        49,
                                                                                    ),
                                                                                },
                                                                                [RANGE]
                                                                                _id: 49,
                                                                                _parent: Some(
                                                                                    48,
                                                                                ),
                                                                            },
                                                                        ),
                                                                        arguments: ArgumentList(
                                                                            ArgumentList {
                                                                                children: [],
                                                                                [RANGE]
                                                                                _id: 52,
                                                                                _parent: Some(
                                                                                    48,
                                                                                ),
                                                                            },
                                                                        ),
                                                                        [RANGE]
                                                                        _id: 48,
                                                                        _parent: Some(
                                                                            47,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 47,
                                                    _parent: Some(
                                                        46,
                                                    ),
                                                },
                                            ),
                                        ),
                                    ],
                                    [RANGE]
                                    _id: 46,
                                    _parent: Some(
                                        30,
                                    ),
                                },
                                guard: None,
                                children: [
                                    CasePattern {
                                        children: Some(
                                            UnionPattern(
                                                UnionPattern {
                                                    children: [
                                                        ClassPattern(
                                                            ClassPattern {
                                                                children: [
                                                                    DottedName(
                                                                        DottedName {
                                                                            children: [
                                                                                Identifier {
                                                                                    [RANGE]
                                                                                    _id: 35,
                                                                                    _parent: Some(
                                                                                        34,
                                                                                    ),
                                                                                },
                                                                            ],
                                                                            [RANGE]
                                                                            _id: 34,
                                                                            _parent: Some(
                                                                                33,
                                                                            ),
                                                                        },
                                                                    ),
                                                                    CasePattern(
                                                                        CasePattern {
                                                                            children: Some(
                                                                                KeywordPattern(
                                                                                    KeywordPattern {
                                                                                        children: [
                                                                                            Identifier(
                                                                                                Identifier {
                                                                                                    [RANGE]
                                                                                                    _id: 38,
                                                                                                    _parent: Some(
                                                                                                        37,
                                                                                                    ),
                                                                                                },
                                                                                            ),
                                                                                            String(
                                                                                                String {
                                                                                                    children: [
                                                                                                        StringStart(
                                                                                                            StringStart {
                                                                                                                [RANGE]
                                                                                                                _id: 40,
                                                                                                                _parent: Some(
                                                                                                                    39,
                                                                                                                ),
                                                                                                            },
                                                                                                        ),
                                                                                                        StringContent(
                                                                                                            StringContent {
                                                                                                                children: [],
                                                                                                                [RANGE]
                                                                                                                _id: 41,
                                                                                                                _parent: Some(
                                                                                                                    39,
                                                                                                                ),
                                                                                                            },
                                                                                                        ),
                                                                                                        StringEnd(
                                                                                                            StringEnd {
                                                                                                                [RANGE]
                                                                                                                _id: 42,
                                                                                                                _parent: Some(
                                                                                                                    39,
                                                                                                                ),
                                                                                                            },
                                                                                                        ),
                                                                                                    ],
                                                                                                    [RANGE]
                                                                                                    _id: 39,
                                                                                                    _parent: Some(
                                                                                                        37,
                                                                                                    ),
                                                                                                },
                                                                                            ),
                                                                                        ],
                                                                                        [RANGE]
                                                                                        _id: 37,
                                                                                        _parent: Some(
                                                                                            36,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                            ),
                                                                            [RANGE]
                                                                            _id: 36,
                                                                            _parent: Some(
                                                                                33,
                                                                            ),
                                                                        },
                                                                    ),
                                                                ],
                                                                [RANGE]
                                                                _id: 33,
                                                                _parent: Some(
                                                                    32,
                                                                ),
                                                            },
                                                        ),
                                                        ClassPattern(
                                                            ClassPattern {
                                                                children: [
                                                                    DottedName(
                                                                        DottedName {
                                                                            children: [
                                                                                Identifier {
                                                                                    [RANGE]
                                                                                    _id: 45,
                                                                                    _parent: Some(
                                                                                        44,
                                                                                    ),
                                                                                },
                                                                            ],
                                                                            [RANGE]
                                                                            _id: 44,
                                                                            _parent: Some(
                                                                                43,
                                                                            ),
                                                                        },
                                                                    ),
                                                                ],
                                                                [RANGE]
                                                                _id: 43,
                                                                _parent: Some(
                                                                    32,
                                                                ),
                                                            },
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 32,
                                                    _parent: Some(
                                                        31,
                                                    ),
                                                },
                                            ),
                                        ),
                                        [RANGE]
                                        _id: 31,
                                        _parent: Some(
                                            30,
                                        ),
                                    },
                                ],
                                [RANGE]
                                _id: 30,
                                _parent: Some(
                                    7,
                                ),
                            },
                            CaseClause {
                                consequence: Block {
                                    alternative: [],
                                    children: [
                                        SimpleStatement(
                                            ExpressionStatement(
                                                ExpressionStatement {
                                                    children: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                Call(
                                                                    Call {
                                                                        function: Attribute(
                                                                            Attribute {
                                                                                object: Identifier(
                                                                                    Identifier {
                                                                                        [RANGE]
                                                                                        _id: 69,
                                                                                        _parent: Some(
                                                                                            68,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                                attribute: Identifier {
                                                                                    [RANGE]
                                                                                    _id: 70,
                                                                                    _parent: Some(
                                                                                        68,
                                                                                    ),
                                                                                },
                                                                                [RANGE]
                                                                                _id: 68,
                                                                                _parent: Some(
                                                                                    67,
                                                                                ),
                                                                            },
                                                                        ),
                                                                        arguments: ArgumentList(
                                                                            ArgumentList {
                                                                                children: [],
                                                                                [RANGE]
                                                                                _id: 71,
                                                                                _parent: Some(
                                                                                    67,
                                                                                ),
                                                                            },
                                                                        ),
                                                                        [RANGE]
                                                                        _id: 67,
                                                                        _parent: Some(
                                                                            66,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 66,
                                                    _parent: Some(
                                                        65,
                                                    ),
                                                },
                                            ),
                                        ),
                                        SimpleStatement(
                                            ExpressionStatement(
                                                ExpressionStatement {
                                                    children: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                Ellipsis(
                                                                    Ellipsis {
                                                                        [RANGE]
                                                                        _id: 73,
                                                                        _parent: Some(
                                                                            72,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 72,
                                                    _parent: Some(
                                                        65,
                                                    ),
                                                },
                                            ),
                                        ),
                                    ],
                                    [RANGE]
                                    _id: 65,
                                    _parent: Some(
                                        53,
                                    ),
                                },
                                guard: None,
                                children: [
                                    CasePattern {
                                        children: Some(
                                            ClassPattern(
                                                ClassPattern {
                                                    children: [
                                                        DottedName(
                                                            DottedName {
                                                                children: [
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 57,
                                                                        _parent: Some(
                                                                            56,
                                                                        ),
                                                                    },
                                                                ],
                                                                [RANGE]
                                                                _id: 56,
                                                                _parent: Some(
                                                                    55,
                                                                ),
                                                            },
                                                        ),
                                                        CasePattern(
                                                            CasePattern {
                                                                children: Some(
                                                                    KeywordPattern(
                                                                        KeywordPattern {
                                                                            children: [
                                                                                Identifier(
                                                                                    Identifier {
                                                                                        [RANGE]
                                                                                        _id: 60,
                                                                                        _parent: Some(
                                                                                            59,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                                String(
                                                                                    String {
                                                                                        children: [
                                                                                            StringStart(
                                                                                                StringStart {
                                                                                                    [RANGE]
                                                                                                    _id: 62,
                                                                                                    _parent: Some(
                                                                                                        61,
                                                                                                    ),
                                                                                                },
                                                                                            ),
                                                                                            StringContent(
                                                                                                StringContent {
                                                                                                    children: [],
                                                                                                    [RANGE]
                                                                                                    _id: 63,
                                                                                                    _parent: Some(
                                                                                                        61,
                                                                                                    ),
                                                                                                },
                                                                                            ),
                                                                                            StringEnd(
                                                                                                StringEnd {
                                                                                                    [RANGE]
                                                                                                    _id: 64,
                                                                                                    _parent: Some(
                                                                                                        61,
                                                                                                    ),
                                                                                                },
                                                                                            ),
                                                                                        ],
                                                                                        [RANGE]
                                                                                        _id: 61,
                                                                                        _parent: Some(
                                                                                            59,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                            ],
                                                                            [RANGE]
                                                                            _id: 59,
                                                                            _parent: Some(
                                                                                58,
                                                                            ),
                                                                        },
                                                                    ),
                                                                ),
                                                                [RANGE]
                                                                _id: 58,
                                                                _parent: Some(
                                                                    55,
                                                                ),
                                                            },
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 55,
                                                    _parent: Some(
                                                        54,
                                                    ),
                                                },
                                            ),
                                        ),
                                        [RANGE]
                                        _id: 54,
                                        _parent: Some(
                                            53,
                                        ),
                                    },
                                ],
                                [RANGE]
                                _id: 53,
                                _parent: Some(
                                    7,
                                ),
                            },
                            CaseClause {
                                consequence: Block {
                                    alternative: [],
                                    children: [
                                        SimpleStatement(
                                            PassStatement(
                                                PassStatement {
                                                    [RANGE]
                                                    _id: 80,
                                                    _parent: Some(
                                                        79,
                                                    ),
                                                },
                                            ),
                                        ),
                                    ],
                                    [RANGE]
                                    _id: 79,
                                    _parent: Some(
                                        74,
                                    ),
                                },
                                guard: None,
                                children: [
                                    CasePattern {
                                        children: Some(
                                            ClassPattern(
                                                ClassPattern {
                                                    children: [
                                                        DottedName(
                                                            DottedName {
                                                                children: [
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 78,
                                                                        _parent: Some(
                                                                            77,
                                                                        ),
                                                                    },
                                                                ],
                                                                [RANGE]
                                                                _id: 77,
                                                                _parent: Some(
                                                                    76,
                                                                ),
                                                            },
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 76,
                                                    _parent: Some(
                                                        75,
                                                    ),
                                                },
                                            ),
                                        ),
                                        [RANGE]
                                        _id: 75,
                                        _parent: Some(
                                            74,
                                        ),
                                    },
                                ],
                                [RANGE]
                                _id: 74,
                                _parent: Some(
                                    7,
                                ),
                            },
                            CaseClause {
                                consequence: Block {
                                    alternative: [],
                                    children: [
                                        SimpleStatement(
                                            RaiseStatement(
                                                RaiseStatement {
                                                    cause: None,
                                                    children: Some(
                                                        Expression(
                                                            PrimaryExpression(
                                                                Call(
                                                                    Call {
                                                                        function: Identifier(
                                                                            Identifier {
                                                                                [RANGE]
                                                                                _id: 88,
                                                                                _parent: Some(
                                                                                    87,
                                                                                ),
                                                                            },
                                                                        ),
                                                                        arguments: ArgumentList(
                                                                            ArgumentList {
                                                                                children: [
                                                                                    Expression(
                                                                                        PrimaryExpression(
                                                                                            String(
                                                                                                String {
                                                                                                    children: [
                                                                                                        StringStart(
                                                                                                            StringStart {
                                                                                                                [RANGE]
                                                                                                                _id: 91,
                                                                                                                _parent: Some(
                                                                                                                    90,
                                                                                                                ),
                                                                                                            },
                                                                                                        ),
                                                                                                        StringContent(
                                                                                                            StringContent {
                                                                                                                children: [],
                                                                                                                [RANGE]
                                                                                                                _id: 92,
                                                                                                                _parent: Some(
                                                                                                                    90,
                                                                                                                ),
                                                                                                            },
                                                                                                        ),
                                                                                                        Interpolation(
                                                                                                            Interpolation {
                                                                                                                format_specifier: None,
                                                                                                                type_conversion: None,
                                                                                                                expression: Expression(
                                                                                                                    PrimaryExpression(
                                                                                                                        Identifier(
                                                                                                                            Identifier {
                                                                                                                                [RANGE]
                                                                                                                                _id: 94,
                                                                                                                                _parent: Some(
                                                                                                                                    93,
                                                                                                                                ),
                                                                                                                            },
                                                                                                                        ),
                                                                                                                    ),
                                                                                                                ),
                                                                                                                [RANGE]
                                                                                                                _id: 93,
                                                                                                                _parent: Some(
                                                                                                                    90,
                                                                                                                ),
                                                                                                            },
                                                                                                        ),
                                                                                                        StringEnd(
                                                                                                            StringEnd {
                                                                                                                [RANGE]
                                                                                                                _id: 95,
                                                                                                                _parent: Some(
                                                                                                                    90,
                                                                                                                ),
                                                                                                            },
                                                                                                        ),
                                                                                                    ],
                                                                                                    [RANGE]
                                                                                                    _id: 90,
                                                                                                    _parent: Some(
                                                                                                        89,
                                                                                                    ),
                                                                                                },
                                                                                            ),
                                                                                        ),
                                                                                    ),
                                                                                ],
                                                                                [RANGE]
                                                                                _id: 89,
                                                                                _parent: Some(
                                                                                    87,
                                                                                ),
                                                                            },
                                                                        ),
                                                                        [RANGE]
                                                                        _id: 87,
                                                                        _parent: Some(
                                                                            86,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ),
                                                    [RANGE]
                                                    _id: 86,
                                                    _parent: Some(
                                                        85,
                                                    ),
                                                },
                                            ),
                                        ),
                                    ],
                                    [RANGE]
                                    _id: 85,
                                    _parent: Some(
                                        81,
                                    ),
                                },
                                guard: None,
                                children: [
                                    CasePattern {
                                        children: Some(
                                            DottedName(
                                                DottedName {
                                                    children: [
                                                        Identifier {
                                                            [RANGE]
                                                            _id: 84,
                                                            _parent: Some(
                                                                83,
                                                            ),
                                                        },
                                                    ],
                                                    [RANGE]
                                                    _id: 83,
                                                    _parent: Some(
                                                        82,
                                                    ),
                                                },
                                            ),
                                        ),
                                        [RANGE]
                                        _id: 82,
                                        _parent: Some(
                                            81,
                                        ),
                                    },
                                ],
                                [RANGE]
                                _id: 81,
                                _parent: Some(
                                    7,
                                ),
                            },
                        ],
                        children: [],
                        [RANGE]
                        _id: 7,
                        _parent: Some(
                            1,
                        ),
                    },
                    subject: [
                        PrimaryExpression(
                            Call(
                                Call {
                                    function: Attribute(
                                        Attribute {
                                            object: Identifier(
                                                Identifier {
                                                    [RANGE]
                                                    _id: 4,
                                                    _parent: Some(
                                                        3,
                                                    ),
                                                },
                                            ),
                                            attribute: Identifier {
                                                [RANGE]
                                                _id: 5,
                                                _parent: Some(
                                                    3,
                                                ),
                                            },
                                            [RANGE]
                                            _id: 3,
                                            _parent: Some(
                                                2,
                                            ),
                                        },
                                    ),
                                    arguments: ArgumentList(
                                        ArgumentList {
                                            children: [],
                                            [RANGE]
                                            _id: 6,
                                            _parent: Some(
                                                2,
                                            ),
                                        },
                                    ),
                                    [RANGE]
                                    _id: 2,
                                    _parent: Some(
                                        1,
                                    ),
                                },
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
