---
source: src/tests/corpus/pattern_matching.rs
expression: module
---
Module {
    children: [
        CompoundStatement(
            IfStatement(
                IfStatement {
                    alternative: [],
                    consequence: Block {
                        alternative: [],
                        children: [
                            SimpleStatement(
                                ReturnStatement(
                                    ReturnStatement {
                                        children: Some(
                                            Expression(
                                                PrimaryExpression(
                                                    Integer(
                                                        Integer {
                                                            [RANGE]
                                                            _id: 21,
                                                            _parent: Some(
                                                                20,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ),
                                        ),
                                        [RANGE]
                                        _id: 20,
                                        _parent: Some(
                                            19,
                                        ),
                                    },
                                ),
                            ),
                        ],
                        [RANGE]
                        _id: 19,
                        _parent: Some(
                            1,
                        ),
                    },
                    condition: NamedExpression(
                        NamedExpression {
                            value: PrimaryExpression(
                                Call(
                                    Call {
                                        function: Attribute(
                                            Attribute {
                                                object: Identifier(
                                                    Identifier {
                                                        [RANGE]
                                                        _id: 6,
                                                        _parent: Some(
                                                            5,
                                                        ),
                                                    },
                                                ),
                                                attribute: Identifier {
                                                    [RANGE]
                                                    _id: 7,
                                                    _parent: Some(
                                                        5,
                                                    ),
                                                },
                                                [RANGE]
                                                _id: 5,
                                                _parent: Some(
                                                    4,
                                                ),
                                            },
                                        ),
                                        arguments: ArgumentList(
                                            ArgumentList {
                                                children: [
                                                    Expression(
                                                        PrimaryExpression(
                                                            String(
                                                                String {
                                                                    children: [
                                                                        StringStart(
                                                                            StringStart {
                                                                                [RANGE]
                                                                                _id: 10,
                                                                                _parent: Some(
                                                                                    9,
                                                                                ),
                                                                            },
                                                                        ),
                                                                        StringContent(
                                                                            StringContent {
                                                                                children: [],
                                                                                [RANGE]
                                                                                _id: 11,
                                                                                _parent: Some(
                                                                                    9,
                                                                                ),
                                                                            },
                                                                        ),
                                                                        StringEnd(
                                                                            StringEnd {
                                                                                [RANGE]
                                                                                _id: 12,
                                                                                _parent: Some(
                                                                                    9,
                                                                                ),
                                                                            },
                                                                        ),
                                                                    ],
                                                                    [RANGE]
                                                                    _id: 9,
                                                                    _parent: Some(
                                                                        8,
                                                                    ),
                                                                },
                                                            ),
                                                        ),
                                                    ),
                                                    Expression(
                                                        PrimaryExpression(
                                                            Identifier(
                                                                Identifier {
                                                                    [RANGE]
                                                                    _id: 13,
                                                                    _parent: Some(
                                                                        8,
                                                                    ),
                                                                },
                                                            ),
                                                        ),
                                                    ),
                                                    KeywordArgument(
                                                        KeywordArgument {
                                                            name: Identifier {
                                                                [RANGE]
                                                                _id: 15,
                                                                _parent: Some(
                                                                    14,
                                                                ),
                                                            },
                                                            value: PrimaryExpression(
                                                                Attribute(
                                                                    Attribute {
                                                                        object: Identifier(
                                                                            Identifier {
                                                                                [RANGE]
                                                                                _id: 17,
                                                                                _parent: Some(
                                                                                    16,
                                                                                ),
                                                                            },
                                                                        ),
                                                                        attribute: Identifier {
                                                                            [RANGE]
                                                                            _id: 18,
                                                                            _parent: Some(
                                                                                16,
                                                                            ),
                                                                        },
                                                                        [RANGE]
                                                                        _id: 16,
                                                                        _parent: Some(
                                                                            14,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                            [RANGE]
                                                            _id: 14,
                                                            _parent: Some(
                                                                8,
                                                            ),
                                                        },
                                                    ),
                                                ],
                                                [RANGE]
                                                _id: 8,
                                                _parent: Some(
                                                    4,
                                                ),
                                            },
                                        ),
                                        [RANGE]
                                        _id: 4,
                                        _parent: Some(
                                            2,
                                        ),
                                    },
                                ),
                            ),
                            name: Identifier {
                                [RANGE]
                                _id: 3,
                                _parent: Some(
                                    2,
                                ),
                            },
                            [RANGE]
                            _id: 2,
                            _parent: Some(
                                1,
                            ),
                        },
                    ),
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
