---
source: src/tests/corpus/statements.rs
expression: module
---
Module {
    children: [
        CompoundStatement(
            ClassDefinition(
                ClassDefinition {
                    type_parameters: None,
                    superclasses: None,
                    body: Block {
                        alternative: [],
                        children: [
                            CompoundStatement(
                                FunctionDefinition(
                                    FunctionDefinition {
                                        body: Block {
                                            alternative: [],
                                            children: [
                                                SimpleStatement(
                                                    ReturnStatement(
                                                        ReturnStatement {
                                                            children: Some(
                                                                Expression(
                                                                    PrimaryExpression(
                                                                        Identifier(
                                                                            Identifier {
                                                                                [RANGE]
                                                                                _id: 10,
                                                                                _parent: Some(
                                                                                    9,
                                                                                ),
                                                                            },
                                                                        ),
                                                                    ),
                                                                ),
                                                            ),
                                                            [RANGE]
                                                            _id: 9,
                                                            _parent: Some(
                                                                8,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ],
                                            [RANGE]
                                            _id: 8,
                                            _parent: Some(
                                                4,
                                            ),
                                        },
                                        name: Identifier {
                                            [RANGE]
                                            _id: 5,
                                            _parent: Some(
                                                4,
                                            ),
                                        },
                                        parameters: Parameters {
                                            children: [
                                                Identifier(
                                                    Identifier {
                                                        [RANGE]
                                                        _id: 7,
                                                        _parent: Some(
                                                            6,
                                                        ),
                                                    },
                                                ),
                                            ],
                                            [RANGE]
                                            _id: 6,
                                            _parent: Some(
                                                4,
                                            ),
                                        },
                                        type_parameters: None,
                                        return_type: None,
                                        [RANGE]
                                        _id: 4,
                                        _parent: Some(
                                            3,
                                        ),
                                    },
                                ),
                            ),
                        ],
                        [RANGE]
                        _id: 3,
                        _parent: Some(
                            1,
                        ),
                    },
                    name: Identifier {
                        [RANGE]
                        _id: 2,
                        _parent: Some(
                            1,
                        ),
                    },
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        CompoundStatement(
            ClassDefinition(
                ClassDefinition {
                    type_parameters: None,
                    superclasses: Some(
                        ArgumentList {
                            children: [],
                            [RANGE]
                            _id: 13,
                            _parent: Some(
                                11,
                            ),
                        },
                    ),
                    body: Block {
                        alternative: [],
                        children: [
                            SimpleStatement(
                                PassStatement(
                                    PassStatement {
                                        [RANGE]
                                        _id: 15,
                                        _parent: Some(
                                            14,
                                        ),
                                    },
                                ),
                            ),
                        ],
                        [RANGE]
                        _id: 14,
                        _parent: Some(
                            11,
                        ),
                    },
                    name: Identifier {
                        [RANGE]
                        _id: 12,
                        _parent: Some(
                            11,
                        ),
                    },
                    [RANGE]
                    _id: 11,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        CompoundStatement(
            ClassDefinition(
                ClassDefinition {
                    type_parameters: None,
                    superclasses: Some(
                        ArgumentList {
                            children: [
                                Expression(
                                    PrimaryExpression(
                                        Identifier(
                                            Identifier {
                                                [RANGE]
                                                _id: 19,
                                                _parent: Some(
                                                    18,
                                                ),
                                            },
                                        ),
                                    ),
                                ),
                            ],
                            [RANGE]
                            _id: 18,
                            _parent: Some(
                                16,
                            ),
                        },
                    ),
                    body: Block {
                        alternative: [],
                        children: [
                            CompoundStatement(
                                FunctionDefinition(
                                    FunctionDefinition {
                                        body: Block {
                                            alternative: [],
                                            children: [
                                                SimpleStatement(
                                                    ReturnStatement(
                                                        ReturnStatement {
                                                            children: None,
                                                            [RANGE]
                                                            _id: 26,
                                                            _parent: Some(
                                                                25,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ],
                                            [RANGE]
                                            _id: 25,
                                            _parent: Some(
                                                21,
                                            ),
                                        },
                                        name: Identifier {
                                            [RANGE]
                                            _id: 22,
                                            _parent: Some(
                                                21,
                                            ),
                                        },
                                        parameters: Parameters {
                                            children: [
                                                Identifier(
                                                    Identifier {
                                                        [RANGE]
                                                        _id: 24,
                                                        _parent: Some(
                                                            23,
                                                        ),
                                                    },
                                                ),
                                            ],
                                            [RANGE]
                                            _id: 23,
                                            _parent: Some(
                                                21,
                                            ),
                                        },
                                        type_parameters: None,
                                        return_type: None,
                                        [RANGE]
                                        _id: 21,
                                        _parent: Some(
                                            20,
                                        ),
                                    },
                                ),
                            ),
                        ],
                        [RANGE]
                        _id: 20,
                        _parent: Some(
                            16,
                        ),
                    },
                    name: Identifier {
                        [RANGE]
                        _id: 17,
                        _parent: Some(
                            16,
                        ),
                    },
                    [RANGE]
                    _id: 16,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        CompoundStatement(
            ClassDefinition(
                ClassDefinition {
                    type_parameters: None,
                    superclasses: Some(
                        ArgumentList {
                            children: [
                                Expression(
                                    PrimaryExpression(
                                        Identifier(
                                            Identifier {
                                                [RANGE]
                                                _id: 30,
                                                _parent: Some(
                                                    29,
                                                ),
                                            },
                                        ),
                                    ),
                                ),
                                Expression(
                                    PrimaryExpression(
                                        Subscript(
                                            Subscript {
                                                subscript: [
                                                    Expression(
                                                        PrimaryExpression(
                                                            Identifier(
                                                                Identifier {
                                                                    [RANGE]
                                                                    _id: 33,
                                                                    _parent: Some(
                                                                        31,
                                                                    ),
                                                                },
                                                            ),
                                                        ),
                                                    ),
                                                ],
                                                value: Identifier(
                                                    Identifier {
                                                        [RANGE]
                                                        _id: 32,
                                                        _parent: Some(
                                                            31,
                                                        ),
                                                    },
                                                ),
                                                [RANGE]
                                                _id: 31,
                                                _parent: Some(
                                                    29,
                                                ),
                                            },
                                        ),
                                    ),
                                ),
                            ],
                            [RANGE]
                            _id: 29,
                            _parent: Some(
                                27,
                            ),
                        },
                    ),
                    body: Block {
                        alternative: [],
                        children: [
                            SimpleStatement(
                                PassStatement(
                                    PassStatement {
                                        [RANGE]
                                        _id: 35,
                                        _parent: Some(
                                            34,
                                        ),
                                    },
                                ),
                            ),
                        ],
                        [RANGE]
                        _id: 34,
                        _parent: Some(
                            27,
                        ),
                    },
                    name: Identifier {
                        [RANGE]
                        _id: 28,
                        _parent: Some(
                            27,
                        ),
                    },
                    [RANGE]
                    _id: 27,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        CompoundStatement(
            ClassDefinition(
                ClassDefinition {
                    type_parameters: None,
                    superclasses: Some(
                        ArgumentList {
                            children: [
                                Expression(
                                    PrimaryExpression(
                                        Subscript(
                                            Subscript {
                                                subscript: [
                                                    Expression(
                                                        PrimaryExpression(
                                                            Identifier(
                                                                Identifier {
                                                                    [RANGE]
                                                                    _id: 41,
                                                                    _parent: Some(
                                                                        39,
                                                                    ),
                                                                },
                                                            ),
                                                        ),
                                                    ),
                                                    Expression(
                                                        PrimaryExpression(
                                                            Identifier(
                                                                Identifier {
                                                                    [RANGE]
                                                                    _id: 42,
                                                                    _parent: Some(
                                                                        39,
                                                                    ),
                                                                },
                                                            ),
                                                        ),
                                                    ),
                                                ],
                                                value: Identifier(
                                                    Identifier {
                                                        [RANGE]
                                                        _id: 40,
                                                        _parent: Some(
                                                            39,
                                                        ),
                                                    },
                                                ),
                                                [RANGE]
                                                _id: 39,
                                                _parent: Some(
                                                    38,
                                                ),
                                            },
                                        ),
                                    ),
                                ),
                            ],
                            [RANGE]
                            _id: 38,
                            _parent: Some(
                                36,
                            ),
                        },
                    ),
                    body: Block {
                        alternative: [],
                        children: [
                            SimpleStatement(
                                PassStatement(
                                    PassStatement {
                                        [RANGE]
                                        _id: 44,
                                        _parent: Some(
                                            43,
                                        ),
                                    },
                                ),
                            ),
                        ],
                        [RANGE]
                        _id: 43,
                        _parent: Some(
                            36,
                        ),
                    },
                    name: Identifier {
                        [RANGE]
                        _id: 37,
                        _parent: Some(
                            36,
                        ),
                    },
                    [RANGE]
                    _id: 36,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
