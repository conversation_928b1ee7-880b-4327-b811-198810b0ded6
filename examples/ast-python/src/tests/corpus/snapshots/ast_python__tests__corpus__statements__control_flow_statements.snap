---
source: src/tests/corpus/statements.rs
expression: module
---
Module {
    children: [
        CompoundStatement(
            WhileStatement(
                WhileStatement {
                    condition: PrimaryExpression(
                        Identifier(
                            Identifier {
                                [RANGE]
                                _id: 2,
                                _parent: Some(
                                    1,
                                ),
                            },
                        ),
                    ),
                    body: Block {
                        alternative: [],
                        children: [
                            SimpleStatement(
                                PassStatement(
                                    PassStatement {
                                        [RANGE]
                                        _id: 4,
                                        _parent: Some(
                                            3,
                                        ),
                                    },
                                ),
                            ),
                            SimpleStatement(
                                BreakStatement(
                                    BreakStatement {
                                        [RANGE]
                                        _id: 5,
                                        _parent: Some(
                                            3,
                                        ),
                                    },
                                ),
                            ),
                            SimpleStatement(
                                ContinueStatement(
                                    ContinueStatement {
                                        [RANGE]
                                        _id: 6,
                                        _parent: Some(
                                            3,
                                        ),
                                    },
                                ),
                            ),
                        ],
                        [RANGE]
                        _id: 3,
                        _parent: Some(
                            1,
                        ),
                    },
                    alternative: None,
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
