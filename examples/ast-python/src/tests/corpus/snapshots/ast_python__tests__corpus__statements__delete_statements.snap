---
source: src/tests/corpus/statements.rs
expression: module
---
Module {
    children: [
        SimpleStatement(
            DeleteStatement(
                DeleteStatement {
                    children: ExpressionList(
                        ExpressionList {
                            children: [
                                PrimaryExpression(
                                    Subscript(
                                        Subscript {
                                            subscript: [
                                                Expression(
                                                    PrimaryExpression(
                                                        Integer(
                                                            Integer {
                                                                [RANGE]
                                                                _id: 5,
                                                                _parent: Some(
                                                                    3,
                                                                ),
                                                            },
                                                        ),
                                                    ),
                                                ),
                                            ],
                                            value: Identifier(
                                                Identifier {
                                                    [RANGE]
                                                    _id: 4,
                                                    _parent: Some(
                                                        3,
                                                    ),
                                                },
                                            ),
                                            [RANGE]
                                            _id: 3,
                                            _parent: Some(
                                                2,
                                            ),
                                        },
                                    ),
                                ),
                                PrimaryExpression(
                                    Subscript(
                                        Subscript {
                                            subscript: [
                                                Expression(
                                                    PrimaryExpression(
                                                        Integer(
                                                            Integer {
                                                                [RANGE]
                                                                _id: 8,
                                                                _parent: Some(
                                                                    6,
                                                                ),
                                                            },
                                                        ),
                                                    ),
                                                ),
                                            ],
                                            value: Identifier(
                                                Identifier {
                                                    [RANGE]
                                                    _id: 7,
                                                    _parent: Some(
                                                        6,
                                                    ),
                                                },
                                            ),
                                            [RANGE]
                                            _id: 6,
                                            _parent: Some(
                                                2,
                                            ),
                                        },
                                    ),
                                ),
                            ],
                            [RANGE]
                            _id: 2,
                            _parent: Some(
                                1,
                            ),
                        },
                    ),
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
