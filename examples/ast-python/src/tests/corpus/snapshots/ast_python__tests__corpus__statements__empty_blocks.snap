---
source: src/tests/corpus/statements.rs
expression: module
---
Module {
    children: [
        CompoundStatement(
            FunctionDefinition(
                FunctionDefinition {
                    body: Block {
                        alternative: [],
                        children: [],
                        [RANGE]
                        _id: 6,
                        _parent: Some(
                            1,
                        ),
                    },
                    name: Identifier {
                        [RANGE]
                        _id: 2,
                        _parent: Some(
                            1,
                        ),
                    },
                    parameters: Parameters {
                        children: [
                            Identifier(
                                Identifier {
                                    [RANGE]
                                    _id: 4,
                                    _parent: Some(
                                        3,
                                    ),
                                },
                            ),
                            Identifier(
                                Identifier {
                                    [RANGE]
                                    _id: 5,
                                    _parent: Some(
                                        3,
                                    ),
                                },
                            ),
                        ],
                        [RANGE]
                        _id: 3,
                        _parent: Some(
                            1,
                        ),
                    },
                    type_parameters: None,
                    return_type: None,
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        CompoundStatement(
            IfStatement(
                IfStatement {
                    alternative: [],
                    consequence: Block {
                        alternative: [],
                        children: [
                            SimpleStatement(
                                PrintStatement(
                                    PrintStatement {
                                        argument: [
                                            PrimaryExpression(
                                                Identifier(
                                                    Identifier {
                                                        [RANGE]
                                                        _id: 11,
                                                        _parent: Some(
                                                            10,
                                                        ),
                                                    },
                                                ),
                                            ),
                                        ],
                                        children: None,
                                        [RANGE]
                                        _id: 10,
                                        _parent: Some(
                                            9,
                                        ),
                                    },
                                ),
                            ),
                            CompoundStatement(
                                WhileStatement(
                                    WhileStatement {
                                        condition: PrimaryExpression(
                                            Call(
                                                Call {
                                                    function: Identifier(
                                                        Identifier {
                                                            [RANGE]
                                                            _id: 14,
                                                            _parent: Some(
                                                                13,
                                                            ),
                                                        },
                                                    ),
                                                    arguments: ArgumentList(
                                                        ArgumentList {
                                                            children: [],
                                                            [RANGE]
                                                            _id: 15,
                                                            _parent: Some(
                                                                13,
                                                            ),
                                                        },
                                                    ),
                                                    [RANGE]
                                                    _id: 13,
                                                    _parent: Some(
                                                        12,
                                                    ),
                                                },
                                            ),
                                        ),
                                        body: Block {
                                            alternative: [],
                                            children: [],
                                            [RANGE]
                                            _id: 16,
                                            _parent: Some(
                                                12,
                                            ),
                                        },
                                        alternative: None,
                                        [RANGE]
                                        _id: 12,
                                        _parent: Some(
                                            9,
                                        ),
                                    },
                                ),
                            ),
                        ],
                        [RANGE]
                        _id: 9,
                        _parent: Some(
                            7,
                        ),
                    },
                    condition: PrimaryExpression(
                        Identifier(
                            Identifier {
                                [RANGE]
                                _id: 8,
                                _parent: Some(
                                    7,
                                ),
                            },
                        ),
                    ),
                    [RANGE]
                    _id: 7,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
