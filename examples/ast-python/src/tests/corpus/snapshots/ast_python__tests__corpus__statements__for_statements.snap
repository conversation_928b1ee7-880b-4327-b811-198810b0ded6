---
source: src/tests/corpus/statements.rs
expression: module
---
Module {
    children: [
        CompoundStatement(
            ForStatement(
                ForStatement {
                    alternative: Some(
                        ElseClause {
                            body: Block {
                                alternative: [],
                                children: [
                                    SimpleStatement(
                                        PrintStatement(
                                            PrintStatement {
                                                argument: [
                                                    PrimaryExpression(
                                                        Identifier(
                                                            Identifier {
                                                                [RANGE]
                                                                _id: 20,
                                                                _parent: Some(
                                                                    19,
                                                                ),
                                                            },
                                                        ),
                                                    ),
                                                ],
                                                children: None,
                                                [RANGE]
                                                _id: 19,
                                                _parent: Some(
                                                    18,
                                                ),
                                            },
                                        ),
                                    ),
                                ],
                                [RANGE]
                                _id: 18,
                                _parent: Some(
                                    17,
                                ),
                            },
                            [RANGE]
                            _id: 17,
                            _parent: Some(
                                1,
                            ),
                        },
                    ),
                    body: Block {
                        alternative: [],
                        children: [
                            SimpleStatement(
                                PrintStatement(
                                    PrintStatement {
                                        argument: [
                                            PrimaryExpression(
                                                Identifier(
                                                    Identifier {
                                                        [RANGE]
                                                        _id: 8,
                                                        _parent: Some(
                                                            7,
                                                        ),
                                                    },
                                                ),
                                            ),
                                        ],
                                        children: None,
                                        [RANGE]
                                        _id: 7,
                                        _parent: Some(
                                            6,
                                        ),
                                    },
                                ),
                            ),
                            CompoundStatement(
                                ForStatement(
                                    ForStatement {
                                        alternative: None,
                                        body: Block {
                                            alternative: [],
                                            children: [
                                                SimpleStatement(
                                                    PrintStatement(
                                                        PrintStatement {
                                                            argument: [
                                                                PrimaryExpression(
                                                                    Identifier(
                                                                        Identifier {
                                                                            [RANGE]
                                                                            _id: 16,
                                                                            _parent: Some(
                                                                                15,
                                                                            ),
                                                                        },
                                                                    ),
                                                                ),
                                                            ],
                                                            children: None,
                                                            [RANGE]
                                                            _id: 15,
                                                            _parent: Some(
                                                                14,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ],
                                            [RANGE]
                                            _id: 14,
                                            _parent: Some(
                                                9,
                                            ),
                                        },
                                        right: Expression(
                                            PrimaryExpression(
                                                Identifier(
                                                    Identifier {
                                                        [RANGE]
                                                        _id: 13,
                                                        _parent: Some(
                                                            9,
                                                        ),
                                                    },
                                                ),
                                            ),
                                        ),
                                        left: PatternList(
                                            PatternList {
                                                children: [
                                                    Identifier(
                                                        Identifier {
                                                            [RANGE]
                                                            _id: 11,
                                                            _parent: Some(
                                                                10,
                                                            ),
                                                        },
                                                    ),
                                                    Identifier(
                                                        Identifier {
                                                            [RANGE]
                                                            _id: 12,
                                                            _parent: Some(
                                                                10,
                                                            ),
                                                        },
                                                    ),
                                                ],
                                                [RANGE]
                                                _id: 10,
                                                _parent: Some(
                                                    9,
                                                ),
                                            },
                                        ),
                                        [RANGE]
                                        _id: 9,
                                        _parent: Some(
                                            6,
                                        ),
                                    },
                                ),
                            ),
                        ],
                        [RANGE]
                        _id: 6,
                        _parent: Some(
                            1,
                        ),
                    },
                    right: Expression(
                        PrimaryExpression(
                            Identifier(
                                Identifier {
                                    [RANGE]
                                    _id: 5,
                                    _parent: Some(
                                        1,
                                    ),
                                },
                            ),
                        ),
                    ),
                    left: PatternList(
                        PatternList {
                            children: [
                                Identifier(
                                    Identifier {
                                        [RANGE]
                                        _id: 3,
                                        _parent: Some(
                                            2,
                                        ),
                                    },
                                ),
                                Identifier(
                                    Identifier {
                                        [RANGE]
                                        _id: 4,
                                        _parent: Some(
                                            2,
                                        ),
                                    },
                                ),
                            ],
                            [RANGE]
                            _id: 2,
                            _parent: Some(
                                1,
                            ),
                        },
                    ),
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        CompoundStatement(
            ForStatement(
                ForStatement {
                    alternative: None,
                    body: Block {
                        alternative: [],
                        children: [
                            SimpleStatement(
                                ExpressionStatement(
                                    ExpressionStatement {
                                        children: [
                                            Expression(
                                                PrimaryExpression(
                                                    Identifier(
                                                        Identifier {
                                                            [RANGE]
                                                            _id: 33,
                                                            _parent: Some(
                                                                32,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 32,
                                        _parent: Some(
                                            31,
                                        ),
                                    },
                                ),
                            ),
                        ],
                        [RANGE]
                        _id: 31,
                        _parent: Some(
                            21,
                        ),
                    },
                    right: Expression(
                        PrimaryExpression(
                            List(
                                List {
                                    children: [
                                        Expression(
                                            PrimaryExpression(
                                                Tuple(
                                                    Tuple {
                                                        children: [
                                                            Expression(
                                                                PrimaryExpression(
                                                                    Integer(
                                                                        Integer {
                                                                            [RANGE]
                                                                            _id: 26,
                                                                            _parent: Some(
                                                                                25,
                                                                            ),
                                                                        },
                                                                    ),
                                                                ),
                                                            ),
                                                        ],
                                                        [RANGE]
                                                        _id: 25,
                                                        _parent: Some(
                                                            24,
                                                        ),
                                                    },
                                                ),
                                            ),
                                        ),
                                        Expression(
                                            PrimaryExpression(
                                                Tuple(
                                                    Tuple {
                                                        children: [
                                                            Expression(
                                                                PrimaryExpression(
                                                                    Integer(
                                                                        Integer {
                                                                            [RANGE]
                                                                            _id: 28,
                                                                            _parent: Some(
                                                                                27,
                                                                            ),
                                                                        },
                                                                    ),
                                                                ),
                                                            ),
                                                        ],
                                                        [RANGE]
                                                        _id: 27,
                                                        _parent: Some(
                                                            24,
                                                        ),
                                                    },
                                                ),
                                            ),
                                        ),
                                        Expression(
                                            PrimaryExpression(
                                                Tuple(
                                                    Tuple {
                                                        children: [
                                                            Expression(
                                                                PrimaryExpression(
                                                                    Integer(
                                                                        Integer {
                                                                            [RANGE]
                                                                            _id: 30,
                                                                            _parent: Some(
                                                                                29,
                                                                            ),
                                                                        },
                                                                    ),
                                                                ),
                                                            ),
                                                        ],
                                                        [RANGE]
                                                        _id: 29,
                                                        _parent: Some(
                                                            24,
                                                        ),
                                                    },
                                                ),
                                            ),
                                        ),
                                    ],
                                    [RANGE]
                                    _id: 24,
                                    _parent: Some(
                                        21,
                                    ),
                                },
                            ),
                        ),
                    ),
                    left: PatternList(
                        PatternList {
                            children: [
                                Identifier(
                                    Identifier {
                                        [RANGE]
                                        _id: 23,
                                        _parent: Some(
                                            22,
                                        ),
                                    },
                                ),
                            ],
                            [RANGE]
                            _id: 22,
                            _parent: Some(
                                21,
                            ),
                        },
                    ),
                    [RANGE]
                    _id: 21,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
