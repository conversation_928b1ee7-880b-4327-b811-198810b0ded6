---
source: src/tests/corpus/statements.rs
expression: module
---
Module {
    children: [
        CompoundStatement(
            IfStatement(
                IfStatement {
                    alternative: [],
                    consequence: Block {
                        alternative: [],
                        children: [
                            CompoundStatement(
                                IfStatement(
                                    IfStatement {
                                        alternative: [
                                            ElseClause(
                                                ElseClause {
                                                    body: Block {
                                                        alternative: [],
                                                        children: [
                                                            CompoundStatement(
                                                                IfStatement(
                                                                    IfStatement {
                                                                        alternative: [],
                                                                        consequence: Block {
                                                                            alternative: [],
                                                                            children: [
                                                                                SimpleStatement(
                                                                                    ExpressionStatement(
                                                                                        ExpressionStatement {
                                                                                            children: [
                                                                                                Expression(
                                                                                                    PrimaryExpression(
                                                                                                        Identifier(
                                                                                                            Identifier {
                                                                                                                [RANGE]
                                                                                                                _id: 15,
                                                                                                                _parent: Some(
                                                                                                                    14,
                                                                                                                ),
                                                                                                            },
                                                                                                        ),
                                                                                                    ),
                                                                                                ),
                                                                                            ],
                                                                                            [RANGE]
                                                                                            _id: 14,
                                                                                            _parent: Some(
                                                                                                13,
                                                                                            ),
                                                                                        },
                                                                                    ),
                                                                                ),
                                                                            ],
                                                                            [RANGE]
                                                                            _id: 13,
                                                                            _parent: Some(
                                                                                11,
                                                                            ),
                                                                        },
                                                                        condition: PrimaryExpression(
                                                                            Identifier(
                                                                                Identifier {
                                                                                    [RANGE]
                                                                                    _id: 12,
                                                                                    _parent: Some(
                                                                                        11,
                                                                                    ),
                                                                                },
                                                                            ),
                                                                        ),
                                                                        [RANGE]
                                                                        _id: 11,
                                                                        _parent: Some(
                                                                            10,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ],
                                                        [RANGE]
                                                        _id: 10,
                                                        _parent: Some(
                                                            9,
                                                        ),
                                                    },
                                                    [RANGE]
                                                    _id: 9,
                                                    _parent: Some(
                                                        4,
                                                    ),
                                                },
                                            ),
                                        ],
                                        consequence: Block {
                                            alternative: [],
                                            children: [
                                                SimpleStatement(
                                                    ExpressionStatement(
                                                        ExpressionStatement {
                                                            children: [
                                                                Expression(
                                                                    PrimaryExpression(
                                                                        Identifier(
                                                                            Identifier {
                                                                                [RANGE]
                                                                                _id: 8,
                                                                                _parent: Some(
                                                                                    7,
                                                                                ),
                                                                            },
                                                                        ),
                                                                    ),
                                                                ),
                                                            ],
                                                            [RANGE]
                                                            _id: 7,
                                                            _parent: Some(
                                                                6,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ],
                                            [RANGE]
                                            _id: 6,
                                            _parent: Some(
                                                4,
                                            ),
                                        },
                                        condition: PrimaryExpression(
                                            Identifier(
                                                Identifier {
                                                    [RANGE]
                                                    _id: 5,
                                                    _parent: Some(
                                                        4,
                                                    ),
                                                },
                                            ),
                                        ),
                                        [RANGE]
                                        _id: 4,
                                        _parent: Some(
                                            3,
                                        ),
                                    },
                                ),
                            ),
                        ],
                        [RANGE]
                        _id: 3,
                        _parent: Some(
                            1,
                        ),
                    },
                    condition: PrimaryExpression(
                        Identifier(
                            Identifier {
                                [RANGE]
                                _id: 2,
                                _parent: Some(
                                    1,
                                ),
                            },
                        ),
                    ),
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            ExpressionStatement(
                ExpressionStatement {
                    children: [
                        Expression(
                            PrimaryExpression(
                                Identifier(
                                    Identifier {
                                        [RANGE]
                                        _id: 17,
                                        _parent: Some(
                                            16,
                                        ),
                                    },
                                ),
                            ),
                        ),
                    ],
                    [RANGE]
                    _id: 16,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
