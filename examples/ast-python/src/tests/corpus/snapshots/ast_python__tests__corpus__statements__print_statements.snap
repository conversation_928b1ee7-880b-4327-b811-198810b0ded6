---
source: src/tests/corpus/statements.rs
expression: module
---
Module {
    children: [
        SimpleStatement(
            PrintStatement(
                PrintStatement {
                    argument: [
                        PrimaryExpression(
                            Identifier(
                                Identifier {
                                    [RANGE]
                                    _id: 2,
                                    _parent: Some(
                                        1,
                                    ),
                                },
                            ),
                        ),
                    ],
                    children: None,
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            PrintStatement(
                PrintStatement {
                    argument: [
                        PrimaryExpression(
                            Identifier(
                                Identifier {
                                    [RANGE]
                                    _id: 4,
                                    _parent: Some(
                                        3,
                                    ),
                                },
                            ),
                        ),
                        PrimaryExpression(
                            Identifier(
                                Identifier {
                                    [RANGE]
                                    _id: 5,
                                    _parent: Some(
                                        3,
                                    ),
                                },
                            ),
                        ),
                    ],
                    children: None,
                    [RANGE]
                    _id: 3,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            PrintStatement(
                PrintStatement {
                    argument: [
                        BooleanOperator(
                            BooleanOperator {
                                operator: Token_Or(
                                    Token_Or {
                                        [RANGE]
                                        _id: 9,
                                        _parent: Some(
                                            7,
                                        ),
                                    },
                                ),
                                right: PrimaryExpression(
                                    Integer(
                                        Integer {
                                            [RANGE]
                                            _id: 10,
                                            _parent: Some(
                                                7,
                                            ),
                                        },
                                    ),
                                ),
                                left: PrimaryExpression(
                                    Integer(
                                        Integer {
                                            [RANGE]
                                            _id: 8,
                                            _parent: Some(
                                                7,
                                            ),
                                        },
                                    ),
                                ),
                                [RANGE]
                                _id: 7,
                                _parent: Some(
                                    6,
                                ),
                            },
                        ),
                        BooleanOperator(
                            BooleanOperator {
                                operator: Token_Or(
                                    Token_Or {
                                        [RANGE]
                                        _id: 13,
                                        _parent: Some(
                                            11,
                                        ),
                                    },
                                ),
                                right: PrimaryExpression(
                                    Integer(
                                        Integer {
                                            [RANGE]
                                            _id: 14,
                                            _parent: Some(
                                                11,
                                            ),
                                        },
                                    ),
                                ),
                                left: PrimaryExpression(
                                    Integer(
                                        Integer {
                                            [RANGE]
                                            _id: 12,
                                            _parent: Some(
                                                11,
                                            ),
                                        },
                                    ),
                                ),
                                [RANGE]
                                _id: 11,
                                _parent: Some(
                                    6,
                                ),
                            },
                        ),
                    ],
                    children: None,
                    [RANGE]
                    _id: 6,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            PrintStatement(
                PrintStatement {
                    argument: [
                        BooleanOperator(
                            BooleanOperator {
                                operator: Token_Or(
                                    Token_Or {
                                        [RANGE]
                                        _id: 18,
                                        _parent: Some(
                                            16,
                                        ),
                                    },
                                ),
                                right: PrimaryExpression(
                                    Integer(
                                        Integer {
                                            [RANGE]
                                            _id: 19,
                                            _parent: Some(
                                                16,
                                            ),
                                        },
                                    ),
                                ),
                                left: PrimaryExpression(
                                    Integer(
                                        Integer {
                                            [RANGE]
                                            _id: 17,
                                            _parent: Some(
                                                16,
                                            ),
                                        },
                                    ),
                                ),
                                [RANGE]
                                _id: 16,
                                _parent: Some(
                                    15,
                                ),
                            },
                        ),
                    ],
                    children: None,
                    [RANGE]
                    _id: 15,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        SimpleStatement(
            PrintStatement(
                PrintStatement {
                    argument: [
                        NotOperator(
                            NotOperator {
                                argument: PrimaryExpression(
                                    True(
                                        True {
                                            [RANGE]
                                            _id: 22,
                                            _parent: Some(
                                                21,
                                            ),
                                        },
                                    ),
                                ),
                                [RANGE]
                                _id: 21,
                                _parent: Some(
                                    20,
                                ),
                            },
                        ),
                    ],
                    children: None,
                    [RANGE]
                    _id: 20,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
