---
source: src/tests/corpus/statements.rs
expression: module
---
Module {
    children: [
        CompoundStatement(
            TryStatement(
                TryStatement {
                    body: Block {
                        alternative: [],
                        children: [
                            SimpleStatement(
                                ExpressionStatement(
                                    ExpressionStatement {
                                        children: [
                                            Expression(
                                                PrimaryExpression(
                                                    Identifier(
                                                        Identifier {
                                                            [RANGE]
                                                            _id: 4,
                                                            _parent: Some(
                                                                3,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 3,
                                        _parent: Some(
                                            2,
                                        ),
                                    },
                                ),
                            ),
                        ],
                        [RANGE]
                        _id: 2,
                        _parent: Some(
                            1,
                        ),
                    },
                    children: [
                        ExceptClause(
                            ExceptClause {
                                value: Some(
                                    PrimaryExpression(
                                        Identifier(
                                            Identifier {
                                                [RANGE]
                                                _id: 6,
                                                _parent: Some(
                                                    5,
                                                ),
                                            },
                                        ),
                                    ),
                                ),
                                alias: None,
                                children: Block {
                                    alternative: [],
                                    children: [
                                        SimpleStatement(
                                            ExpressionStatement(
                                                ExpressionStatement {
                                                    children: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 9,
                                                                        _parent: Some(
                                                                            8,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 8,
                                                    _parent: Some(
                                                        7,
                                                    ),
                                                },
                                            ),
                                        ),
                                    ],
                                    [RANGE]
                                    _id: 7,
                                    _parent: Some(
                                        5,
                                    ),
                                },
                                [RANGE]
                                _id: 5,
                                _parent: Some(
                                    1,
                                ),
                            },
                        ),
                        ExceptClause(
                            ExceptClause {
                                value: Some(
                                    AsPattern(
                                        AsPattern {
                                            alias: Some(
                                                AsPatternTarget {
                                                    [RANGE]
                                                    _id: 13,
                                                    _parent: Some(
                                                        11,
                                                    ),
                                                },
                                            ),
                                            children: [
                                                Identifier(
                                                    Identifier {
                                                        [RANGE]
                                                        _id: 12,
                                                        _parent: Some(
                                                            11,
                                                        ),
                                                    },
                                                ),
                                            ],
                                            [RANGE]
                                            _id: 11,
                                            _parent: Some(
                                                10,
                                            ),
                                        },
                                    ),
                                ),
                                alias: None,
                                children: Block {
                                    alternative: [],
                                    children: [
                                        SimpleStatement(
                                            ExpressionStatement(
                                                ExpressionStatement {
                                                    children: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 16,
                                                                        _parent: Some(
                                                                            15,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 15,
                                                    _parent: Some(
                                                        14,
                                                    ),
                                                },
                                            ),
                                        ),
                                    ],
                                    [RANGE]
                                    _id: 14,
                                    _parent: Some(
                                        10,
                                    ),
                                },
                                [RANGE]
                                _id: 10,
                                _parent: Some(
                                    1,
                                ),
                            },
                        ),
                        ExceptClause(
                            ExceptClause {
                                value: Some(
                                    PrimaryExpression(
                                        Identifier(
                                            Identifier {
                                                [RANGE]
                                                _id: 18,
                                                _parent: Some(
                                                    17,
                                                ),
                                            },
                                        ),
                                    ),
                                ),
                                alias: Some(
                                    PrimaryExpression(
                                        Identifier(
                                            Identifier {
                                                [RANGE]
                                                _id: 19,
                                                _parent: Some(
                                                    17,
                                                ),
                                            },
                                        ),
                                    ),
                                ),
                                children: Block {
                                    alternative: [],
                                    children: [
                                        SimpleStatement(
                                            ExpressionStatement(
                                                ExpressionStatement {
                                                    children: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 22,
                                                                        _parent: Some(
                                                                            21,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 21,
                                                    _parent: Some(
                                                        20,
                                                    ),
                                                },
                                            ),
                                        ),
                                    ],
                                    [RANGE]
                                    _id: 20,
                                    _parent: Some(
                                        17,
                                    ),
                                },
                                [RANGE]
                                _id: 17,
                                _parent: Some(
                                    1,
                                ),
                            },
                        ),
                        ExceptClause(
                            ExceptClause {
                                value: None,
                                alias: None,
                                children: Block {
                                    alternative: [],
                                    children: [
                                        SimpleStatement(
                                            ExpressionStatement(
                                                ExpressionStatement {
                                                    children: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 26,
                                                                        _parent: Some(
                                                                            25,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 25,
                                                    _parent: Some(
                                                        24,
                                                    ),
                                                },
                                            ),
                                        ),
                                    ],
                                    [RANGE]
                                    _id: 24,
                                    _parent: Some(
                                        23,
                                    ),
                                },
                                [RANGE]
                                _id: 23,
                                _parent: Some(
                                    1,
                                ),
                            },
                        ),
                    ],
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        CompoundStatement(
            TryStatement(
                TryStatement {
                    body: Block {
                        alternative: [],
                        children: [
                            SimpleStatement(
                                ExpressionStatement(
                                    ExpressionStatement {
                                        children: [
                                            Expression(
                                                PrimaryExpression(
                                                    Identifier(
                                                        Identifier {
                                                            [RANGE]
                                                            _id: 30,
                                                            _parent: Some(
                                                                29,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 29,
                                        _parent: Some(
                                            28,
                                        ),
                                    },
                                ),
                            ),
                        ],
                        [RANGE]
                        _id: 28,
                        _parent: Some(
                            27,
                        ),
                    },
                    children: [
                        ExceptClause(
                            ExceptClause {
                                value: Some(
                                    PrimaryExpression(
                                        Identifier(
                                            Identifier {
                                                [RANGE]
                                                _id: 32,
                                                _parent: Some(
                                                    31,
                                                ),
                                            },
                                        ),
                                    ),
                                ),
                                alias: None,
                                children: Block {
                                    alternative: [],
                                    children: [
                                        SimpleStatement(
                                            ExpressionStatement(
                                                ExpressionStatement {
                                                    children: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 35,
                                                                        _parent: Some(
                                                                            34,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 34,
                                                    _parent: Some(
                                                        33,
                                                    ),
                                                },
                                            ),
                                        ),
                                        SimpleStatement(
                                            ExpressionStatement(
                                                ExpressionStatement {
                                                    children: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 37,
                                                                        _parent: Some(
                                                                            36,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 36,
                                                    _parent: Some(
                                                        33,
                                                    ),
                                                },
                                            ),
                                        ),
                                    ],
                                    [RANGE]
                                    _id: 33,
                                    _parent: Some(
                                        31,
                                    ),
                                },
                                [RANGE]
                                _id: 31,
                                _parent: Some(
                                    27,
                                ),
                            },
                        ),
                        ElseClause(
                            ElseClause {
                                body: Block {
                                    alternative: [],
                                    children: [
                                        SimpleStatement(
                                            ExpressionStatement(
                                                ExpressionStatement {
                                                    children: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 41,
                                                                        _parent: Some(
                                                                            40,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 40,
                                                    _parent: Some(
                                                        39,
                                                    ),
                                                },
                                            ),
                                        ),
                                    ],
                                    [RANGE]
                                    _id: 39,
                                    _parent: Some(
                                        38,
                                    ),
                                },
                                [RANGE]
                                _id: 38,
                                _parent: Some(
                                    27,
                                ),
                            },
                        ),
                        FinallyClause(
                            FinallyClause {
                                children: Block {
                                    alternative: [],
                                    children: [
                                        SimpleStatement(
                                            ExpressionStatement(
                                                ExpressionStatement {
                                                    children: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 45,
                                                                        _parent: Some(
                                                                            44,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 44,
                                                    _parent: Some(
                                                        43,
                                                    ),
                                                },
                                            ),
                                        ),
                                    ],
                                    [RANGE]
                                    _id: 43,
                                    _parent: Some(
                                        42,
                                    ),
                                },
                                [RANGE]
                                _id: 42,
                                _parent: Some(
                                    27,
                                ),
                            },
                        ),
                    ],
                    [RANGE]
                    _id: 27,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        CompoundStatement(
            TryStatement(
                TryStatement {
                    body: Block {
                        alternative: [],
                        children: [
                            SimpleStatement(
                                ExpressionStatement(
                                    ExpressionStatement {
                                        children: [
                                            Expression(
                                                PrimaryExpression(
                                                    Identifier(
                                                        Identifier {
                                                            [RANGE]
                                                            _id: 49,
                                                            _parent: Some(
                                                                48,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 48,
                                        _parent: Some(
                                            47,
                                        ),
                                    },
                                ),
                            ),
                        ],
                        [RANGE]
                        _id: 47,
                        _parent: Some(
                            46,
                        ),
                    },
                    children: [
                        ExceptGroupClause(
                            ExceptGroupClause {
                                children: [
                                    Expression(
                                        PrimaryExpression(
                                            Identifier(
                                                Identifier {
                                                    [RANGE]
                                                    _id: 51,
                                                    _parent: Some(
                                                        50,
                                                    ),
                                                },
                                            ),
                                        ),
                                    ),
                                    Block(
                                        Block {
                                            alternative: [],
                                            children: [
                                                SimpleStatement(
                                                    ExpressionStatement(
                                                        ExpressionStatement {
                                                            children: [
                                                                Expression(
                                                                    PrimaryExpression(
                                                                        Identifier(
                                                                            Identifier {
                                                                                [RANGE]
                                                                                _id: 54,
                                                                                _parent: Some(
                                                                                    53,
                                                                                ),
                                                                            },
                                                                        ),
                                                                    ),
                                                                ),
                                                            ],
                                                            [RANGE]
                                                            _id: 53,
                                                            _parent: Some(
                                                                52,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ],
                                            [RANGE]
                                            _id: 52,
                                            _parent: Some(
                                                50,
                                            ),
                                        },
                                    ),
                                ],
                                [RANGE]
                                _id: 50,
                                _parent: Some(
                                    46,
                                ),
                            },
                        ),
                        ExceptGroupClause(
                            ExceptGroupClause {
                                children: [
                                    Expression(
                                        AsPattern(
                                            AsPattern {
                                                alias: Some(
                                                    AsPatternTarget {
                                                        [RANGE]
                                                        _id: 58,
                                                        _parent: Some(
                                                            56,
                                                        ),
                                                    },
                                                ),
                                                children: [
                                                    Identifier(
                                                        Identifier {
                                                            [RANGE]
                                                            _id: 57,
                                                            _parent: Some(
                                                                56,
                                                            ),
                                                        },
                                                    ),
                                                ],
                                                [RANGE]
                                                _id: 56,
                                                _parent: Some(
                                                    55,
                                                ),
                                            },
                                        ),
                                    ),
                                    Block(
                                        Block {
                                            alternative: [],
                                            children: [
                                                SimpleStatement(
                                                    ExpressionStatement(
                                                        ExpressionStatement {
                                                            children: [
                                                                Expression(
                                                                    PrimaryExpression(
                                                                        Identifier(
                                                                            Identifier {
                                                                                [RANGE]
                                                                                _id: 61,
                                                                                _parent: Some(
                                                                                    60,
                                                                                ),
                                                                            },
                                                                        ),
                                                                    ),
                                                                ),
                                                            ],
                                                            [RANGE]
                                                            _id: 60,
                                                            _parent: Some(
                                                                59,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ],
                                            [RANGE]
                                            _id: 59,
                                            _parent: Some(
                                                55,
                                            ),
                                        },
                                    ),
                                ],
                                [RANGE]
                                _id: 55,
                                _parent: Some(
                                    46,
                                ),
                            },
                        ),
                        ElseClause(
                            ElseClause {
                                body: Block {
                                    alternative: [],
                                    children: [
                                        SimpleStatement(
                                            ExpressionStatement(
                                                ExpressionStatement {
                                                    children: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 65,
                                                                        _parent: Some(
                                                                            64,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 64,
                                                    _parent: Some(
                                                        63,
                                                    ),
                                                },
                                            ),
                                        ),
                                    ],
                                    [RANGE]
                                    _id: 63,
                                    _parent: Some(
                                        62,
                                    ),
                                },
                                [RANGE]
                                _id: 62,
                                _parent: Some(
                                    46,
                                ),
                            },
                        ),
                        FinallyClause(
                            FinallyClause {
                                children: Block {
                                    alternative: [],
                                    children: [
                                        SimpleStatement(
                                            ExpressionStatement(
                                                ExpressionStatement {
                                                    children: [
                                                        Expression(
                                                            PrimaryExpression(
                                                                Identifier(
                                                                    Identifier {
                                                                        [RANGE]
                                                                        _id: 69,
                                                                        _parent: Some(
                                                                            68,
                                                                        ),
                                                                    },
                                                                ),
                                                            ),
                                                        ),
                                                    ],
                                                    [RANGE]
                                                    _id: 68,
                                                    _parent: Some(
                                                        67,
                                                    ),
                                                },
                                            ),
                                        ),
                                    ],
                                    [RANGE]
                                    _id: 67,
                                    _parent: Some(
                                        66,
                                    ),
                                },
                                [RANGE]
                                _id: 66,
                                _parent: Some(
                                    46,
                                ),
                            },
                        ),
                    ],
                    [RANGE]
                    _id: 46,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
