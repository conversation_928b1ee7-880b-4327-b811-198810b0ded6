---
source: src/tests/corpus/statements.rs
expression: module
---
Module {
    children: [
        CompoundStatement(
            WithStatement(
                WithStatement {
                    body: Block {
                        alternative: [],
                        children: [
                            SimpleStatement(
                                ExpressionStatement(
                                    ExpressionStatement {
                                        children: [
                                            Expression(
                                                PrimaryExpression(
                                                    Identifier(
                                                        Identifier {
                                                            [RANGE]
                                                            _id: 9,
                                                            _parent: Some(
                                                                8,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 8,
                                        _parent: Some(
                                            7,
                                        ),
                                    },
                                ),
                            ),
                        ],
                        [RANGE]
                        _id: 7,
                        _parent: Some(
                            1,
                        ),
                    },
                    children: WithClause {
                        children: [
                            WithItem {
                                value: AsPattern(
                                    AsPattern {
                                        alias: Some(
                                            AsPatternTarget {
                                                [RANGE]
                                                _id: 6,
                                                _parent: Some(
                                                    4,
                                                ),
                                            },
                                        ),
                                        children: [
                                            Identifier(
                                                Identifier {
                                                    [RANGE]
                                                    _id: 5,
                                                    _parent: Some(
                                                        4,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 4,
                                        _parent: Some(
                                            3,
                                        ),
                                    },
                                ),
                                [RANGE]
                                _id: 3,
                                _parent: Some(
                                    2,
                                ),
                            },
                        ],
                        [RANGE]
                        _id: 2,
                        _parent: Some(
                            1,
                        ),
                    },
                    [RANGE]
                    _id: 1,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        CompoundStatement(
            WithStatement(
                WithStatement {
                    body: Block {
                        alternative: [],
                        children: [
                            SimpleStatement(
                                ExpressionStatement(
                                    ExpressionStatement {
                                        children: [
                                            Expression(
                                                PrimaryExpression(
                                                    Identifier(
                                                        Identifier {
                                                            [RANGE]
                                                            _id: 34,
                                                            _parent: Some(
                                                                33,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 33,
                                        _parent: Some(
                                            32,
                                        ),
                                    },
                                ),
                            ),
                        ],
                        [RANGE]
                        _id: 32,
                        _parent: Some(
                            10,
                        ),
                    },
                    children: WithClause {
                        children: [
                            WithItem {
                                value: AsPattern(
                                    AsPattern {
                                        alias: Some(
                                            AsPatternTarget {
                                                [RANGE]
                                                _id: 21,
                                                _parent: Some(
                                                    13,
                                                ),
                                            },
                                        ),
                                        children: [
                                            Expression(
                                                PrimaryExpression(
                                                    Call(
                                                        Call {
                                                            function: Identifier(
                                                                Identifier {
                                                                    [RANGE]
                                                                    _id: 15,
                                                                    _parent: Some(
                                                                        14,
                                                                    ),
                                                                },
                                                            ),
                                                            arguments: ArgumentList(
                                                                ArgumentList {
                                                                    children: [
                                                                        Expression(
                                                                            PrimaryExpression(
                                                                                String(
                                                                                    String {
                                                                                        children: [
                                                                                            StringStart(
                                                                                                StringStart {
                                                                                                    [RANGE]
                                                                                                    _id: 18,
                                                                                                    _parent: Some(
                                                                                                        17,
                                                                                                    ),
                                                                                                },
                                                                                            ),
                                                                                            StringContent(
                                                                                                StringContent {
                                                                                                    children: [],
                                                                                                    [RANGE]
                                                                                                    _id: 19,
                                                                                                    _parent: Some(
                                                                                                        17,
                                                                                                    ),
                                                                                                },
                                                                                            ),
                                                                                            StringEnd(
                                                                                                StringEnd {
                                                                                                    [RANGE]
                                                                                                    _id: 20,
                                                                                                    _parent: Some(
                                                                                                        17,
                                                                                                    ),
                                                                                                },
                                                                                            ),
                                                                                        ],
                                                                                        [RANGE]
                                                                                        _id: 17,
                                                                                        _parent: Some(
                                                                                            16,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                            ),
                                                                        ),
                                                                    ],
                                                                    [RANGE]
                                                                    _id: 16,
                                                                    _parent: Some(
                                                                        14,
                                                                    ),
                                                                },
                                                            ),
                                                            [RANGE]
                                                            _id: 14,
                                                            _parent: Some(
                                                                13,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 13,
                                        _parent: Some(
                                            12,
                                        ),
                                    },
                                ),
                                [RANGE]
                                _id: 12,
                                _parent: Some(
                                    11,
                                ),
                            },
                            WithItem {
                                value: AsPattern(
                                    AsPattern {
                                        alias: Some(
                                            AsPatternTarget {
                                                [RANGE]
                                                _id: 31,
                                                _parent: Some(
                                                    23,
                                                ),
                                            },
                                        ),
                                        children: [
                                            Expression(
                                                PrimaryExpression(
                                                    Call(
                                                        Call {
                                                            function: Identifier(
                                                                Identifier {
                                                                    [RANGE]
                                                                    _id: 25,
                                                                    _parent: Some(
                                                                        24,
                                                                    ),
                                                                },
                                                            ),
                                                            arguments: ArgumentList(
                                                                ArgumentList {
                                                                    children: [
                                                                        Expression(
                                                                            PrimaryExpression(
                                                                                String(
                                                                                    String {
                                                                                        children: [
                                                                                            StringStart(
                                                                                                StringStart {
                                                                                                    [RANGE]
                                                                                                    _id: 28,
                                                                                                    _parent: Some(
                                                                                                        27,
                                                                                                    ),
                                                                                                },
                                                                                            ),
                                                                                            StringContent(
                                                                                                StringContent {
                                                                                                    children: [],
                                                                                                    [RANGE]
                                                                                                    _id: 29,
                                                                                                    _parent: Some(
                                                                                                        27,
                                                                                                    ),
                                                                                                },
                                                                                            ),
                                                                                            StringEnd(
                                                                                                StringEnd {
                                                                                                    [RANGE]
                                                                                                    _id: 30,
                                                                                                    _parent: Some(
                                                                                                        27,
                                                                                                    ),
                                                                                                },
                                                                                            ),
                                                                                        ],
                                                                                        [RANGE]
                                                                                        _id: 27,
                                                                                        _parent: Some(
                                                                                            26,
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                            ),
                                                                        ),
                                                                    ],
                                                                    [RANGE]
                                                                    _id: 26,
                                                                    _parent: Some(
                                                                        24,
                                                                    ),
                                                                },
                                                            ),
                                                            [RANGE]
                                                            _id: 24,
                                                            _parent: Some(
                                                                23,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 23,
                                        _parent: Some(
                                            22,
                                        ),
                                    },
                                ),
                                [RANGE]
                                _id: 22,
                                _parent: Some(
                                    11,
                                ),
                            },
                        ],
                        [RANGE]
                        _id: 11,
                        _parent: Some(
                            10,
                        ),
                    },
                    [RANGE]
                    _id: 10,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
        CompoundStatement(
            WithStatement(
                WithStatement {
                    body: Block {
                        alternative: [],
                        children: [
                            SimpleStatement(
                                ExpressionStatement(
                                    ExpressionStatement {
                                        children: [
                                            Expression(
                                                PrimaryExpression(
                                                    Identifier(
                                                        Identifier {
                                                            [RANGE]
                                                            _id: 47,
                                                            _parent: Some(
                                                                46,
                                                            ),
                                                        },
                                                    ),
                                                ),
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 46,
                                        _parent: Some(
                                            45,
                                        ),
                                    },
                                ),
                            ),
                        ],
                        [RANGE]
                        _id: 45,
                        _parent: Some(
                            35,
                        ),
                    },
                    children: WithClause {
                        children: [
                            WithItem {
                                value: AsPattern(
                                    AsPattern {
                                        alias: Some(
                                            AsPatternTarget {
                                                [RANGE]
                                                _id: 40,
                                                _parent: Some(
                                                    38,
                                                ),
                                            },
                                        ),
                                        children: [
                                            Identifier(
                                                Identifier {
                                                    [RANGE]
                                                    _id: 39,
                                                    _parent: Some(
                                                        38,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 38,
                                        _parent: Some(
                                            37,
                                        ),
                                    },
                                ),
                                [RANGE]
                                _id: 37,
                                _parent: Some(
                                    36,
                                ),
                            },
                            WithItem {
                                value: AsPattern(
                                    AsPattern {
                                        alias: Some(
                                            AsPatternTarget {
                                                [RANGE]
                                                _id: 44,
                                                _parent: Some(
                                                    42,
                                                ),
                                            },
                                        ),
                                        children: [
                                            Identifier(
                                                Identifier {
                                                    [RANGE]
                                                    _id: 43,
                                                    _parent: Some(
                                                        42,
                                                    ),
                                                },
                                            ),
                                        ],
                                        [RANGE]
                                        _id: 42,
                                        _parent: Some(
                                            41,
                                        ),
                                    },
                                ),
                                [RANGE]
                                _id: 41,
                                _parent: Some(
                                    36,
                                ),
                            },
                        ],
                        [RANGE]
                        _id: 36,
                        _parent: Some(
                            35,
                        ),
                    },
                    [RANGE]
                    _id: 35,
                    _parent: Some(
                        0,
                    ),
                },
            ),
        ),
    ],
    [RANGE]
    _id: 0,
    _parent: None,
}
