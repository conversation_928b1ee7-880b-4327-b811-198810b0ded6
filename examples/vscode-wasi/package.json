{"name": "vscode-wasi", "description": ".", "author": "<PERSON><PERSON>", "license": "MIT", "version": "1.0.0", "publisher": "adclz", "categories": [], "keywords": [], "engines": {"vscode": "^1.88.0"}, "main": "./client/out/extension", "browser": "./client/dist/web/extension", "activationEvents": ["*"], "extensionDependencies": ["ms-vscode.wasm-wasi-core"], "contributes": {"languages": [{"id": "python", "aliases": ["Python", "P<PERSON>"], "extensions": [".py"]}], "grammars": [{"language": "python", "scopeName": "source.python", "path": "./syntaxes/MagicPython.tmLanguage.json"}, {"scopeName": "source.regexp.python", "path": "./syntaxes/MagicRegExp.tmLanguage.json"}]}, "dependencies": {}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^7.14.0", "@typescript-eslint/parser": "^7.14.0", "@types/node": "^20", "eslint": "^8.57.0", "typescript": "^5.6.2", "esbuild": "^0.21.3", "serve": "^14.2.3"}, "scripts": {"postinstall": "cd client && npm install && cd ..", "vscode:prepublish": "npm run build", "build": "cd client && npm run compile && cd ../server && npm run build && cd ..", "lint": "cd client && npm run lint && cd ..", "esbuild": "node ./bin/esbuild.js", "serve": "serve --cors -l 5000 --ssl-cert $HOME/certs/localhost.pem --ssl-key $HOME/certs/localhost-key.pem"}}