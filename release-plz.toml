[workspace]
git_release_enable = true
pr_labels = ["release"]
semver_check = true

[[package]]
name = "auto-lsp"

[[package]]
name = "auto-lsp-core"

[[package]]
name = "auto-lsp-codegen"

[changelog]
protect_breaking_commits = true
header = """# Changelog

## [Unreleased]
"""

body = """

{% macro print_commit(commit) -%}
    - {% if commit.scope %}*({{ commit.scope }})* {% endif %}\
      {% if commit.breaking %}[**breaking**] {% endif %}\
      {{ commit.message | upper_first }} - \
      ([{{ commit.id | truncate(length=7, end="") }}]({{ remote.link }}/commit/{{ commit.id }}))\
{% endmacro -%}

{% if version %}\
    {% if previous.version %}\
        ## [{{ version | trim_start_matches(pat="v") }}]({{ release_link }})
    {% else %}\
        ## [{{ version | trim_start_matches(pat="v") }}]
    {% endif %}\
{% endif %}\

{% for group, commits in commits
| filter(attribute="merge_commit", value=false)
| unique(attribute="message")
| group_by(attribute="group") %}
    ### {{ group | striptags | trim | upper_first }}
    {% for commit in commits
    | filter(attribute="scope")
    | sort(attribute="scope") %}
        {{ self::print_commit(commit=commit) }}
    {%- endfor -%}
    {% raw %}\n{% endraw %}\
    {%- for commit in commits %}
        {%- if not commit.scope -%}
            {{ self::print_commit(commit=commit) }}
        {% endif -%}
    {% endfor -%}
{% endfor %}\n
"""

commit_parsers = [
    { message = "^feat", group = "<!-- 0 -->Features" },
    { message = "^fix", group = "<!-- 1 -->Bug Fixes" },
    { message = "^doc", group = "<!-- 3 -->Documentation" },
    { message = "^perf", group = "<!-- 4 -->Performance" },
    { message = "^refactor\\(clippy\\)", skip = true },
    { message = "^refactor", group = "<!-- 2 -->Refactor" },
    { message = "^style", group = "<!-- 5 -->Styling" },
    { message = "^test", group = "<!-- 6 -->Testing" },
    { message = "^chore\\(release\\):", skip = true },
    { message = "^chore: release", skip = true },
    { message = "^chore\\(deps.*\\)", skip = true },
    { message = "^chore\\(pr\\)", skip = true },
    { message = "^chore\\(pull\\)", skip = true },
    { message = "^chore\\(npm\\).*yarn\\.lock", skip = true },
    { message = "^chore|^ci", group = "<!-- 7 -->Miscellaneous Tasks" },
    { body = ".*security", group = "<!-- 8 -->Security" },
    { message = "^revert", group = "<!-- 9 -->Revert" },
]

link_parsers = [
    { pattern = "#(\\d+)", href = "{{ remote.link }}/issues/$1" },
    { pattern = "RFC(\\d+)", text = "ietf-rfc$1", href = "https://datatracker.ietf.org/doc/html/rfc$1" },
]
